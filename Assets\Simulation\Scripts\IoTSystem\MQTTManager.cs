using System;
using System.Collections.Generic;
using System.Text;
using UnityEngine;
using Cysharp.Threading.Tasks;
using System.Threading;
using Best.MQTT;
using Best.MQTT.Packets;
using Best.MQTT.Packets.Builders;

namespace Simulation.IoTSystem
{
    /// <summary>
    /// MQTT连接管理器
    /// 负责MQTT客户端的连接、断线重连、消息发布等功能
    /// </summary>
    public class MQTTManager : MonoBehaviour
    {
        [Header("MQTT连接配置")]
        [SerializeField] private string brokerAddress = "broker.emqx.io";
        [SerializeField] private int brokerPort = 1883;
        [SerializeField] private string clientId = "simulation_system";
        [SerializeField] private string username = "";
        [SerializeField] private string password = "";
        [SerializeField] private bool useSSL = false;
        [SerializeField] private int keepAliveInterval = 60;
        [SerializeField] private int connectionTimeout = 30;
        [SerializeField] private SupportedTransports transport = SupportedTransports.TCP;
        [SerializeField] private string websocketPath = "/mqtt";
        
        [Header("重连配置")]
        [SerializeField] private bool autoReconnect = true;
        [SerializeField] private int reconnectDelay = 5;
        [SerializeField] private int maxReconnectAttempts = 10;
        
        [Header("主题配置")]
        [SerializeField] private string topicPrefix = "simulation";
        [SerializeField] private int qosLevel = 1;
        [SerializeField] private bool retainMessages = false;
        
        [Header("调试配置")]
        [SerializeField] private bool enableDebugLog = true;
        [SerializeField] private bool logPublishedMessages = false;
        
        // 连接状态
        private bool isConnected = false;
        private bool isConnecting = false;
        private int reconnectAttempts = 0;
        private CancellationTokenSource cancellationTokenSource;
        
        // 消息队列
        private readonly Queue<MQTTMessage> messageQueue = new Queue<MQTTMessage>();
        private readonly object queueLock = new object();
        
        // BestMQTT客户端实例
        private MQTTClient mqttClient;
        
        // 公共属性
        public bool IsConnected => isConnected;
        public bool IsConnecting => isConnecting;
        public string BrokerAddress => brokerAddress;
        public int BrokerPort => brokerPort;
        public string ClientId => clientId;
        public int QueuedMessageCount => messageQueue.Count;
        
        // 事件
        public event Action OnConnected;
        public event Action OnDisconnected;
        public event Action<string> OnConnectionError;
        public event Action<string, string> OnMessageReceived;
        public event Action<string> OnMessagePublished;
        
        private void Awake()
        {
            // 如果没有设置客户端ID，自动生成
            if (string.IsNullOrEmpty(clientId))
            {
                clientId = $"simulation_{SystemInfo.deviceUniqueIdentifier}_{DateTime.Now.Ticks}";
            }
        }
        
        private void OnDestroy()
        {
            DisconnectAsync().Forget();
        }
        
        /// <summary>
        /// 连接到MQTT代理
        /// </summary>
        public async UniTask<bool> ConnectAsync()
        {
            if (isConnected)
            {
                LogDebug("MQTT已连接");
                return true;
            }

            if (isConnecting)
            {
                LogDebug("MQTT正在连接中");
                return false;
            }

            try
            {
                isConnecting = true;
                cancellationTokenSource = new CancellationTokenSource();

                LogDebug($"开始连接MQTT代理: {brokerAddress}:{brokerPort}");

                // 创建BestMQTT客户端
                await CreateMQTTClient();

                // 开始连接
                var connectResult = await ConnectToMQTTBroker();

                if (connectResult)
                {
                    isConnected = true;
                    isConnecting = false;
                    reconnectAttempts = 0;

                    OnConnected?.Invoke();
                    LogDebug("MQTT连接成功");

                    // 启动消息处理循环
                    MessageProcessingLoop(cancellationTokenSource.Token).Forget();

                    return true;
                }
                else
                {
                    isConnecting = false;
                    LogError("MQTT连接失败");

                    // 自动重连
                    if (autoReconnect && reconnectAttempts < maxReconnectAttempts)
                    {
                        AutoReconnectAsync().Forget();
                    }

                    return false;
                }
            }
            catch (Exception ex)
            {
                isConnecting = false;
                LogError($"MQTT连接异常: {ex.Message}");
                OnConnectionError?.Invoke(ex.Message);

                // 自动重连
                if (autoReconnect && reconnectAttempts < maxReconnectAttempts)
                {
                    AutoReconnectAsync().Forget();
                }

                return false;
            }
        }
        
        /// <summary>
        /// 断开MQTT连接
        /// </summary>
        public async UniTaskVoid DisconnectAsync()
        {
            if (!isConnected && !isConnecting) return;

            try
            {
                LogDebug("断开MQTT连接");

                cancellationTokenSource?.Cancel();

                // 使用BestMQTT断开连接
                if (mqttClient != null)
                {
                    mqttClient.CreateDisconnectPacketBuilder()
                        .WithReasonCode(DisconnectReasonCodes.NormalDisconnection)
                        .WithReasonString("正常断开连接")
                        .BeginDisconnect();

                    // 等待断开完成
                    await UniTask.Delay(500);

                    mqttClient = null;
                }

                isConnected = false;
                isConnecting = false;

                OnDisconnected?.Invoke();
                LogDebug("MQTT连接已断开");
            }
            catch (Exception ex)
            {
                LogError($"MQTT断开异常: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 发布消息到MQTT主题
        /// </summary>
        public bool PublishMessage(string topic, object data, bool useQueue = true)
        {
            if (data == null)
            {
                LogError("尝试发布空数据");
                return false;
            }
            
            try
            {
                string jsonData = JsonUtility.ToJson(data);
                return PublishMessage(topic, jsonData, useQueue);
            }
            catch (Exception ex)
            {
                LogError($"序列化数据失败: {ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// 发布字符串消息到MQTT主题
        /// </summary>
        public bool PublishMessage(string topic, string message, bool useQueue = true)
        {
            if (string.IsNullOrEmpty(topic))
            {
                LogError("主题不能为空");
                return false;
            }
            
            if (string.IsNullOrEmpty(message))
            {
                LogError("消息内容不能为空");
                return false;
            }
            
            var mqttMessage = new MQTTMessage
            {
                Topic = $"{topicPrefix}/{topic}",
                Payload = message,
                QoS = qosLevel,
                Retain = retainMessages,
                Timestamp = DateTime.UtcNow
            };
            
            if (useQueue || !isConnected)
            {
                // 加入队列
                lock (queueLock)
                {
                    messageQueue.Enqueue(mqttMessage);
                }
                LogDebug($"消息已加入队列: {mqttMessage.Topic}");
                return true;
            }
            else
            {
                // 直接发送
                return PublishMessageDirectly(mqttMessage);
            }
        }
        
        /// <summary>
        /// 直接发布消息（不使用队列）
        /// </summary>
        private bool PublishMessageDirectly(MQTTMessage message)
        {
            try
            {
                if (mqttClient == null || !isConnected)
                {
                    LogError("MQTT客户端未连接，无法发布消息");
                    return false;
                }

                // 使用BestMQTT发布消息
                var qosLevel = (QoSLevels)message.QoS;

                mqttClient.CreateApplicationMessageBuilder(message.Topic)
                    .WithPayload(message.Payload)
                    .WithQoS(qosLevel)
                    .WithRetain(message.Retain)
                    .WithContentType("application/json; charset=UTF-8")
                    .BeginPublish();

                if (logPublishedMessages)
                {
                    LogDebug($"发布消息到 {message.Topic}: {message.Payload}");
                }

                OnMessagePublished?.Invoke(message.Topic);
                return true;
            }
            catch (Exception ex)
            {
                LogError($"发布消息失败: {ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// 订阅MQTT主题
        /// </summary>
        public bool SubscribeToTopic(string topic)
        {
            if (!isConnected || mqttClient == null)
            {
                LogError("MQTT未连接，无法订阅主题");
                return false;
            }

            try
            {
                string fullTopic = $"{topicPrefix}/{topic}";

                // 使用BestMQTT订阅主题
                mqttClient.CreateSubscriptionBuilder(fullTopic)
                    .WithMessageCallback(OnMQTTMessageReceived)
                    .WithAcknowledgementCallback(OnSubscriptionAcknowledged)
                    .WithMaximumQoS((QoSLevels)qosLevel)
                    .BeginSubscribe();

                LogDebug($"订阅主题: {fullTopic}");
                return true;
            }
            catch (Exception ex)
            {
                LogError($"订阅主题失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 取消订阅MQTT主题
        /// </summary>
        public bool UnsubscribeFromTopic(string topic)
        {
            if (!isConnected || mqttClient == null)
            {
                LogError("MQTT未连接，无法取消订阅");
                return false;
            }

            try
            {
                string fullTopic = $"{topicPrefix}/{topic}";

                // 使用BestMQTT取消订阅
                mqttClient.CreateUnsubscribePacketBuilder(fullTopic)
                    .WithAcknowledgementCallback(OnUnsubscribeAcknowledged)
                    .BeginUnsubscribe();

                LogDebug($"取消订阅主题: {fullTopic}");
                return true;
            }
            catch (Exception ex)
            {
                LogError($"取消订阅失败: {ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// 自动重连
        /// </summary>
        private async UniTaskVoid AutoReconnectAsync()
        {
            reconnectAttempts++;
            LogDebug($"尝试自动重连 ({reconnectAttempts}/{maxReconnectAttempts})");
            
            await UniTask.Delay(TimeSpan.FromSeconds(reconnectDelay));
            
            if (reconnectAttempts <= maxReconnectAttempts)
            {
                await ConnectAsync();
            }
            else
            {
                LogError("达到最大重连次数，停止重连");
            }
        }
        
        /// <summary>
        /// 消息处理循环
        /// </summary>
        private async UniTaskVoid MessageProcessingLoop(CancellationToken cancellationToken)
        {
            try
            {
                while (!cancellationToken.IsCancellationRequested && isConnected)
                {
                    await ProcessQueuedMessages();
                    await UniTask.Delay(100, cancellationToken: cancellationToken);
                }
            }
            catch (OperationCanceledException)
            {
                LogDebug("消息处理循环被取消");
            }
            catch (Exception ex)
            {
                LogError($"消息处理循环异常: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 处理队列中的消息
        /// </summary>
        private async UniTask ProcessQueuedMessages()
        {
            var messagesToProcess = new List<MQTTMessage>();
            
            lock (queueLock)
            {
                while (messageQueue.Count > 0)
                {
                    messagesToProcess.Add(messageQueue.Dequeue());
                }
            }
            
            foreach (var message in messagesToProcess)
            {
                PublishMessageDirectly(message);
                await UniTask.Yield();
            }
        }
        
        /// <summary>
        /// 创建MQTT客户端
        /// </summary>
        private async UniTask CreateMQTTClient()
        {
            try
            {
                // 创建连接选项
                var connectionOptions = new ConnectionOptionsBuilder();

                if (transport == SupportedTransports.TCP)
                {
                    connectionOptions.WithTCP(brokerAddress, brokerPort);
                }
                else if (transport == SupportedTransports.WebSocket)
                {
                    connectionOptions.WithWebSocket(brokerAddress, brokerPort).WithPath(websocketPath);
                }

                if (useSSL)
                {
                    connectionOptions.WithTLS();
                }

                // 创建MQTT客户端
                mqttClient = new MQTTClientBuilder()
                    .WithOptions(connectionOptions)
                    .WithEventHandler(OnMQTTConnected)
                    .WithEventHandler(OnMQTTDisconnected)
                    .WithEventHandler(OnMQTTStateChanged)
                    .WithEventHandler(OnMQTTError)
                    .CreateClient();

                LogDebug("MQTT客户端创建成功");
                await UniTask.Yield();
            }
            catch (Exception ex)
            {
                LogError($"创建MQTT客户端失败: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 连接到MQTT代理
        /// </summary>
        private async UniTask<bool> ConnectToMQTTBroker()
        {
            try
            {
                var connectTaskSource = new UniTaskCompletionSource<bool>();
                bool connectionCompleted = false;

                // 设置连接回调
                void OnConnectedCallback(MQTTClient client)
                {
                    if (!connectionCompleted)
                    {
                        connectionCompleted = true;
                        connectTaskSource.TrySetResult(true);
                    }
                }

                void OnErrorCallback(MQTTClient client, string reason)
                {
                    if (!connectionCompleted)
                    {
                        connectionCompleted = true;
                        connectTaskSource.TrySetResult(false);
                    }
                }

                // 临时添加事件处理器
                mqttClient.OnConnected += OnConnectedCallback;
                mqttClient.OnError += OnErrorCallback;

                // 开始连接
                mqttClient.BeginConnect(ConnectPacketBuilderCallback);

                // 等待连接结果或超时
                var timeoutTask = UniTask.Delay(TimeSpan.FromSeconds(connectionTimeout));
                var connectTask = connectTaskSource.Task;

                var result = await UniTask.WhenAny(connectTask, timeoutTask);

                // 移除临时事件处理器
                mqttClient.OnConnected -= OnConnectedCallback;
                mqttClient.OnError -= OnErrorCallback;

                if (result == 0)
                {
                    return await connectTask;
                }
                else
                {
                    LogError("MQTT连接超时");
                    return false;
                }
            }
            catch (Exception ex)
            {
                LogError($"连接MQTT代理失败: {ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// 调试日志
        /// </summary>
        private void LogDebug(string message)
        {
            if (enableDebugLog)
            {
                Debug.Log($"[MQTTManager] {message}");
            }
        }
        
        /// <summary>
        /// 错误日志
        /// </summary>
        private void LogError(string message)
        {
            Debug.LogError($"[MQTTManager] {message}");
        }

        #region BestMQTT事件处理

        /// <summary>
        /// 连接包构建回调
        /// </summary>
        private ConnectPacketBuilder ConnectPacketBuilderCallback(MQTTClient client, ConnectPacketBuilder builder)
        {
            // 设置客户端ID或会话
            if (!string.IsNullOrEmpty(clientId))
            {
                builder.WithClientID(clientId);
            }
            else
            {
                // 使用默认会话
                var session = SessionHelper.Get(client.Options.Host);
                builder.WithSession(session);
            }

            // 设置用户名和密码
            if (!string.IsNullOrEmpty(username))
            {
                builder.WithUserName(username);
            }

            if (!string.IsNullOrEmpty(password))
            {
                builder.WithPassword(password);
            }

            // 设置保活间隔
            builder.WithKeepAlive((ushort)keepAliveInterval);

            LogDebug($"配置连接包 - ClientId: {clientId}, KeepAlive: {keepAliveInterval}s");

            return builder;
        }

        /// <summary>
        /// MQTT连接成功事件
        /// </summary>
        private void OnMQTTConnected(MQTTClient client)
        {
            LogDebug("BestMQTT连接成功");
        }

        /// <summary>
        /// MQTT断开连接事件
        /// </summary>
        private void OnMQTTDisconnected(MQTTClient client, DisconnectReasonCodes code, string reason)
        {
            LogDebug($"BestMQTT断开连接 - 代码: {code}, 原因: {reason}");

            isConnected = false;
            isConnecting = false;

            OnDisconnected?.Invoke();

            // 如果不是正常断开，尝试重连
            if (code != DisconnectReasonCodes.NormalDisconnection && autoReconnect && reconnectAttempts < maxReconnectAttempts)
            {
                AutoReconnectAsync().Forget();
            }
        }

        /// <summary>
        /// MQTT状态变化事件
        /// </summary>
        private void OnMQTTStateChanged(MQTTClient client, ClientStates oldState, ClientStates newState)
        {
            LogDebug($"MQTT状态变化: {oldState} => {newState}");
        }

        /// <summary>
        /// MQTT错误事件
        /// </summary>
        private void OnMQTTError(MQTTClient client, string reason)
        {
            LogError($"MQTT错误: {reason}");
            OnConnectionError?.Invoke(reason);
        }

        /// <summary>
        /// 接收到MQTT消息
        /// </summary>
        private void OnMQTTMessageReceived(MQTTClient client, SubscriptionTopic topic, string topicName, ApplicationMessage message)
        {
            try
            {
                // 转换消息载荷为字符串
                var payload = Encoding.UTF8.GetString(message.Payload.Data, message.Payload.Offset, message.Payload.Count);

                LogDebug($"收到消息 - 主题: {topicName}, 内容: {payload}");

                OnMessageReceived?.Invoke(topicName, payload);
            }
            catch (Exception ex)
            {
                LogError($"处理接收消息失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 订阅确认回调
        /// </summary>
        private void OnSubscriptionAcknowledged(MQTTClient client, SubscriptionTopic topic, SubscribeAckReasonCodes reasonCode)
        {
            if (reasonCode <= SubscribeAckReasonCodes.GrantedQoS2)
            {
                LogDebug($"订阅成功 - 主题: {topic.Filter.OriginalFilter}, QoS: {reasonCode}");
            }
            else
            {
                LogError($"订阅失败 - 主题: {topic.Filter.OriginalFilter}, 错误: {reasonCode}");
            }
        }

        /// <summary>
        /// 取消订阅确认回调
        /// </summary>
        private void OnUnsubscribeAcknowledged(MQTTClient client, string topicFilter, UnsubscribeAckReasonCodes reasonCode)
        {
            if (reasonCode == UnsubscribeAckReasonCodes.Success)
            {
                LogDebug($"取消订阅成功 - 主题: {topicFilter}");
            }
            else
            {
                LogError($"取消订阅失败 - 主题: {topicFilter}, 错误: {reasonCode}");
            }
        }

        #endregion
    }
    
    /// <summary>
    /// MQTT消息结构
    /// </summary>
    [Serializable]
    public class MQTTMessage
    {
        public string Topic;
        public string Payload;
        public int QoS;
        public bool Retain;
        public DateTime Timestamp;
    }
}
