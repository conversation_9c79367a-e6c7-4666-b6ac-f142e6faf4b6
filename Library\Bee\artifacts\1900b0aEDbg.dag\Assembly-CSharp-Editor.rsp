-target:library
-out:"Library/Bee/artifacts/1900b0aEDbg.dag/Assembly-CSharp-Editor.dll"
-refout:"Library/Bee/artifacts/1900b0aEDbg.dag/Assembly-CSharp-Editor.ref.dll"
-define:UNITY_6000_1_7
-define:UNITY_6000_1
-define:UNITY_6000
-define:UNITY_5_3_OR_NEWER
-define:UNITY_5_4_OR_NEWER
-define:UNITY_5_5_OR_NEWER
-define:UNITY_5_6_OR_NEWER
-define:UNITY_2017_1_OR_NEWER
-define:UNITY_2017_2_OR_NEWER
-define:UNITY_2017_3_OR_NEWER
-define:UNITY_2017_4_OR_NEWER
-define:UNITY_2018_1_OR_NEWER
-define:UNITY_2018_2_OR_NEWER
-define:UNITY_2018_3_OR_NEWER
-define:UNITY_2018_4_OR_NEWER
-define:UNITY_2019_1_OR_NEWER
-define:UNITY_2019_2_OR_NEWER
-define:UNITY_2019_3_OR_NEWER
-define:UNITY_2019_4_OR_NEWER
-define:UNITY_2020_1_OR_NEWER
-define:UNITY_2020_2_OR_NEWER
-define:UNITY_2020_3_OR_NEWER
-define:UNITY_2021_1_OR_NEWER
-define:UNITY_2021_2_OR_NEWER
-define:UNITY_2021_3_OR_NEWER
-define:UNITY_2022_1_OR_NEWER
-define:UNITY_2022_2_OR_NEWER
-define:UNITY_2022_3_OR_NEWER
-define:UNITY_2023_1_OR_NEWER
-define:UNITY_2023_2_OR_NEWER
-define:UNITY_2023_3_OR_NEWER
-define:UNITY_6000_0_OR_NEWER
-define:UNITY_6000_1_OR_NEWER
-define:PLATFORM_ARCH_64
-define:UNITY_64
-define:UNITY_INCLUDE_TESTS
-define:ENABLE_AR
-define:ENABLE_AUDIO
-define:ENABLE_CACHING
-define:ENABLE_CLOTH
-define:ENABLE_EVENT_QUEUE
-define:ENABLE_MICROPHONE
-define:ENABLE_MULTIPLE_DISPLAYS
-define:ENABLE_PHYSICS
-define:ENABLE_TEXTURE_STREAMING
-define:ENABLE_VIRTUALTEXTURING
-define:ENABLE_LZMA
-define:ENABLE_UNITYEVENTS
-define:ENABLE_VR
-define:ENABLE_WEBCAM
-define:ENABLE_UNITYWEBREQUEST
-define:ENABLE_WWW
-define:ENABLE_CLOUD_SERVICES
-define:ENABLE_CLOUD_SERVICES_ADS
-define:ENABLE_CLOUD_SERVICES_USE_WEBREQUEST
-define:ENABLE_CLOUD_SERVICES_CRASH_REPORTING
-define:ENABLE_CLOUD_SERVICES_PURCHASING
-define:ENABLE_CLOUD_SERVICES_ANALYTICS
-define:ENABLE_CLOUD_SERVICES_BUILD
-define:ENABLE_EDITOR_GAME_SERVICES
-define:ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT
-define:ENABLE_CLOUD_LICENSE
-define:ENABLE_EDITOR_HUB_LICENSE
-define:ENABLE_WEBSOCKET_CLIENT
-define:ENABLE_GENERATE_NATIVE_PLUGINS_FOR_ASSEMBLIES_API
-define:ENABLE_DIRECTOR_AUDIO
-define:ENABLE_DIRECTOR_TEXTURE
-define:ENABLE_MANAGED_JOBS
-define:ENABLE_MANAGED_TRANSFORM_JOBS
-define:ENABLE_MANAGED_ANIMATION_JOBS
-define:ENABLE_MANAGED_AUDIO_JOBS
-define:ENABLE_MANAGED_UNITYTLS
-define:INCLUDE_DYNAMIC_GI
-define:ENABLE_SCRIPTING_GC_WBARRIERS
-define:PLATFORM_SUPPORTS_MONO
-define:RENDER_SOFTWARE_CURSOR
-define:ENABLE_MARSHALLING_TESTS
-define:ENABLE_VIDEO
-define:ENABLE_NAVIGATION_OFFMESHLINK_TO_NAVMESHLINK
-define:ENABLE_ACCELERATOR_CLIENT_DEBUGGING
-define:TEXTCORE_1_0_OR_NEWER
-define:EDITOR_ONLY_NAVMESH_BUILDER_DEPRECATED
-define:PLATFORM_STANDALONE_WIN
-define:PLATFORM_STANDALONE
-define:UNITY_STANDALONE_WIN
-define:UNITY_STANDALONE
-define:ENABLE_RUNTIME_GI
-define:ENABLE_MOVIES
-define:ENABLE_NETWORK
-define:ENABLE_NVIDIA
-define:ENABLE_AMD
-define:ENABLE_CRUNCH_TEXTURE_COMPRESSION
-define:ENABLE_OUT_OF_PROCESS_CRASH_HANDLER
-define:ENABLE_CLUSTER_SYNC
-define:ENABLE_CLUSTERINPUT
-define:PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP
-define:GFXDEVICE_WAITFOREVENT_MESSAGEPUMP
-define:PLATFORM_USES_EXPLICIT_MEMORY_MANAGER_INITIALIZER
-define:PLATFORM_SUPPORTS_WAIT_FOR_PRESENTATION
-define:PLATFORM_SUPPORTS_SPLIT_GRAPHICS_JOBS
-define:ENABLE_MONO
-define:NET_4_6
-define:NET_UNITY_4_8
-define:ENABLE_PROFILER
-define:DEBUG
-define:TRACE
-define:UNITY_ASSERTIONS
-define:UNITY_EDITOR
-define:UNITY_EDITOR_64
-define:UNITY_EDITOR_WIN
-define:ENABLE_UNITY_COLLECTIONS_CHECKS
-define:ENABLE_BURST_AOT
-define:UNITY_TEAM_LICENSE
-define:ENABLE_CUSTOM_RENDER_TEXTURE
-define:ENABLE_DIRECTOR
-define:ENABLE_LOCALIZATION
-define:ENABLE_SPRITES
-define:ENABLE_TERRAIN
-define:ENABLE_TILEMAP
-define:ENABLE_TIMELINE
-define:ENABLE_INPUT_SYSTEM
-define:ENABLE_LEGACY_INPUT_MANAGER
-define:TEXTCORE_FONT_ENGINE_1_5_OR_NEWER
-define:TEXTCORE_TEXT_ENGINE_1_5_OR_NEWER
-define:DOTWEEN
-define:UNITASK_DOTWEEN_SUPPORT
-define:ENVIRO_3
-define:ENVIRO_URP
-define:CSHARP_7_OR_LATER
-define:CSHARP_7_3_OR_NEWER
-define:UNITY_EDITOR_ONLY_COMPILATION
-r:"Assets/Battlehub/Protobuf.Net/protobuf-net.dll"
-r:"Assets/Battlehub/RTEditor/ThirdParty/ICSharpCode.SharpZipLib.dll"
-r:"Assets/Battlehub/RTEditor/ThirdParty/UnityWeld/UnityWeld.dll"
-r:"Assets/Battlehub/RTScripting.Jint/ThirdParty/Jint/Acornima.dll"
-r:"Assets/Battlehub/RTScripting.Jint/ThirdParty/Jint/Jint.dll"
-r:"Assets/Battlehub/StorageData/Generated/StorageTypeModel.dll"
-r:"Assets/Plugins/DOTween/DOTween.dll"
-r:"Assets/Plugins/DOTween/Editor/DOTweenEditor.dll"
-r:"Assets/TriLib/TriLibCore/Plugins/Debug/TriLibCore.Dae.dll"
-r:"Assets/TriLib/TriLibCore/Plugins/Debug/TriLibCore.dll"
-r:"Assets/TriLib/TriLibCore/Plugins/Debug/TriLibCore.Fbx.dll"
-r:"Assets/TriLib/TriLibCore/Plugins/Debug/TriLibCore.Gltf.dll"
-r:"Assets/TriLib/TriLibCore/Plugins/Debug/TriLibCore.Gltf.Draco.dll"
-r:"Assets/TriLib/TriLibCore/Plugins/Debug/TriLibCore.HDRLoader.dll"
-r:"Assets/TriLib/TriLibCore/Plugins/Debug/TriLibCore.Obj.dll"
-r:"Assets/TriLib/TriLibCore/Plugins/Debug/TriLibCore.Ply.dll"
-r:"Assets/TriLib/TriLibCore/Plugins/Debug/TriLibCore.Stl.dll"
-r:"Assets/TriLib/TriLibCore/Plugins/Debug/TriLibCore.Textures.dll"
-r:"Assets/TriLib/TriLibCore/Plugins/Debug/TriLibCore.ThreeMf.dll"
-r:"Assets/TriLib/TriLibCore/Plugins/Dependencies/IxMilia.ThreeMf.dll"
-r:"Assets/TriLib/TriLibCore/Plugins/Dependencies/LibTessDotNet.dll"
-r:"Assets/TriLib/TriLibCore/Plugins/Dependencies/SafeStbImageSharp.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEditor.Graphs.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEditor.AccessibilityModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEditor.AdaptivePerformanceModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEditor.BuildProfileModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEditor.CoreBusinessMetricsModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEditor.CoreModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEditor.DeviceSimulatorModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEditor.DiagnosticsModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEditor.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEditor.EditorToolbarModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEditor.EmbreeModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEditor.GIModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEditor.GraphicsStateCollectionSerializerModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEditor.GraphViewModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEditor.GridAndSnapModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEditor.GridModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEditor.MultiplayerModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEditor.Physics2DModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEditor.PhysicsModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEditor.PresetsUIModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEditor.PropertiesModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEditor.QuickSearchModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEditor.SafeModeModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEditor.SceneTemplateModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEditor.SceneViewModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEditor.ShaderFoundryModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEditor.SketchUpModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEditor.SpriteMaskModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEditor.SpriteShapeModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEditor.SubstanceModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEditor.TerrainModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEditor.TextCoreFontEngineModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEditor.TextCoreTextEngineModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEditor.TextRenderingModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEditor.TilemapModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEditor.TreeModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIAutomationModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIBuilderModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsSamplesModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEditor.UmbraModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEditor.UnityConnectModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEditor.VFXModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEditor.VideoModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEditor.XRModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEngine.AccessibilityModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEngine.AIModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEngine.AMDModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEngine.AndroidJNIModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEngine.AnimationModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEngine.ARModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEngine.AssetBundleModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEngine.AudioModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClothModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClusterInputModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClusterRendererModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEngine.ContentLoadModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEngine.CoreModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEngine.CrashReportingModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEngine.DirectorModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEngine.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEngine.DSPGraphModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEngine.GameCenterModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEngine.GIModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEngine.GraphicsStateCollectionSerializerModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEngine.GridModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEngine.HierarchyCoreModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEngine.HotReloadModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEngine.ImageConversionModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEngine.IMGUIModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputForUIModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputLegacyModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEngine.JSONSerializeModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEngine.LocalizationModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEngine.MarshallingModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEngine.MultiplayerModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEngine.NVIDIAModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEngine.ParticleSystemModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEngine.PerformanceReportingModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEngine.Physics2DModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEngine.PhysicsModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEngine.PropertiesModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEngine.ScreenCaptureModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEngine.ShaderVariantAnalyticsModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEngine.SharedInternalsModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteMaskModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteShapeModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEngine.StreamingModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEngine.SubstanceModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEngine.SubsystemsModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainPhysicsModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextCoreFontEngineModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextCoreTextEngineModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextRenderingModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEngine.TilemapModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEngine.TLSModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEngine.UIElementsModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEngine.UIModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEngine.UmbraModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsCommonModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityConnectModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityCurlModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityTestProtocolModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAssetBundleModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAudioModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestTextureModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestWWWModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEngine.VehiclesModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEngine.VFXModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEngine.VideoModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEngine.VirtualTexturingModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEngine.VRModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEngine.WindModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/Managed/UnityEngine/UnityEngine.XRModule.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/Microsoft.Win32.Primitives.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/netstandard.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.AppContext.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Buffers.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.Concurrent.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.NonGeneric.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.Specialized.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.Annotations.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.EventBasedAsync.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.Primitives.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.TypeConverter.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Console.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Data.Common.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Contracts.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Debug.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.FileVersionInfo.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Process.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.StackTrace.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.TextWriterTraceListener.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Tools.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.TraceSource.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Drawing.Primitives.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Dynamic.Runtime.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Globalization.Calendars.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Globalization.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Globalization.Extensions.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.Compression.ZipFile.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.DriveInfo.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.Primitives.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.Watcher.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.IsolatedStorage.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.MemoryMappedFiles.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.Pipes.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.UnmanagedMemoryStream.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.Expressions.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.Parallel.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.Queryable.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Memory.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Http.Rtc.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.NameResolution.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.NetworkInformation.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Ping.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Primitives.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Requests.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Security.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Sockets.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.WebHeaderCollection.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.WebSockets.Client.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.WebSockets.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ObjectModel.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.DispatchProxy.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Emit.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Emit.ILGeneration.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Emit.Lightweight.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Extensions.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Primitives.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Resources.Reader.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Resources.ResourceManager.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Resources.Writer.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.CompilerServices.VisualC.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Extensions.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Handles.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.InteropServices.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.InteropServices.RuntimeInformation.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.InteropServices.WindowsRuntime.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Numerics.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Formatters.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Json.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Primitives.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Xml.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Claims.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Algorithms.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Csp.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Encoding.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Primitives.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.X509Certificates.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Principal.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.SecureString.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Duplex.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Http.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.NetTcp.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Primitives.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Security.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Text.Encoding.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Text.Encoding.Extensions.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Text.RegularExpressions.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Overlapped.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Tasks.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Tasks.Extensions.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Tasks.Parallel.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Thread.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.ThreadPool.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Timer.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ValueTuple.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.ReaderWriter.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XDocument.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XmlDocument.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XmlSerializer.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XPath.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XPath.XDocument.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Microsoft.CSharp.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/mscorlib.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.ComponentModel.Composition.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Core.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Data.DataSetExtensions.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Data.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Drawing.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.IO.Compression.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.IO.Compression.FileSystem.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Net.Http.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Numerics.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Numerics.Vectors.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Runtime.Serialization.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Transactions.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Xml.dll"
-r:"E:/Unity/6000.1.7f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Xml.Linq.dll"
-r:"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Lib/Editor/log4netPlastic.dll"
-r:"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Lib/Editor/Unity.Plastic.Antlr3.Runtime.dll"
-r:"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Lib/Editor/Unity.Plastic.Newtonsoft.Json.dll"
-r:"Library/PackageCache/com.unity.collab-proxy@c854d1f7d97f/Lib/Editor/unityplastic.dll"
-r:"Library/PackageCache/com.unity.collections@56bff8827a7e/Unity.Collections.LowLevel.ILSupport/Unity.Collections.LowLevel.ILSupport.dll"
-r:"Library/PackageCache/com.unity.ext.nunit@031a54704bff/net40/unity-custom/nunit.framework.dll"
-r:"Library/PackageCache/com.unity.ext.nunit@031a54704bff/net40/unity-custom/nunit.framework.dll"
-r:"Library/PackageCache/com.unity.nuget.mono-cecil@d6f9955a5d5f/Mono.Cecil.dll"
-r:"Library/PackageCache/com.unity.nuget.newtonsoft-json@74deb55db2a0/Runtime/Newtonsoft.Json.dll"
-r:"Library/PackageCache/com.unity.sharp-zip-lib@6b61f82b0cb3/Runtime/Unity.SharpZipLib.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Assembly-CSharp.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Autodesk.Fbx.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Autodesk.Fbx.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Battlehub.LoadImageAsync.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Battlehub.RTEditor.Demo.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Battlehub.RTEditor.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Battlehub.RTEditor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Battlehub.RTEditor.URP.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Battlehub.RTEditor.URP.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Battlehub.RTExtensions.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Battlehub.RTExtensions.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Battlehub.RTExtensions.URP.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Battlehub.RTExtensions.URP.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Battlehub.RTImporter.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Battlehub.RTScripting.Common.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Battlehub.RTScripting.Jint.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Battlehub.RTScripting.Jint.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Battlehub.RTScripting.Roslyn.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Battlehub.RTScripting.Roslyn.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Battlehub.Storage.Addressables.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Battlehub.Storage.Core.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Battlehub.Storage.Core.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Battlehub.Storage.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Battlehub.Storage.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Battlehub.Storage.ShaderUtil.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Battlehub.Storage.ShaderUtil.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/CodeAnalysis.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/com.Tivadar.Best.HTTP.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/com.Tivadar.Best.MQTT.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/com.Tivadar.Best.TLSSecurity.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/com.Tivadar.Best.WebSockets.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/DOTween.Modules.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Draco.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Draco.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Enviro3.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Enviro3.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/glTFast.Animation.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/glTFast.dots.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/glTFast.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/glTFast.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/HSVPicker.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/IngameDebugConsole.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/IngameDebugConsole.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Ktx.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/PPv2URPConverters.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/PsdPlugin.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Simulation.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/TriLib.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/TriLib.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/UIShapesKit.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/UIShapesKit.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/UMP.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/UniTask.Addressables.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/UniTask.DOTween.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/UniTask.Linq.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/UniTask.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/UniTask.TextMeshPro.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.2D.Common.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.2D.Common.Path.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.2D.Common.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.2D.Psdimporter.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.2D.Sprite.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.2D.Tilemap.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Addressables.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Addressables.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AI.Navigation.Editor.ConversionSystem.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AI.Navigation.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AI.Navigation.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AI.Navigation.Updater.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Burst.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Burst.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Collections.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Collections.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Formats.Fbx.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Formats.Fbx.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.InputSystem.ForUI.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.InputSystem.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.InternalAPIEditorBridge.001.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.InternalAPIEngineBridge.001.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Mathematics.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Mathematics.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Multiplayer.Center.Common.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Multiplayer.Center.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.PlasticSCM.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.ProBuilder.AddOns.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.ProBuilder.AssetIdRemapUtility.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.ProBuilder.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.ProBuilder.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Profiling.Core.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Rendering.LightTransport.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Rendering.LightTransport.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipeline.Universal.ShaderLibrary.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.Shared.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.Shared.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Core.ShaderLibrary.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.GPUDriven.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.2D.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.Config.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.Shaders.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.ResourceManager.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Rider.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.ScriptableBuildPipeline.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.ScriptableBuildPipeline.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Searcher.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Settings.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.ShaderGraph.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.SharpZipLib.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.SharpZipLib.Utils.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TextMeshPro.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TextMeshPro.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Timeline.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Timeline.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualEffectGraph.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualEffectGraph.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualStudio.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/UnityEditor.TestRunner.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/UnityEditor.UI.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/UnityEngine.TestRunner.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/UnityEngine.UI.ref.dll"
-analyzer:"E:/Unity/6000.1.7f1/Editor/Data/Tools/Unity.SourceGenerators/Unity.Properties.SourceGenerator.dll"
-analyzer:"E:/Unity/6000.1.7f1/Editor/Data/Tools/Unity.SourceGenerators/Unity.SourceGenerators.dll"
-analyzer:"E:/Unity/6000.1.7f1/Editor/Data/Tools/Unity.SourceGenerators/Unity.UIToolkit.SourceGenerator.dll"
"Assets/Battlehub/RTEditorDemo/Editor/RTEditor/RTEditor/RuntimeEditorMenu.cs"
"Assets/Battlehub/RTEditorDemo/Editor/RTEditor/RTHandles/RTHandlesMenu.cs"
"Assets/Battlehub/RTEditorDemo/Editor/Utils/BHRoot.cs"
"Assets/Enviro 3 - Sky and Weather/Scripts/ThirdPartySupport/WAPI/Editor/EnviroWAPIEditor.cs"
"Assets/GeneralEditor/Scripts/Editor/EditorHelper.cs"
"Assets/GeneralEditor/Scripts/Editor/PrefabCollectorEditorWindow.cs"
"Assets/GeneralEditor/Scripts/Editor/PrefabNameCleanerWindow.cs"
"Assets/GeneralEditor/Scripts/Editor/PrefabReplacerWindow.cs"
"Assets/GeneralEditor/Scripts/Editor/ThemeImportWindow.cs"
"Assets/UniversalMediaPlayer/Editor/UMPEditor.cs"
"Assets/UniversalMediaPlayer/Editor/UMPPostAssets.cs"
"Assets/UniversalMediaPlayer/Editor/UMPPostBuilds.cs"
"Assets/UniversalMediaPlayer/Editor/UMPSettingsEditor.cs"
"Assets/UniversalMediaPlayer/Editor/UMPSettingsWindow.cs"
-langversion:9.0
/deterministic
/optimize-
/debug:portable
/nologo
/RuntimeMetadataVersion:v4.0.30319
/nowarn:0169
/nowarn:0649
/nowarn:0282
/nowarn:1701
/nowarn:1702
/utf8output
/preferreduilang:en-US
/additionalfile:"Library/Bee/artifacts/1900b0aEDbg.dag/Assembly-CSharp-Editor.UnityAdditionalFile.txt"