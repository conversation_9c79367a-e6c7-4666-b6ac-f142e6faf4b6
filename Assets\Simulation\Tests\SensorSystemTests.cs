using System.Collections;
using UnityEngine;
using UnityEngine.TestTools;
using NUnit.Framework;
using Simulation.Sensors;
using Simulation.Data;
using Simulation.IoT;

namespace Simulation.Tests
{
    /// <summary>
    /// 传感器系统单元测试
    /// </summary>
    public class SensorSystemTests : TestBase
    {
        private TestSensor testSensor;
        private Transform targetTransform;
        
        [SetUp]
        public override void SetUp()
        {
            base.SetUp();
            
            // 创建测试传感器
            testSensor = CreateTestComponent<TestSensor>();
            
            // 创建目标Transform
            targetTransform = CreateTestTransform(new Vector3(1, 2, 3), Quaternion.Euler(45, 90, 0));
        }
        
        /// <summary>
        /// 测试传感器基础配置
        /// </summary>
        [Test]
        public void TestSensorBasicConfiguration()
        {
            // 配置传感器
            string testSensorId = CreateMockSensorId("config_test");
            testSensor.SetSensorConfiguration(testSensorId, "device_001", 1.0f);
            
            Assert.AreEqual(testSensorId, testSensor.GetSensorId(), "传感器ID应该匹配");
            Assert.AreEqual("device_001", testSensor.GetDeviceId(), "设备ID应该匹配");
            Assert.AreEqual(1.0f, testSensor.GetDataCollectionInterval(), "数据收集间隔应该匹配");
        }
        
        /// <summary>
        /// 测试传感器启动和停止
        /// </summary>
        [UnityTest]
        public IEnumerator TestSensorStartStop()
        {
            string testSensorId = CreateMockSensorId("start_stop_test");
            testSensor.SetSensorConfiguration(testSensorId, "device_001", 0.1f);
            
            // 启动传感器
            testSensor.StartDataCollection();
            yield return new WaitForSeconds(0.05f);
            
            Assert.IsTrue(testSensor.IsCollecting(), "传感器应该正在收集数据");
            
            // 停止传感器
            testSensor.StopDataCollection();
            yield return new WaitForSeconds(0.05f);
            
            Assert.IsFalse(testSensor.IsCollecting(), "传感器应该已停止收集数据");
        }
        
        /// <summary>
        /// 测试数据收集间隔
        /// </summary>
        [UnityTest]
        public IEnumerator TestDataCollectionInterval()
        {
            string testSensorId = CreateMockSensorId("interval_test");
            float interval = 0.2f;
            testSensor.SetSensorConfiguration(testSensorId, "device_001", interval);
            
            int initialDataCount = testSensor.GetDataCollectionCount();
            
            // 启动数据收集
            testSensor.StartDataCollection();
            
            // 等待一个间隔周期
            yield return new WaitForSeconds(interval + 0.1f);
            
            int afterOneInterval = testSensor.GetDataCollectionCount();
            Assert.Greater(afterOneInterval, initialDataCount, "应该收集到新数据");
            
            // 等待另一个间隔周期
            yield return new WaitForSeconds(interval);
            
            int afterTwoIntervals = testSensor.GetDataCollectionCount();
            Assert.Greater(afterTwoIntervals, afterOneInterval, "应该收集到更多数据");
            
            testSensor.StopDataCollection();
        }
        
        /// <summary>
        /// 测试位置传感器数据收集
        /// </summary>
        [Test]
        public void TestPositionSensorDataCollection()
        {
            var positionSensor = CreateTestComponent<PositionSensor>();
            string sensorId = CreateMockSensorId("position_test");
            
            // 配置位置传感器
            positionSensor.SetSensorConfiguration(sensorId, "device_001", 1.0f);
            positionSensor.SetTargetTransform(targetTransform);
            
            // 手动触发数据收集
            var positionData = positionSensor.CollectPositionData();
            
            Assert.IsNotNull(positionData, "位置数据不应为空");
            Assert.AreEqual(sensorId, positionData.sensorId, "传感器ID应该匹配");
            Assert.AreEqual("position", positionData.sensorType, "传感器类型应该是position");
            
            // 验证位置数据
            AssertVector3Approximately(targetTransform.position, 
                new Vector3(positionData.position.x, positionData.position.y, positionData.position.z), 
                0.01f, "位置数据应该匹配");
        }
        
        /// <summary>
        /// 测试运动传感器速度计算
        /// </summary>
        [UnityTest]
        public IEnumerator TestMotionSensorSpeedCalculation()
        {
            var motionSensor = CreateTestComponent<MotionSensor>();
            string sensorId = CreateMockSensorId("motion_test");
            
            // 配置运动传感器
            motionSensor.SetSensorConfiguration(sensorId, "device_001", 0.1f);
            motionSensor.SetTargetTransform(targetTransform);
            
            // 记录初始位置
            Vector3 initialPosition = targetTransform.position;
            
            // 移动目标
            Vector3 newPosition = initialPosition + Vector3.right * 2f;
            targetTransform.position = newPosition;
            
            yield return new WaitForSeconds(0.1f);
            
            // 收集运动数据
            var motionData = motionSensor.CollectMotionData();
            
            Assert.IsNotNull(motionData, "运动数据不应为空");
            Assert.AreEqual(sensorId, motionData.sensorId, "传感器ID应该匹配");
            Assert.AreEqual("motion", motionData.sensorType, "传感器类型应该是motion");
            Assert.Greater(motionData.velocity.speed, 0f, "速度应该大于0");
        }
        
        /// <summary>
        /// 测试身份传感器数据格式
        /// </summary>
        [Test]
        public void TestIdentitySensorDataFormat()
        {
            var identitySensor = CreateTestComponent<IdentitySensor>();
            string sensorId = CreateMockSensorId("identity_test");
            
            // 配置身份传感器
            identitySensor.SetSensorConfiguration(sensorId, "device_001", 1.0f);
            identitySensor.SetIdentityInfo("张三", "工人", "建筑部", "CARD001");
            
            // 收集身份数据
            var identityData = identitySensor.CollectIdentityData();
            
            Assert.IsNotNull(identityData, "身份数据不应为空");
            Assert.AreEqual(sensorId, identityData.sensorId, "传感器ID应该匹配");
            Assert.AreEqual("identity", identityData.sensorType, "传感器类型应该是identity");
            Assert.AreEqual("张三", identityData.name, "姓名应该匹配");
            Assert.AreEqual("工人", identityData.role, "角色应该匹配");
            Assert.AreEqual("建筑部", identityData.department, "部门应该匹配");
            Assert.AreEqual("CARD001", identityData.cardId, "卡号应该匹配");
        }
        
        /// <summary>
        /// 测试传感器数据事件触发
        /// </summary>
        [UnityTest]
        public IEnumerator TestSensorDataEventTrigger()
        {
            string sensorId = CreateMockSensorId("event_test");
            testSensor.SetSensorConfiguration(sensorId, "device_001", 0.1f);
            
            bool eventTriggered = false;
            object receivedData = null;
            
            // 订阅数据生成事件
            testSensor.OnDataGenerated += (id, data) =>
            {
                if (id == sensorId)
                {
                    eventTriggered = true;
                    receivedData = data;
                }
            };
            
            // 启动数据收集
            testSensor.StartDataCollection();
            
            // 等待事件触发
            yield return new WaitForSeconds(0.2f);
            
            Assert.IsTrue(eventTriggered, "数据生成事件应该被触发");
            Assert.IsNotNull(receivedData, "接收到的数据不应为空");
            
            testSensor.StopDataCollection();
        }
        
        /// <summary>
        /// 测试传感器数据JSON序列化
        /// </summary>
        [Test]
        public void TestSensorDataJsonSerialization()
        {
            // 测试位置数据序列化
            var positionData = new PositionData(CreateMockSensorId("json_test"), "device_001", "position");
            positionData.position.SetPosition(new Vector3(1, 2, 3));
            positionData.rotation.SetRotation(Quaternion.Euler(45, 90, 0));
            
            AssertJsonSerialization(positionData);
            
            // 测试身份数据序列化
            var identityData = new IdentityData(CreateMockSensorId("json_test2"), "device_002", "identity");
            identityData.name = "测试用户";
            identityData.role = "测试角色";
            
            AssertJsonSerialization(identityData);
        }
    }
    
    /// <summary>
    /// 测试用传感器类
    /// </summary>
    public class TestSensor : SensorBase
    {
        private int dataCollectionCount = 0;
        
        public void SetSensorConfiguration(string sensorId, string deviceId, float interval)
        {
            this.sensorId = sensorId;
            this.deviceId = deviceId;
            this.dataCollectionInterval = interval;
        }
        
        public string GetSensorId() => sensorId;
        public string GetDeviceId() => deviceId;
        public float GetDataCollectionInterval() => dataCollectionInterval;
        public int GetDataCollectionCount() => dataCollectionCount;
        
        protected override void CollectData()
        {
            dataCollectionCount++;
            
            // 创建测试数据
            var testData = new TestSensorData(sensorId, deviceId, "test");
            testData.testValue = dataCollectionCount;
            
            // 上报数据
            ReportSensorData(testData);
            
            // 触发事件
            TriggerDataGenerated(testData);
        }
    }
    
    /// <summary>
    /// 测试传感器数据类
    /// </summary>
    [System.Serializable]
    public class TestSensorData : SensorDataBase
    {
        public int testValue;
        
        public TestSensorData(string sensorId, string deviceId, string sensorType) 
            : base(sensorId, deviceId, sensorType)
        {
        }
    }
}
