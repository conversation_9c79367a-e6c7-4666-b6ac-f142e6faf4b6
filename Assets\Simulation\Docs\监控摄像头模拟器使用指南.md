# 监控摄像头模拟器使用指南

## 概述

监控摄像头模拟器（CameraMonitorSimulator）是建筑工地数字孪生系统的重要组成部分，用于模拟监控摄像头的运行状态、视角控制和监控事件检测功能。

## 功能特性

### 1. 摄像头类型支持
- **固定摄像头（Fixed）**：固定位置和视角的监控摄像头
- **云台摄像头（PTZ）**：支持水平、垂直旋转和缩放控制的摄像头

### 2. 监控功能
- **运动检测**：检测监控范围内的移动目标
- **入侵检测**：识别未授权人员或异常行为
- **对象检测**：识别和分类监控范围内的对象类型

### 3. 录像功能
- **连续录像**：支持24小时连续录像
- **事件触发录像**：检测到事件时自动开始录像
- **存储管理**：模拟存储空间使用和管理

### 4. 数据上报
- **状态数据**：定期上报摄像头运行状态
- **监控事件**：实时上报检测到的监控事件
- **统计信息**：提供监控统计数据

## 使用方法

### 1. 基础配置

#### 摄像头基础信息
```cs
[Header("摄像头基础信息")]
[SerializeField] private string cameraId = "camera_001";
[SerializeField] private string cameraName = "主入口监控";
[SerializeField] private CameraType cameraType = CameraType.Fixed;
[SerializeField] private string installationLocation = "主入口";
```

#### 技术参数配置
```cs
[Header("摄像头技术参数")]
[SerializeField] private string resolution = "1920x1080";
[SerializeField] private int frameRate = 30;
[SerializeField] private float viewDistance = 50f;
[SerializeField] private float horizontalFOV = 90f;
[SerializeField] private float verticalFOV = 60f;
```

### 2. 监控配置

#### 检测范围设置
```cs
[Header("监控配置")]
[SerializeField] private float monitoringRadius = 30f; // 监控半径
[SerializeField] private LayerMask detectionLayers = -1; // 检测层
[SerializeField] private bool enableMotionDetection = true;
[SerializeField] private bool enableIntrusionDetection = true;
[SerializeField] private bool enableObjectDetection = false;
```

#### 数据上报配置
```cs
[Header("数据上报配置")]
[SerializeField] private float statusReportInterval = 60f; // 状态上报间隔（秒）
[SerializeField] private float detectionCheckInterval = 2f; // 检测检查间隔（秒）
[SerializeField] private bool enableDataLogging = true;
```

### 3. 云台摄像头配置

#### 云台控制参数
```cs
[Header("云台摄像头参数（仅云台类型）")]
[SerializeField] private bool enablePTZControl = false;
[SerializeField] private float minHorizontalAngle = -180f;
[SerializeField] private float maxHorizontalAngle = 180f;
[SerializeField] private float minVerticalAngle = -30f;
[SerializeField] private float maxVerticalAngle = 90f;
[SerializeField] private float minZoomLevel = 1f;
[SerializeField] private float maxZoomLevel = 10f;
```

#### 手动控制云台
```cs
// 控制云台到指定位置
cameraSimulator.ControlPTZ(45f, 30f, 2f); // 水平45°，垂直30°，缩放2倍
```

### 4. 录像配置

```cs
[Header("录像配置")]
[SerializeField] private bool enableRecording = true;
[SerializeField] private float storageCapacity = 1000f; // 存储容量（GB）
[SerializeField] private float recordingQuality = 0.8f; // 录像质量（0-1）
[SerializeField] private bool enableEventTriggeredRecording = true;
```

## 数据结构

### 1. 摄像头数据（CameraData）
```cs
public class CameraData : SensorDataBase
{
    public string cameraType; // "fixed" or "ptz"
    public CameraStatus status;
    public CameraViewInfo viewInfo;
    public RecordingInfo recordingInfo;
    public PositionInfo location;
}
```

### 2. 监控事件数据（MonitoringEventData）
```cs
public class MonitoringEventData : IoTEventData
{
    public string cameraId;
    public string detectionType; // motion, intrusion, object_detection
    public string detectedObject;
    public PositionInfo detectionLocation;
    public float confidence; // 检测置信度
    public string imageUrl; // 截图URL
}
```

## 事件类型

### 1. 检测事件
- **motion_detected**：运动检测
- **intrusion_detected**：入侵检测
- **object_detected**：对象检测
- **object_left**：对象离开监控范围

### 2. 系统事件
- **recording_started**：开始录像
- **recording_stopped**：停止录像
- **storage_full**：存储空间不足
- **camera_shutdown**：摄像头关机

### 3. 云台事件
- **ptz_patrol_started**：开始巡航
- **ptz_patrol_completed**：巡航完成
- **ptz_manual_control**：手动控制云台

## 测试和调试

### 1. 使用测试脚本
项目提供了`CameraMonitorTest`测试脚本，支持：
- 自动创建测试对象（工人、车辆）
- 模拟对象移动以触发检测事件
- 手动控制云台测试
- 显示监控统计信息

### 2. 可视化调试
在Scene视图中可以看到：
- 监控范围（绿色球体）
- 视角方向（蓝色射线）
- 视角锥体边界

### 3. 日志输出
模拟器会输出详细的日志信息：
```
[CameraMonitor] 启动监控摄像头模拟器: 主入口监控 (camera_001)
[CameraMonitor] 摄像头 主入口监控 状态已上报 - 在线: True, 录像: True, 信号: 95.2%
[CameraMonitor] 发送监控事件: 检测到移动目标: 工人 (置信度: 80%)
```

## 集成说明

### 1. IoT系统集成
摄像头模拟器自动集成到IoT系统：
- 启动时自动注册到IoT系统
- 定期上报状态数据
- 实时发送监控事件

### 2. MQTT数据传输
所有数据通过MQTT协议传输：
- 状态数据发送到传感器数据主题
- 监控事件发送到事件主题
- 支持JSON格式数据序列化

### 3. 数据存储
- 状态数据存储在传感器数据库
- 事件数据存储在事件数据库
- 录像文件模拟存储路径管理

## 最佳实践

1. **合理设置监控范围**：根据实际需求调整监控半径和视角
2. **优化检测频率**：平衡检测精度和性能消耗
3. **配置存储容量**：根据录像需求设置合适的存储容量
4. **使用分层检测**：通过LayerMask精确控制检测对象
5. **监控性能指标**：定期检查信号强度和存储使用情况

## 扩展开发

### 1. 自定义检测逻辑
可以扩展`OnObjectDetected`方法实现自定义检测逻辑：
```cs
private void OnObjectDetected(GameObject detectedObject)
{
    // 自定义检测逻辑
    if (IsCustomCondition(detectedObject))
    {
        SendCustomEvent();
    }
}
```

### 2. 添加新的事件类型
在`MonitoringEventData`中添加新的检测类型和事件处理。

### 3. 集成AI检测
可以集成机器学习模型进行更精确的对象识别和行为分析。
