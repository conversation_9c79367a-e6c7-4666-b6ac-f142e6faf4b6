using System;
using System.Collections.Generic;
using System.Threading;
using Cysharp.Threading.Tasks;
using UnityEngine;
using Simulation.IoTSystem;
using Simulation.Data;

namespace Simulation.Simulators
{
    /// <summary>
    /// 升降机模拟器
    /// 模拟升降机的运行和载人载物流程
    /// </summary>
    public class ElevatorSimulator : SimulatorBase
    {
        [Header("升降机基础信息")]
        [SerializeField] private string elevatorId = "elevator_001";
        [SerializeField] private string elevatorName = "主升降机";
        [SerializeField] private string operatorId = "operator_002";
        [SerializeField] private string operatorName = "张师傅";
        [SerializeField] private float maxHeight = 100f; // 最大高度（米）
        [SerializeField] private float maxLoad = 2000f; // 最大载重（kg）
        [SerializeField] private int maxPersons = 10; // 最大载人数
        
        [Header("运行配置")]
        [SerializeField] private float liftSpeed = 1.5f; // 升降速度（米/秒）
        [SerializeField] private float doorOperationTime = 3f; // 开关门时间（秒）
        [SerializeField] private float loadingTime = 10f; // 装载时间（秒）
        [SerializeField] private float unloadingTime = 8f; // 卸载时间（秒）
        [SerializeField] private int dailyTrips = 30; // 每日运行次数
        [SerializeField] private float tripInterval = 600f; // 运行间隔（秒）
        
        [Header("楼层配置")]
        [SerializeField] private float[] floorHeights = { 0f, 3f, 6f, 9f, 12f, 15f }; // 楼层高度
        [SerializeField] private string[] floorNames = { "地面", "1层", "2层", "3层", "4层", "5层" }; // 楼层名称
        [SerializeField] private Transform[] floorPositions; // 楼层位置
        
        [Header("安全配置")]
        [SerializeField] private bool enableSafetyChecks = true;
        [SerializeField] private float safetyCheckInterval = 60f; // 安全检查间隔（秒）
        [SerializeField] private float overloadThreshold = 0.9f; // 超载阈值
        [SerializeField] private float emergencyStopProbability = 0.02f; // 紧急停止概率
        
        [Header("维护配置")]
        [SerializeField] private bool enableMaintenance = true;
        [SerializeField] private float maintenanceInterval = 14400f; // 维护间隔（秒，4小时）
        [SerializeField] private float maintenanceDuration = 1800f; // 维护时长（秒，30分钟）
        
        [Header("调试配置")]
        [SerializeField] private bool enableDebugLog = true;
        [SerializeField] private bool showMovementGizmos = true;
        [SerializeField] private Color movementColor = Color.magenta;
        
        // 升降机状态
        private ElevatorState currentState = ElevatorState.Idle;
        private int currentFloor = 0;
        private int targetFloor = 0;
        private float currentHeight = 0f;
        private float currentLoad = 0f;
        private int currentPersonCount = 0;
        private bool doorOpen = false;
        private float stateStartTime;
        private int completedTrips = 0;
        
        // 乘客队列
        private Queue<ElevatorRequest> requestQueue = new Queue<ElevatorRequest>();
        private List<ElevatorPassenger> currentPassengers = new List<ElevatorPassenger>();
        
        // 运行统计
        private float totalOperationTime = 0f;
        private float totalDistance = 0f;
        private int totalPassengers = 0;
        private int totalSafetyEvents = 0;
        private DateTime lastMaintenanceTime = DateTime.UtcNow;
        
        // 公共属性
        public string ElevatorId => elevatorId;
        public ElevatorState CurrentState => currentState;
        public int CurrentFloor => currentFloor;
        public float CurrentLoad => currentLoad;
        public int CurrentPersonCount => currentPersonCount;
        public bool DoorOpen => doorOpen;
        
        protected override async UniTask OnStartSimulationAsync(CancellationToken cancellationToken)
        {
            LogDebug("升降机模拟器启动");
            
            // 初始化楼层配置
            if (floorPositions == null || floorPositions.Length == 0)
            {
                CreateDefaultFloorPositions();
            }
            
            // 设置初始状态
            ChangeState(ElevatorState.Idle);
            currentFloor = 0;
            currentHeight = floorHeights[0];
            transform.position = new Vector3(transform.position.x, currentHeight, transform.position.z);
            
            // 发送升降机启动事件
            SendElevatorEvent("elevator_start", "升降机启动", $"{elevatorName} 由 {operatorName} 操作启动");
            
            // 启动安全检查
            if (enableSafetyChecks)
            {
                SafetyCheckLoop(cancellationToken).Forget();
            }
            
            // 启动维护检查
            if (enableMaintenance)
            {
                MaintenanceCheckLoop(cancellationToken).Forget();
            }
            
            // 启动运行循环
            OperationLoop(cancellationToken).Forget();
            
            // 启动请求生成
            RequestGenerationLoop(cancellationToken).Forget();
        }
        
        protected override void OnSimulationUpdate(float deltaTime)
        {
            totalOperationTime += deltaTime;
            
            switch (currentState)
            {
                case ElevatorState.MovingUp:
                case ElevatorState.MovingDown:
                    UpdateMoving(deltaTime);
                    break;
                case ElevatorState.OpeningDoor:
                    UpdateOpeningDoor(deltaTime);
                    break;
                case ElevatorState.Loading:
                    UpdateLoading(deltaTime);
                    break;
                case ElevatorState.Unloading:
                    UpdateUnloading(deltaTime);
                    break;
                case ElevatorState.ClosingDoor:
                    UpdateClosingDoor(deltaTime);
                    break;
            }
        }
        
        protected override UniTask OnStopSimulationAsync()
        {
            LogDebug("升降机模拟器停止");

            // 发送升降机停止事件
            SendElevatorEvent("elevator_stop", "升降机停止",
                $"{elevatorName} 停止运行，共完成{completedTrips}次运行，运送{totalPassengers}人次");

            return UniTask.CompletedTask;
        }
        
        /// <summary>
        /// 运行循环
        /// </summary>
        private async UniTaskVoid OperationLoop(CancellationToken cancellationToken)
        {
            try
            {
                while (!cancellationToken.IsCancellationRequested && completedTrips < dailyTrips)
                {
                    // 处理请求队列
                    if (requestQueue.Count > 0 && currentState == ElevatorState.Idle)
                    {
                        var request = requestQueue.Dequeue();
                        await ProcessRequest(request, cancellationToken);
                    }
                    
                    await UniTask.Delay(TimeSpan.FromSeconds(1f), cancellationToken: cancellationToken);
                }
                
                LogDebug($"完成所有运行，共{completedTrips}次");
            }
            catch (OperationCanceledException)
            {
                LogDebug("运行循环被取消");
            }
        }
        
        /// <summary>
        /// 请求生成循环
        /// </summary>
        private async UniTaskVoid RequestGenerationLoop(CancellationToken cancellationToken)
        {
            try
            {
                while (!cancellationToken.IsCancellationRequested)
                {
                    await UniTask.Delay(TimeSpan.FromSeconds(tripInterval), cancellationToken: cancellationToken);
                    
                    // 生成随机请求
                    GenerateRandomRequest();
                }
            }
            catch (OperationCanceledException)
            {
                LogDebug("请求生成循环被取消");
            }
        }
        
        /// <summary>
        /// 生成随机请求
        /// </summary>
        private void GenerateRandomRequest()
        {
            var request = new ElevatorRequest
            {
                requestId = Guid.NewGuid().ToString(),
                fromFloor = UnityEngine.Random.Range(0, floorHeights.Length),
                toFloor = UnityEngine.Random.Range(0, floorHeights.Length),
                passengerCount = UnityEngine.Random.Range(1, 5),
                materialWeight = UnityEngine.Random.Range(0f, 500f),
                requestTime = DateTime.UtcNow
            };
            
            // 确保起始楼层和目标楼层不同
            while (request.fromFloor == request.toFloor)
            {
                request.toFloor = UnityEngine.Random.Range(0, floorHeights.Length);
            }
            
            requestQueue.Enqueue(request);
            LogDebug($"生成请求: {floorNames[request.fromFloor]} -> {floorNames[request.toFloor]}, {request.passengerCount}人");
        }
        
        /// <summary>
        /// 处理请求
        /// </summary>
        private async UniTask ProcessRequest(ElevatorRequest request, CancellationToken cancellationToken)
        {
            LogDebug($"处理请求: {request.requestId}");
            
            // 移动到起始楼层
            if (currentFloor != request.fromFloor)
            {
                await MoveToFloor(request.fromFloor, cancellationToken);
            }
            
            // 开门
            ChangeState(ElevatorState.OpeningDoor);
            await UniTask.Delay(TimeSpan.FromSeconds(doorOperationTime), cancellationToken: cancellationToken);
            doorOpen = true;
            
            // 装载
            ChangeState(ElevatorState.Loading);
            await LoadPassengers(request, cancellationToken);
            
            // 关门
            ChangeState(ElevatorState.ClosingDoor);
            await UniTask.Delay(TimeSpan.FromSeconds(doorOperationTime), cancellationToken: cancellationToken);
            doorOpen = false;
            
            // 移动到目标楼层
            await MoveToFloor(request.toFloor, cancellationToken);
            
            // 开门
            ChangeState(ElevatorState.OpeningDoor);
            await UniTask.Delay(TimeSpan.FromSeconds(doorOperationTime), cancellationToken: cancellationToken);
            doorOpen = true;
            
            // 卸载
            ChangeState(ElevatorState.Unloading);
            await UnloadPassengers(cancellationToken);
            
            // 关门
            ChangeState(ElevatorState.ClosingDoor);
            await UniTask.Delay(TimeSpan.FromSeconds(doorOperationTime), cancellationToken: cancellationToken);
            doorOpen = false;
            
            completedTrips++;
            
            // 发送运行完成事件
            SendOperationEvent("elevator_trip_complete", "升降机运行完成", 
                $"完成第{completedTrips}次运行: {floorNames[request.fromFloor]} -> {floorNames[request.toFloor]}");
            
            ChangeState(ElevatorState.Idle);
        }
        
        /// <summary>
        /// 移动到指定楼层
        /// </summary>
        private async UniTask MoveToFloor(int targetFloor, CancellationToken cancellationToken)
        {
            this.targetFloor = targetFloor;
            float targetHeight = floorHeights[targetFloor];
            
            if (currentHeight < targetHeight)
            {
                ChangeState(ElevatorState.MovingUp);
            }
            else if (currentHeight > targetHeight)
            {
                ChangeState(ElevatorState.MovingDown);
            }
            else
            {
                return; // 已经在目标楼层
            }
            
            // 等待移动完成
            while (Mathf.Abs(currentHeight - targetHeight) > 0.1f && !cancellationToken.IsCancellationRequested)
            {
                await UniTask.Delay(TimeSpan.FromSeconds(0.1f), cancellationToken: cancellationToken);
            }
            
            currentFloor = targetFloor;
            LogDebug($"到达楼层: {floorNames[currentFloor]}");
        }
        
        /// <summary>
        /// 装载乘客
        /// </summary>
        private async UniTask LoadPassengers(ElevatorRequest request, CancellationToken cancellationToken)
        {
            await UniTask.Delay(TimeSpan.FromSeconds(loadingTime), cancellationToken: cancellationToken);
            
            // 检查载重和人数限制
            float totalWeight = currentLoad + request.materialWeight;
            int totalPersons = currentPersonCount + request.passengerCount;
            
            if (totalWeight <= maxLoad && totalPersons <= maxPersons)
            {
                // 添加乘客
                var passenger = new ElevatorPassenger
                {
                    passengerId = Guid.NewGuid().ToString(),
                    passengerCount = request.passengerCount,
                    materialWeight = request.materialWeight,
                    fromFloor = request.fromFloor,
                    toFloor = request.toFloor
                };
                
                currentPassengers.Add(passenger);
                currentLoad += request.materialWeight;
                currentPersonCount += request.passengerCount;
                totalPassengers += request.passengerCount;
                
                LogDebug($"装载完成: {request.passengerCount}人, {request.materialWeight:F0}kg");
                
                // 发送装载事件
                SendLoadEvent("passenger_loaded", request.passengerCount, request.materialWeight);
            }
            else
            {
                LogDebug("装载失败: 超载或超员");
                
                // 发送超载事件
                SendSafetyEvent("overload_warning");
            }
        }
        
        /// <summary>
        /// 卸载乘客
        /// </summary>
        private async UniTask UnloadPassengers(CancellationToken cancellationToken)
        {
            await UniTask.Delay(TimeSpan.FromSeconds(unloadingTime), cancellationToken: cancellationToken);
            
            // 卸载到达目标楼层的乘客
            var passengersToRemove = new List<ElevatorPassenger>();
            
            foreach (var passenger in currentPassengers)
            {
                if (passenger.toFloor == currentFloor)
                {
                    passengersToRemove.Add(passenger);
                    currentLoad -= passenger.materialWeight;
                    currentPersonCount -= passenger.passengerCount;
                    
                    LogDebug($"卸载乘客: {passenger.passengerCount}人, {passenger.materialWeight:F0}kg");
                    
                    // 发送卸载事件
                    SendLoadEvent("passenger_unloaded", passenger.passengerCount, passenger.materialWeight);
                }
            }
            
            foreach (var passenger in passengersToRemove)
            {
                currentPassengers.Remove(passenger);
            }
        }
        
        /// <summary>
        /// 更新移动状态
        /// </summary>
        private void UpdateMoving(float deltaTime)
        {
            float targetHeight = floorHeights[targetFloor];
            float direction = Mathf.Sign(targetHeight - currentHeight);
            
            currentHeight = Mathf.MoveTowards(currentHeight, targetHeight, liftSpeed * deltaTime);
            totalDistance += liftSpeed * deltaTime;
            
            // 更新位置
            transform.position = new Vector3(transform.position.x, currentHeight, transform.position.z);
            
            // 检查是否到达目标
            if (Mathf.Abs(currentHeight - targetHeight) <= 0.1f)
            {
                currentHeight = targetHeight;
                currentFloor = targetFloor;
                ChangeState(ElevatorState.Idle);
            }
        }
        
        /// <summary>
        /// 更新开门状态
        /// </summary>
        private void UpdateOpeningDoor(float deltaTime)
        {
            float openTime = Time.time - stateStartTime;
            if (openTime >= doorOperationTime)
            {
                doorOpen = true;
                ChangeState(ElevatorState.Idle);
            }
        }
        
        /// <summary>
        /// 更新装载状态
        /// </summary>
        private void UpdateLoading(float deltaTime)
        {
            float loadTime = Time.time - stateStartTime;
            if (loadTime >= loadingTime)
            {
                ChangeState(ElevatorState.Idle);
            }
        }
        
        /// <summary>
        /// 更新卸载状态
        /// </summary>
        private void UpdateUnloading(float deltaTime)
        {
            float unloadTime = Time.time - stateStartTime;
            if (unloadTime >= unloadingTime)
            {
                ChangeState(ElevatorState.Idle);
            }
        }
        
        /// <summary>
        /// 更新关门状态
        /// </summary>
        private void UpdateClosingDoor(float deltaTime)
        {
            float closeTime = Time.time - stateStartTime;
            if (closeTime >= doorOperationTime)
            {
                doorOpen = false;
                ChangeState(ElevatorState.Idle);
            }
        }
        
        /// <summary>
        /// 安全检查循环
        /// </summary>
        private async UniTaskVoid SafetyCheckLoop(CancellationToken cancellationToken)
        {
            try
            {
                while (!cancellationToken.IsCancellationRequested)
                {
                    await UniTask.Delay(TimeSpan.FromSeconds(safetyCheckInterval), cancellationToken: cancellationToken);
                    
                    CheckSafety();
                }
            }
            catch (OperationCanceledException)
            {
                LogDebug("安全检查循环被取消");
            }
        }
        
        /// <summary>
        /// 维护检查循环
        /// </summary>
        private async UniTaskVoid MaintenanceCheckLoop(CancellationToken cancellationToken)
        {
            try
            {
                while (!cancellationToken.IsCancellationRequested)
                {
                    await UniTask.Delay(TimeSpan.FromSeconds(maintenanceInterval), cancellationToken: cancellationToken);
                    
                    if (currentState == ElevatorState.Idle)
                    {
                        await PerformMaintenance(cancellationToken);
                    }
                }
            }
            catch (OperationCanceledException)
            {
                LogDebug("维护检查循环被取消");
            }
        }
        
        /// <summary>
        /// 检查安全状况
        /// </summary>
        private void CheckSafety()
        {
            // 检查超载
            if (currentLoad / maxLoad > overloadThreshold)
            {
                totalSafetyEvents++;
                SendSafetyEvent("overload_detected");
            }
            
            // 检查超员
            if (currentPersonCount > maxPersons)
            {
                totalSafetyEvents++;
                SendSafetyEvent("overcrowding_detected");
            }
            
            // 随机紧急停止
            if (UnityEngine.Random.Range(0f, 1f) < emergencyStopProbability)
            {
                totalSafetyEvents++;
                SendSafetyEvent("emergency_stop");
                
                if (currentState == ElevatorState.MovingUp || currentState == ElevatorState.MovingDown)
                {
                    ChangeState(ElevatorState.Emergency);
                }
            }
        }
        
        /// <summary>
        /// 执行维护
        /// </summary>
        private async UniTask PerformMaintenance(CancellationToken cancellationToken)
        {
            LogDebug("开始维护");
            ChangeState(ElevatorState.Maintenance);
            
            // 发送维护开始事件
            SendMaintenanceEvent("maintenance_start", "升降机维护开始");
            
            await UniTask.Delay(TimeSpan.FromSeconds(maintenanceDuration), cancellationToken: cancellationToken);
            
            lastMaintenanceTime = DateTime.UtcNow;
            
            // 发送维护完成事件
            SendMaintenanceEvent("maintenance_complete", "升降机维护完成");
            
            ChangeState(ElevatorState.Idle);
            LogDebug("维护完成");
        }
        
        /// <summary>
        /// 创建默认楼层位置
        /// </summary>
        private void CreateDefaultFloorPositions()
        {
            floorPositions = new Transform[floorHeights.Length];
            
            for (int i = 0; i < floorHeights.Length; i++)
            {
                GameObject floorPos = new GameObject($"Floor_{i}");
                floorPos.transform.position = new Vector3(transform.position.x, floorHeights[i], transform.position.z);
                floorPositions[i] = floorPos.transform;
            }
        }
        
        /// <summary>
        /// 改变升降机状态
        /// </summary>
        private void ChangeState(ElevatorState newState)
        {
            if (currentState != newState)
            {
                LogDebug($"升降机状态变化: {currentState} -> {newState}");
                currentState = newState;
                stateStartTime = Time.time;
                
                // 发送状态变化事件
                SendStatusEvent(currentState.ToString(), newState.ToString());
            }
        }
        
        /// <summary>
        /// 发送升降机事件
        /// </summary>
        private void SendElevatorEvent(string eventType, string title, string description)
        {
            var eventData = new OperationEventData
            {
                operationType = eventType,
                operatorId = operatorId,
                operatorName = operatorName,
                targetId = elevatorId,
                targetType = "elevator",
                title = title,
                description = description,
                sourceId = elevatorId
            };
            
            eventData.location.SetPosition(transform.position);
            
            IoTSystem.IoTSystem.Instance?.SendIOTEvent(elevatorId, eventType, title, description, eventData);
        }
        
        /// <summary>
        /// 发送状态变化事件
        /// </summary>
        private void SendStatusEvent(string previousStatus, string currentStatus)
        {
            var statusEvent = new StatusEventData
            {
                previousStatus = previousStatus,
                currentStatus = currentStatus,
                equipmentId = elevatorId,
                equipmentType = "elevator",
                sourceId = elevatorId,
                title = "升降机状态变化",
                description = $"{elevatorName} 状态从{previousStatus}变为{currentStatus}"
            };
            
            IoTSystem.IoTSystem.Instance?.SendIOTEvent(elevatorId, "status_change", "升降机状态变化", statusEvent.description, statusEvent);
        }
        
        /// <summary>
        /// 发送操作事件
        /// </summary>
        private void SendOperationEvent(string operationType, string title, string description)
        {
            var operationEvent = new OperationEventData
            {
                operationType = operationType,
                operatorId = operatorId,
                operatorName = operatorName,
                targetId = elevatorId,
                targetType = "elevator",
                title = title,
                description = description,
                sourceId = elevatorId
            };
            
            operationEvent.location.SetPosition(transform.position);
            
            IoTSystem.IoTSystem.Instance?.SendIOTEvent(elevatorId, operationType, title, description, operationEvent);
        }
        
        /// <summary>
        /// 发送装载事件
        /// </summary>
        private void SendLoadEvent(string operationType, int passengerCount, float materialWeight)
        {
            var loadEvent = new MaterialEventData
            {
                materialType = "passenger_and_material",
                quantity = materialWeight,
                unit = "kg",
                operationType = operationType == "passenger_loaded" ? "in" : "out",
                operatorId = operatorId,
                sourceId = elevatorId,
                title = operationType == "passenger_loaded" ? "乘客装载" : "乘客卸载",
                description = $"{elevatorName} {(operationType == "passenger_loaded" ? "装载" : "卸载")} {passengerCount}人，{materialWeight:F0}kg物料"
            };
            
            IoTSystem.IoTSystem.Instance?.SendIOTEvent(elevatorId, "load_operation", loadEvent.title, loadEvent.description, loadEvent);
        }
        
        /// <summary>
        /// 发送安全事件
        /// </summary>
        private void SendSafetyEvent(string violationType)
        {
            var safetyEvent = new SafetyEventData
            {
                violationType = violationType,
                workerId = operatorId,
                workerName = operatorName,
                sourceId = elevatorId,
                title = "升降机安全事件",
                description = $"{elevatorName} 发生安全事件: {violationType}",
                severity = "warning"
            };
            
            safetyEvent.location.SetPosition(transform.position);
            
            IoTSystem.IoTSystem.Instance?.SendIOTEvent(elevatorId, "safety_violation", "升降机安全事件", safetyEvent.description, safetyEvent);
        }
        
        /// <summary>
        /// 发送维护事件
        /// </summary>
        private void SendMaintenanceEvent(string eventType, string description)
        {
            var maintenanceEvent = new StatusEventData
            {
                equipmentId = elevatorId,
                equipmentType = "elevator",
                currentStatus = currentState.ToString(),
                sourceId = elevatorId,
                title = "升降机维护",
                description = description
            };
            
            IoTSystem.IoTSystem.Instance?.SendIOTEvent(elevatorId, eventType, "升降机维护", description, maintenanceEvent);
        }
        
        /// <summary>
        /// 调试日志
        /// </summary>
        private void LogDebug(string message)
        {
            if (enableDebugLog)
            {
                Debug.Log($"[ElevatorSimulator:{elevatorId}] {message}");
            }
        }
        
        /// <summary>
        /// 绘制调试信息
        /// </summary>
        private void OnDrawGizmos()
        {
            if (!showMovementGizmos) return;
            
            // 绘制升降机轨道
            if (floorHeights != null && floorHeights.Length > 1)
            {
                Gizmos.color = movementColor;
                Vector3 bottom = new Vector3(transform.position.x, floorHeights[0], transform.position.z);
                Vector3 top = new Vector3(transform.position.x, floorHeights[floorHeights.Length - 1], transform.position.z);
                Gizmos.DrawLine(bottom, top);
                
                // 绘制楼层标记
                for (int i = 0; i < floorHeights.Length; i++)
                {
                    Vector3 floorPos = new Vector3(transform.position.x, floorHeights[i], transform.position.z);
                    Gizmos.color = i == currentFloor ? Color.green : Color.gray;
                    Gizmos.DrawWireCube(floorPos, Vector3.one);
                }
            }
            
            // 绘制升降机轿厢
            Gizmos.color = doorOpen ? Color.yellow : Color.blue;
            Gizmos.DrawWireCube(transform.position, new Vector3(2f, 1f, 2f));
            
            // 绘制载重状态
            float loadRatio = currentLoad / maxLoad;
            Gizmos.color = Color.Lerp(Color.green, Color.red, loadRatio);
            Gizmos.DrawWireSphere(transform.position + Vector3.up * 2f, 0.5f);
        }
    }
    
    /// <summary>
    /// 升降机状态枚举
    /// </summary>
    public enum ElevatorState
    {
        Idle,           // 空闲
        MovingUp,       // 上升
        MovingDown,     // 下降
        OpeningDoor,    // 开门
        ClosingDoor,    // 关门
        Loading,        // 装载
        Unloading,      // 卸载
        Emergency,      // 紧急状态
        Maintenance     // 维护状态
    }
    
    /// <summary>
    /// 升降机请求数据
    /// </summary>
    [Serializable]
    public class ElevatorRequest
    {
        public string requestId;
        public int fromFloor;
        public int toFloor;
        public int passengerCount;
        public float materialWeight;
        public DateTime requestTime;
    }
    
    /// <summary>
    /// 升降机乘客数据
    /// </summary>
    [Serializable]
    public class ElevatorPassenger
    {
        public string passengerId;
        public int passengerCount;
        public float materialWeight;
        public int fromFloor;
        public int toFloor;
    }
}
