using UnityEngine;
using Best.MQTT;
using Best.MQTT.Packets;
using Cysharp.Threading.Tasks;
using System.Threading;
using System;
using System.Text;

namespace Simulation.IoTSystem.MQTT
{
    /// <summary>
    /// MQTT客户端管理器
    /// 负责MQTT连接、发布和订阅管理
    /// </summary>
    public class MQTTManager : MonoBehaviour
    {
        [Header("配置")]
        [SerializeField] private MQTTConfig mqttConfig;
        [SerializeField] private bool autoConnect = true;
        [SerializeField] private bool enableDebugLog = true;
        
        // MQTT客户端
        private MqttClient mqttClient;
        private CancellationTokenSource cancellationTokenSource;
        
        // 连接状态
        public bool IsConnected => mqttClient?.State == ClientStates.Connected;
        public bool IsConnecting => mqttClient?.State == ClientStates.Connecting;
        
        // 事件
        public event Action OnConnected;
        public event Action<string> OnDisconnected;
        public event Action<string, byte[]> OnMessageReceived;
        public event Action<string> OnError;
        
        private void Awake()
        {
            cancellationTokenSource = new CancellationTokenSource();
        }
        
        private void Start()
        {
            if (autoConnect && mqttConfig != null)
            {
                ConnectAsync(cancellationTokenSource.Token).Forget();
            }
        }
        
        private void OnDestroy()
        {
            DisconnectAsync().Forget();
            cancellationTokenSource?.Cancel();
            cancellationTokenSource?.Dispose();
        }
        
        /// <summary>
        /// 异步连接到MQTT Broker
        /// </summary>
        public async UniTask<bool> ConnectAsync(CancellationToken cancellationToken = default)
        {
            if (mqttConfig == null)
            {
                LogError("MQTT配置为空，无法连接");
                return false;
            }
            
            if (!mqttConfig.ValidateConfig())
            {
                LogError("MQTT配置验证失败");
                return false;
            }
            
            if (IsConnected)
            {
                LogDebug("MQTT已连接");
                return true;
            }
            
            try
            {
                LogDebug($"正在连接到MQTT Broker: {mqttConfig.BrokerHost}:{mqttConfig.BrokerPort}");
                
                // 创建MQTT客户端
                var options = mqttConfig.CreateConnectionOptions();
                mqttClient = new MqttClient(options);
                
                // 设置事件处理
                SetupEventHandlers();
                
                // 开始连接
                mqttClient.BeginConnect();
                
                // 等待连接完成
                var timeout = TimeSpan.FromSeconds(mqttConfig.ConnectTimeoutSeconds);
                var startTime = DateTime.Now;
                
                while (!IsConnected && DateTime.Now - startTime < timeout)
                {
                    if (cancellationToken.IsCancellationRequested)
                    {
                        LogDebug("连接被取消");
                        return false;
                    }
                    
                    await UniTask.Delay(100, cancellationToken: cancellationToken);
                }
                
                if (IsConnected)
                {
                    LogDebug("MQTT连接成功");
                    OnConnected?.Invoke();
                    return true;
                }
                else
                {
                    LogError("MQTT连接超时");
                    return false;
                }
            }
            catch (Exception ex)
            {
                LogError($"MQTT连接失败: {ex.Message}");
                OnError?.Invoke(ex.Message);
                return false;
            }
        }
        
        /// <summary>
        /// 异步断开连接
        /// </summary>
        public async UniTask DisconnectAsync()
        {
            if (mqttClient != null && IsConnected)
            {
                try
                {
                    LogDebug("正在断开MQTT连接");
                    mqttClient.Disconnect();
                    
                    // 等待断开完成
                    var timeout = TimeSpan.FromSeconds(5);
                    var startTime = DateTime.Now;
                    
                    while (IsConnected && DateTime.Now - startTime < timeout)
                    {
                        await UniTask.Delay(100);
                    }
                    
                    LogDebug("MQTT连接已断开");
                }
                catch (Exception ex)
                {
                    LogError($"断开MQTT连接时出错: {ex.Message}");
                }
                finally
                {
                    mqttClient = null;
                }
            }
        }
        
        /// <summary>
        /// 发布消息
        /// </summary>
        public bool PublishMessage(string topic, string message, QoSLevels qos = QoSLevels.AtMostOnceDelivery)
        {
            if (!IsConnected)
            {
                LogError("MQTT未连接，无法发布消息");
                return false;
            }
            
            try
            {
                var payload = Encoding.UTF8.GetBytes(message);
                var packet = new PublishPacket(topic, payload, qos);
                mqttClient.Send(packet);
                
                LogDebug($"发布消息到主题 {topic}: {message}");
                return true;
            }
            catch (Exception ex)
            {
                LogError($"发布消息失败: {ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// 订阅主题
        /// </summary>
        public bool Subscribe(string topic, QoSLevels qos = QoSLevels.AtMostOnceDelivery)
        {
            if (!IsConnected)
            {
                LogError("MQTT未连接，无法订阅主题");
                return false;
            }
            
            try
            {
                var subscription = new Subscription(topic, qos);
                var packet = new SubscribePacket(1, subscription);
                mqttClient.Send(packet);
                
                LogDebug($"订阅主题: {topic}");
                return true;
            }
            catch (Exception ex)
            {
                LogError($"订阅主题失败: {ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// 取消订阅主题
        /// </summary>
        public bool Unsubscribe(string topic)
        {
            if (!IsConnected)
            {
                LogError("MQTT未连接，无法取消订阅");
                return false;
            }
            
            try
            {
                var packet = new UnsubscribePacket(1, topic);
                mqttClient.Send(packet);
                
                LogDebug($"取消订阅主题: {topic}");
                return true;
            }
            catch (Exception ex)
            {
                LogError($"取消订阅失败: {ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// 设置事件处理器
        /// </summary>
        private void SetupEventHandlers()
        {
            if (mqttClient == null) return;
            
            mqttClient.OnStateChanged += OnStateChanged;
            mqttClient.OnApplicationMessageReceived += OnApplicationMessageReceived;
        }
        
        /// <summary>
        /// 状态变化处理
        /// </summary>
        private void OnStateChanged(MqttClient client, ClientStates oldState, ClientStates newState)
        {
            LogDebug($"MQTT状态变化: {oldState} -> {newState}");
            
            if (newState == ClientStates.Disconnected && oldState == ClientStates.Connected)
            {
                OnDisconnected?.Invoke("连接断开");
                
                // 自动重连
                if (mqttConfig.AutoReconnect)
                {
                    AutoReconnectAsync(cancellationTokenSource.Token).Forget();
                }
            }
        }
        
        /// <summary>
        /// 消息接收处理
        /// </summary>
        private void OnApplicationMessageReceived(MqttClient client, SubscriptionTopic topic, string topicName, ApplicationMessage message)
        {
            try
            {
                var payload = message.Payload;
                LogDebug($"收到消息 - 主题: {topicName}, 长度: {payload.Length}");
                OnMessageReceived?.Invoke(topicName, payload);
            }
            catch (Exception ex)
            {
                LogError($"处理接收消息时出错: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 自动重连
        /// </summary>
        private async UniTaskVoid AutoReconnectAsync(CancellationToken cancellationToken)
        {
            int attempts = 0;
            
            while (attempts < mqttConfig.MaxReconnectAttempts && !cancellationToken.IsCancellationRequested)
            {
                attempts++;
                LogDebug($"尝试重连 ({attempts}/{mqttConfig.MaxReconnectAttempts})");
                
                await UniTask.Delay(TimeSpan.FromSeconds(mqttConfig.ReconnectDelaySeconds), cancellationToken: cancellationToken);
                
                if (await ConnectAsync(cancellationToken))
                {
                    LogDebug("重连成功");
                    return;
                }
            }
            
            LogError("重连失败，已达到最大尝试次数");
        }
        
        /// <summary>
        /// 调试日志
        /// </summary>
        private void LogDebug(string message)
        {
            if (enableDebugLog)
            {
                Debug.Log($"[MQTTManager] {message}");
            }
        }
        
        /// <summary>
        /// 错误日志
        /// </summary>
        private void LogError(string message)
        {
            Debug.LogError($"[MQTTManager] {message}");
        }
    }
}
