using UnityEngine;
using Cysharp.Threading.Tasks;
using System.Threading;
using System.Text;

namespace Simulation.IoTSystem.MQTT
{
    /// <summary>
    /// MQTT功能测试脚本
    /// 用于验证MQTT连接和消息收发功能
    /// </summary>
    public class MQTTTest : MonoBehaviour
    {
        [Header("测试配置")]
        [SerializeField] private MQTTManager mqttManager;
        [SerializeField] private bool autoStartTest = false;
        [SerializeField] private string testTopic = "test/unity";
        [SerializeField] private float publishInterval = 5.0f;
        
        private CancellationTokenSource cancellationTokenSource;
        private int messageCounter = 0;
        
        private void Awake()
        {
            cancellationTokenSource = new CancellationTokenSource();
        }
        
        private void Start()
        {
            if (mqttManager != null)
            {
                // 订阅MQTT事件
                mqttManager.OnConnected += OnMQTTConnected;
                mqttManager.OnDisconnected += OnMQTTDisconnected;
                mqttManager.OnMessageReceived += OnMQTTMessageReceived;
                mqttManager.OnError += OnMQTTError;
                
                if (autoStartTest)
                {
                    StartTest();
                }
            }
            else
            {
                Debug.LogError("[MQTTTest] MQTTManager未设置");
            }
        }
        
        private void OnDestroy()
        {
            if (mqttManager != null)
            {
                mqttManager.OnConnected -= OnMQTTConnected;
                mqttManager.OnDisconnected -= OnMQTTDisconnected;
                mqttManager.OnMessageReceived -= OnMQTTMessageReceived;
                mqttManager.OnError -= OnMQTTError;
            }
            
            cancellationTokenSource?.Cancel();
            cancellationTokenSource?.Dispose();
        }
        
        /// <summary>
        /// 开始测试
        /// </summary>
        [ContextMenu("Start Test")]
        public void StartTest()
        {
            if (mqttManager == null)
            {
                Debug.LogError("[MQTTTest] MQTTManager未设置");
                return;
            }
            
            Debug.Log("[MQTTTest] 开始MQTT测试...");
            TestMQTTAsync(cancellationTokenSource.Token).Forget();
        }
        
        /// <summary>
        /// 停止测试
        /// </summary>
        [ContextMenu("Stop Test")]
        public void StopTest()
        {
            cancellationTokenSource?.Cancel();
            Debug.Log("[MQTTTest] 测试已停止");
        }
        
        /// <summary>
        /// 异步测试方法
        /// </summary>
        private async UniTaskVoid TestMQTTAsync(CancellationToken cancellationToken)
        {
            try
            {
                // 等待MQTT连接
                Debug.Log("[MQTTTest] 等待MQTT连接...");
                while (!mqttManager.IsConnected && !cancellationToken.IsCancellationRequested)
                {
                    await UniTask.Delay(1000, cancellationToken: cancellationToken);
                }
                
                if (cancellationToken.IsCancellationRequested)
                {
                    return;
                }
                
                // 订阅测试主题
                Debug.Log($"[MQTTTest] 订阅测试主题: {testTopic}");
                mqttManager.Subscribe(testTopic);
                
                // 开始定期发布消息
                await PublishTestMessagesAsync(cancellationToken);
            }
            catch (System.OperationCanceledException)
            {
                Debug.Log("[MQTTTest] 测试被取消");
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"[MQTTTest] 测试异常: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 定期发布测试消息
        /// </summary>
        private async UniTask PublishTestMessagesAsync(CancellationToken cancellationToken)
        {
            while (!cancellationToken.IsCancellationRequested)
            {
                messageCounter++;
                var message = CreateTestMessage();
                
                Debug.Log($"[MQTTTest] 发布测试消息 #{messageCounter}");
                mqttManager.PublishMessage(testTopic, message);
                
                await UniTask.Delay(System.TimeSpan.FromSeconds(publishInterval), cancellationToken: cancellationToken);
            }
        }
        
        /// <summary>
        /// 创建测试消息
        /// </summary>
        private string CreateTestMessage()
        {
            var testData = new
            {
                messageId = messageCounter,
                timestamp = System.DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"),
                source = "Unity MQTT Test",
                data = new
                {
                    temperature = Random.Range(20.0f, 35.0f),
                    humidity = Random.Range(40.0f, 80.0f),
                    status = "testing"
                }
            };
            
            return JsonUtility.ToJson(testData, true);
        }
        
        /// <summary>
        /// MQTT连接成功事件
        /// </summary>
        private void OnMQTTConnected()
        {
            Debug.Log("[MQTTTest] ✅ MQTT连接成功");
        }
        
        /// <summary>
        /// MQTT断开连接事件
        /// </summary>
        private void OnMQTTDisconnected(string reason)
        {
            Debug.Log($"[MQTTTest] ⚠️ MQTT连接断开: {reason}");
        }
        
        /// <summary>
        /// MQTT消息接收事件
        /// </summary>
        private void OnMQTTMessageReceived(string topic, byte[] payload)
        {
            try
            {
                var message = Encoding.UTF8.GetString(payload);
                Debug.Log($"[MQTTTest] 📨 收到消息 - 主题: {topic}");
                Debug.Log($"[MQTTTest] 消息内容: {message}");
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"[MQTTTest] 处理接收消息时出错: {ex.Message}");
            }
        }
        
        /// <summary>
        /// MQTT错误事件
        /// </summary>
        private void OnMQTTError(string error)
        {
            Debug.LogError($"[MQTTTest] ❌ MQTT错误: {error}");
        }
        
        /// <summary>
        /// 手动发送测试消息
        /// </summary>
        [ContextMenu("Send Test Message")]
        public void SendTestMessage()
        {
            if (mqttManager != null && mqttManager.IsConnected)
            {
                messageCounter++;
                var message = CreateTestMessage();
                mqttManager.PublishMessage(testTopic, message);
                Debug.Log($"[MQTTTest] 手动发送测试消息 #{messageCounter}");
            }
            else
            {
                Debug.LogWarning("[MQTTTest] MQTT未连接，无法发送消息");
            }
        }
    }
}
