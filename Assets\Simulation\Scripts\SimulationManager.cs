using System.Collections.Generic;
using UnityEngine;

namespace Simulation
{
    /// <summary>
    /// 模拟管理器，用于管理场景中所有的模拟器
    /// </summary>
    public class SimulationManager : MonoBehaviour
    {
        [SerializeField] List<SimulatorBase> simulators; // 模拟器列表

        private bool isRunning; // 模拟是否正在运行

        /// <summary>
        /// 启动模拟
        /// </summary>
        public void StartSimulation()
        {
            if (isRunning)
            {
                Debug.LogWarning("Simulation is already running");
                return;
            }

            Debug.Log("Starting simulation with " + simulators.Count + " simulators");

            foreach (var simulator in simulators)
            {
                if (simulator != null)
                {
                    simulator.StartSimulationAsync().Forget();
                }
            }

            isRunning = true;
            Debug.Log("Simulation started successfully");
        }

        /// <summary>
        /// 关闭模拟
        /// </summary>
        public void ShutdownSimulation()
        {
            if (!isRunning)
            {
                Debug.LogWarning("Simulation is not running");
                return;
            }

            Debug.Log("Shutting down simulation");

            foreach (var simulator in simulators)
            {
                if (simulator != null)
                {
                    simulator.StopSimulation();
                }
            }

            isRunning = false;
            Debug.Log("Simulation shut down successfully");
        }

        /// <summary>
        /// 暂停/恢复模拟
        /// </summary>
        public void TogglePause()
        {
            if (isRunning)
            {
                Time.timeScale = Time.timeScale > 0 ? 0 : 1;
                Debug.Log("Simulation " + (Time.timeScale > 0 ? "resumed" : "paused"));
            }
        }

        /// <summary>
        /// 获取模拟运行状态
        /// </summary>
        public bool IsRunning => isRunning;

        /// <summary>
        /// 添加模拟器
        /// </summary>
        public void AddSimulator(SimulatorBase simulator)
        {
            if (simulator != null && !simulators.Contains(simulator))
            {
                simulators.Add(simulator);

                if (isRunning)
                {
                    simulator.StartSimulationAsync().Forget();
                }
            }
        }

        /// <summary>
        /// 移除模拟器
        /// </summary>
        public void RemoveSimulator(SimulatorBase simulator)
        {
            if (simulator != null && simulators.Contains(simulator))
            {
                if (isRunning)
                {
                    simulator.StopSimulation();
                }

                simulators.Remove(simulator);
            }
        }

        private void OnDestroy()
        {
            if (isRunning)
            {
                ShutdownSimulation();
            }
        }
    }
}