using UnityEngine;
using Best.MQTT;
using Best.MQTT.Packets;

namespace Simulation.IoTSystem.MQTT
{
    /// <summary>
    /// MQTT连接配置
    /// </summary>
    [CreateAssetMenu(fileName = "MQTTConfig", menuName = "Simulation/Config/MQTT Config")]
    public class MQTTConfig : ScriptableObject
    {
        [Header("连接配置")]
        [SerializeField] private string brokerHost = "localhost";
        [SerializeField] private int brokerPort = 1883;
        [SerializeField] private string clientId = "UnitySimulation";
        [SerializeField] private string username = "";
        [SerializeField] private string password = "";
        
        [Header("连接选项")]
        [SerializeField] private bool useSSL = false;
        [SerializeField] private int keepAliveSeconds = 60;
        [SerializeField] private int connectTimeoutSeconds = 30;
        [SerializeField] private bool cleanSession = true;
        [SerializeField] private QoSLevels defaultQoS = QoSLevels.AtMostOnceDelivery;
        
        [Header("重连配置")]
        [SerializeField] private bool autoReconnect = true;
        [SerializeField] private float reconnectDelaySeconds = 5.0f;
        [SerializeField] private int maxReconnectAttempts = 10;
        
        [Header("主题配置")]
        [SerializeField] private string topicPrefix = "construction_site";
        [SerializeField] private string deviceIdPrefix = "device";
        
        // 公共属性
        public string BrokerHost => brokerHost;
        public int BrokerPort => brokerPort;
        public string ClientId => clientId;
        public string Username => username;
        public string Password => password;
        public bool UseSSL => useSSL;
        public int KeepAliveSeconds => keepAliveSeconds;
        public int ConnectTimeoutSeconds => connectTimeoutSeconds;
        public bool CleanSession => cleanSession;
        public QoSLevels DefaultQoS => defaultQoS;
        public bool AutoReconnect => autoReconnect;
        public float ReconnectDelaySeconds => reconnectDelaySeconds;
        public int MaxReconnectAttempts => maxReconnectAttempts;
        public string TopicPrefix => topicPrefix;
        public string DeviceIdPrefix => deviceIdPrefix;
        
        /// <summary>
        /// 创建MQTT连接选项
        /// </summary>
        public ConnectionOptions CreateConnectionOptions()
        {
            var options = new ConnectionOptions
            {
                Host = brokerHost,
                Port = brokerPort,
                ClientId = string.IsNullOrEmpty(clientId) ? $"UnitySimulation_{System.Guid.NewGuid():N}" : clientId,
                UserName = username,
                Password = password,
                KeepAliveTime = System.TimeSpan.FromSeconds(keepAliveSeconds),
                ConnectTimeout = System.TimeSpan.FromSeconds(connectTimeoutSeconds),
                CleanSession = cleanSession
            };
            
            return options;
        }
        
        /// <summary>
        /// 生成设备主题
        /// </summary>
        public string GenerateDeviceTopic(string deviceId, string sensorType, string dataType = "data")
        {
            return $"{topicPrefix}/{deviceId}/{sensorType}/{dataType}";
        }
        
        /// <summary>
        /// 生成设备ID
        /// </summary>
        public string GenerateDeviceId(string deviceType, int deviceIndex)
        {
            return $"{deviceIdPrefix}_{deviceType}_{deviceIndex:D3}";
        }
        
        /// <summary>
        /// 验证配置
        /// </summary>
        public bool ValidateConfig()
        {
            if (string.IsNullOrEmpty(brokerHost))
            {
                Debug.LogError("[MQTTConfig] Broker Host不能为空");
                return false;
            }
            
            if (brokerPort <= 0 || brokerPort > 65535)
            {
                Debug.LogError("[MQTTConfig] Broker Port必须在1-65535范围内");
                return false;
            }
            
            if (keepAliveSeconds <= 0)
            {
                Debug.LogError("[MQTTConfig] Keep Alive时间必须大于0");
                return false;
            }
            
            if (connectTimeoutSeconds <= 0)
            {
                Debug.LogError("[MQTTConfig] 连接超时时间必须大于0");
                return false;
            }
            
            return true;
        }
        
        /// <summary>
        /// 重置为默认值
        /// </summary>
        [ContextMenu("Reset to Default")]
        public void ResetToDefault()
        {
            brokerHost = "localhost";
            brokerPort = 1883;
            clientId = "UnitySimulation";
            username = "";
            password = "";
            useSSL = false;
            keepAliveSeconds = 60;
            connectTimeoutSeconds = 30;
            cleanSession = true;
            defaultQoS = QoSLevels.AtMostOnceDelivery;
            autoReconnect = true;
            reconnectDelaySeconds = 5.0f;
            maxReconnectAttempts = 10;
            topicPrefix = "construction_site";
            deviceIdPrefix = "device";
        }
    }
}
