using System.Threading;
using Cysharp.Threading.Tasks;
using UnityEngine;
using Simulation.Data;

namespace Simulation.Sensors
{
    /// <summary>
    /// 运动状态传感器
    /// 负责采集目标对象的运动状态和速度信息
    /// </summary>
    public class MotionSensor : SensorBase
    {
        [Header("运动检测配置")]
        [SerializeField] private Transform targetTransform;
        [SerializeField] private string targetType = "person"; // person, vehicle, equipment
        [SerializeField] private string targetId = "target_001";
        [SerializeField] private string workingStatus = "idle"; // idle, working, moving, resting
        
        [Header("速度计算配置")]
        [SerializeField] private bool enableSpeedCalculation = true;
        [SerializeField] private bool enableDirectionCalculation = true;
        [SerializeField] private float speedSmoothingFactor = 0.8f; // 速度平滑系数
        [SerializeField] private int velocityHistorySize = 5; // 速度历史记录大小
        
        [Header("状态检测配置")]
        [SerializeField] private float idleSpeedThreshold = 0.1f; // 静止速度阈值
        [SerializeField] private float walkingSpeedThreshold = 2f; // 步行速度阈值
        [SerializeField] private float runningSpeedThreshold = 5f; // 跑步速度阈值
        [SerializeField] private float statusChangeDelay = 2f; // 状态变化延迟（秒）
        
        [Header("精度配置")]
        [SerializeField] private int speedDecimalPlaces = 2;
        [SerializeField] private int directionDecimalPlaces = 1;
        [SerializeField] private float minimumSpeedChange = 0.05f; // 最小速度变化
        [SerializeField] private float minimumDirectionChange = 5f; // 最小方向变化（度）
        
        // 运动状态数据
        private Vector3 lastPosition;
        private Vector3 currentVelocity;
        private float currentSpeed;
        private float currentDirection;
        private Vector3[] velocityHistory;
        private int velocityHistoryIndex = 0;
        private bool isFirstUpdate = true;
        
        // 状态检测
        private string lastWorkingStatus;
        private float statusChangeTime;
        private bool statusChangeInProgress = false;
        
        // 传感器类型
        public override string SensorType => "motion";
        
        // 公共属性
        public float CurrentSpeed => currentSpeed;
        public Vector3 CurrentVelocity => currentVelocity;
        public string WorkingStatus => workingStatus;
        
        protected override void OnSensorStarted()
        {
            base.OnSensorStarted();
            
            // 如果没有设置目标对象，使用自身
            if (targetTransform == null)
            {
                targetTransform = transform;
                LogDebug("未设置目标对象，使用自身Transform");
            }
            
            // 初始化数据
            lastPosition = targetTransform.position;
            currentVelocity = Vector3.zero;
            currentSpeed = 0f;
            currentDirection = 0f;
            lastWorkingStatus = workingStatus;
            
            // 初始化速度历史记录
            velocityHistory = new Vector3[velocityHistorySize];
            for (int i = 0; i < velocityHistorySize; i++)
            {
                velocityHistory[i] = Vector3.zero;
            }
            
            isFirstUpdate = true;
            
            LogDebug($"运动传感器启动 - 目标: {targetType}:{targetId}");
        }
        
        protected override void OnSimulationUpdate(float deltaTime)
        {
            base.OnSimulationUpdate(deltaTime);
            
            if (targetTransform != null)
            {
                UpdateMotionData(deltaTime);
                UpdateWorkingStatus();
            }
        }
        
        protected override async UniTask<object> GenerateSensorDataAsync(CancellationToken cancellationToken)
        {
            if (targetTransform == null)
            {
                LogError("目标对象为空，无法采集运动数据");
                return null;
            }
            
            // 检查是否有显著变化
            if (!HasSignificantChange() && !isFirstUpdate)
            {
                return null; // 没有显著变化，不发送数据
            }
            
            // 创建运动数据
            var motionData = new MotionData(sensorId, deviceId);
            
            // 设置目标信息
            motionData.targetType = targetType;
            motionData.targetId = targetId;
            motionData.workingStatus = workingStatus;
            
            // 设置速度信息
            if (enableSpeedCalculation)
            {
                motionData.velocity.speed = RoundToDecimalPlaces(currentSpeed, speedDecimalPlaces);
            }
            
            if (enableDirectionCalculation)
            {
                motionData.velocity.direction = RoundToDecimalPlaces(currentDirection, directionDecimalPlaces);
            }
            
            isFirstUpdate = false;
            
            return motionData;
        }
        
        /// <summary>
        /// 更新运动数据
        /// </summary>
        private void UpdateMotionData(float deltaTime)
        {
            if (deltaTime <= 0) return;
            
            Vector3 currentPosition = targetTransform.position;
            Vector3 displacement = currentPosition - lastPosition;
            
            // 计算瞬时速度
            Vector3 instantVelocity = displacement / deltaTime;
            
            // 添加到历史记录
            velocityHistory[velocityHistoryIndex] = instantVelocity;
            velocityHistoryIndex = (velocityHistoryIndex + 1) % velocityHistorySize;
            
            // 计算平滑速度
            currentVelocity = CalculateSmoothedVelocity();
            
            // 计算速度和方向
            if (enableSpeedCalculation)
            {
                currentSpeed = currentVelocity.magnitude;
            }
            
            if (enableDirectionCalculation)
            {
                if (currentSpeed > idleSpeedThreshold)
                {
                    // 计算水平方向角度（忽略Y轴）
                    Vector3 horizontalVelocity = new Vector3(currentVelocity.x, 0, currentVelocity.z);
                    if (horizontalVelocity.magnitude > 0.001f)
                    {
                        currentDirection = Mathf.Atan2(horizontalVelocity.z, horizontalVelocity.x) * Mathf.Rad2Deg;
                        if (currentDirection < 0) currentDirection += 360f;
                    }
                }
            }
            
            lastPosition = currentPosition;
        }
        
        /// <summary>
        /// 计算平滑速度
        /// </summary>
        private Vector3 CalculateSmoothedVelocity()
        {
            Vector3 averageVelocity = Vector3.zero;
            int validSamples = 0;
            
            for (int i = 0; i < velocityHistorySize; i++)
            {
                if (velocityHistory[i] != Vector3.zero || !isFirstUpdate)
                {
                    averageVelocity += velocityHistory[i];
                    validSamples++;
                }
            }
            
            if (validSamples > 0)
            {
                averageVelocity /= validSamples;
            }
            
            // 应用平滑系数
            return Vector3.Lerp(currentVelocity, averageVelocity, speedSmoothingFactor);
        }
        
        /// <summary>
        /// 更新工作状态
        /// </summary>
        private void UpdateWorkingStatus()
        {
            string newStatus = DetermineWorkingStatus();
            
            if (newStatus != workingStatus)
            {
                if (!statusChangeInProgress)
                {
                    // 开始状态变化延迟
                    statusChangeInProgress = true;
                    statusChangeTime = Time.time;
                    lastWorkingStatus = workingStatus;
                }
                else if (Time.time - statusChangeTime >= statusChangeDelay)
                {
                    // 延迟时间到，确认状态变化
                    string oldStatus = workingStatus;
                    workingStatus = newStatus;
                    statusChangeInProgress = false;
                    
                    LogDebug($"工作状态变化: {oldStatus} -> {workingStatus}");
                }
            }
            else
            {
                // 状态没有变化，重置状态变化标志
                statusChangeInProgress = false;
            }
        }
        
        /// <summary>
        /// 确定工作状态
        /// </summary>
        private string DetermineWorkingStatus()
        {
            if (currentSpeed <= idleSpeedThreshold)
            {
                return "idle";
            }
            else if (currentSpeed <= walkingSpeedThreshold)
            {
                return "walking";
            }
            else if (currentSpeed <= runningSpeedThreshold)
            {
                return "running";
            }
            else
            {
                return "fast_moving";
            }
        }
        
        /// <summary>
        /// 检查是否有显著变化
        /// </summary>
        private bool HasSignificantChange()
        {
            // 检查速度变化
            if (enableSpeedCalculation)
            {
                float speedChange = Mathf.Abs(currentSpeed - GetLastReportedSpeed());
                if (speedChange >= minimumSpeedChange)
                {
                    return true;
                }
            }
            
            // 检查方向变化
            if (enableDirectionCalculation && currentSpeed > idleSpeedThreshold)
            {
                float directionChange = Mathf.Abs(Mathf.DeltaAngle(currentDirection, GetLastReportedDirection()));
                if (directionChange >= minimumDirectionChange)
                {
                    return true;
                }
            }
            
            // 检查工作状态变化
            if (workingStatus != lastWorkingStatus)
            {
                return true;
            }
            
            return false;
        }
        
        /// <summary>
        /// 获取上次报告的速度（简化实现）
        /// </summary>
        private float GetLastReportedSpeed()
        {
            // 这里可以存储上次报告的值，简化实现直接返回当前值的90%
            return currentSpeed * 0.9f;
        }
        
        /// <summary>
        /// 获取上次报告的方向（简化实现）
        /// </summary>
        private float GetLastReportedDirection()
        {
            // 这里可以存储上次报告的值，简化实现直接返回当前值
            return currentDirection;
        }
        
        /// <summary>
        /// 四舍五入到指定小数位
        /// </summary>
        private float RoundToDecimalPlaces(float value, int decimalPlaces)
        {
            float multiplier = Mathf.Pow(10f, decimalPlaces);
            return Mathf.Round(value * multiplier) / multiplier;
        }
        
        /// <summary>
        /// 设置目标对象
        /// </summary>
        public void SetTarget(Transform target, string type, string id)
        {
            targetTransform = target;
            targetType = type;
            targetId = id;
            
            if (targetTransform != null)
            {
                lastPosition = targetTransform.position;
            }
            
            LogDebug($"设置新目标: {type}:{id}");
        }
        
        /// <summary>
        /// 手动设置工作状态
        /// </summary>
        public void SetWorkingStatus(string status)
        {
            if (workingStatus != status)
            {
                string oldStatus = workingStatus;
                workingStatus = status;
                LogDebug($"手动设置工作状态: {oldStatus} -> {status}");
            }
        }
        
        /// <summary>
        /// 获取运动统计信息
        /// </summary>
        public MotionStatistics GetMotionStatistics()
        {
            return new MotionStatistics
            {
                currentSpeed = currentSpeed,
                currentDirection = currentDirection,
                workingStatus = workingStatus,
                averageSpeed = CalculateAverageSpeed(),
                maxSpeed = CalculateMaxSpeed()
            };
        }
        
        /// <summary>
        /// 计算平均速度
        /// </summary>
        private float CalculateAverageSpeed()
        {
            float totalSpeed = 0f;
            int validSamples = 0;
            
            for (int i = 0; i < velocityHistorySize; i++)
            {
                if (velocityHistory[i] != Vector3.zero || !isFirstUpdate)
                {
                    totalSpeed += velocityHistory[i].magnitude;
                    validSamples++;
                }
            }
            
            return validSamples > 0 ? totalSpeed / validSamples : 0f;
        }
        
        /// <summary>
        /// 计算最大速度
        /// </summary>
        private float CalculateMaxSpeed()
        {
            float maxSpeed = 0f;
            
            for (int i = 0; i < velocityHistorySize; i++)
            {
                float speed = velocityHistory[i].magnitude;
                if (speed > maxSpeed)
                {
                    maxSpeed = speed;
                }
            }
            
            return maxSpeed;
        }
        
        /// <summary>
        /// 重置运动数据
        /// </summary>
        public void ResetMotionData()
        {
            currentVelocity = Vector3.zero;
            currentSpeed = 0f;
            currentDirection = 0f;
            
            for (int i = 0; i < velocityHistorySize; i++)
            {
                velocityHistory[i] = Vector3.zero;
            }
            
            velocityHistoryIndex = 0;
            isFirstUpdate = true;
            
            LogDebug("运动数据已重置");
        }
        
        public override bool ValidateConfiguration()
        {
            if (!base.ValidateConfiguration()) return false;
            
            if (targetTransform == null)
            {
                LogError("目标对象不能为空");
                return false;
            }
            
            if (idleSpeedThreshold < 0 || walkingSpeedThreshold < 0 || runningSpeedThreshold < 0)
            {
                LogError("速度阈值不能为负数");
                return false;
            }
            
            if (walkingSpeedThreshold <= idleSpeedThreshold)
            {
                LogError("步行速度阈值必须大于静止速度阈值");
                return false;
            }
            
            if (runningSpeedThreshold <= walkingSpeedThreshold)
            {
                LogError("跑步速度阈值必须大于步行速度阈值");
                return false;
            }
            
            if (velocityHistorySize <= 0)
            {
                LogError("速度历史记录大小必须大于0");
                return false;
            }
            
            return true;
        }
        
        /// <summary>
        /// 绘制调试信息
        /// </summary>
        private void OnDrawGizmos()
        {
            if (targetTransform == null) return;
            
            // 绘制目标位置
            Gizmos.color = Color.cyan;
            Gizmos.DrawWireSphere(targetTransform.position, 0.2f);
            
            // 绘制速度向量
            if (currentSpeed > idleSpeedThreshold)
            {
                Gizmos.color = Color.red;
                Vector3 velocityVisualization = currentVelocity.normalized * Mathf.Min(currentSpeed, 3f);
                Gizmos.DrawRay(targetTransform.position, velocityVisualization);
                
                // 绘制速度大小指示
                Gizmos.color = Color.yellow;
                Gizmos.DrawWireSphere(targetTransform.position + velocityVisualization, 0.1f);
            }
            
            // 绘制传感器位置
            if (targetTransform != transform)
            {
                Gizmos.color = Color.green;
                Gizmos.DrawWireSphere(transform.position, 0.1f);
                Gizmos.DrawLine(transform.position, targetTransform.position);
            }
        }
    }
    
    /// <summary>
    /// 运动统计信息
    /// </summary>
    [System.Serializable]
    public class MotionStatistics
    {
        public float currentSpeed;
        public float currentDirection;
        public string workingStatus;
        public float averageSpeed;
        public float maxSpeed;
    }
}
