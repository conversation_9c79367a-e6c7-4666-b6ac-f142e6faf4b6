using System;
using System.Collections.Generic;
using System.Threading;
using Cysharp.Threading.Tasks;
using UnityEngine;
using Simulation.IoTSystem;
using Simulation.Data;

namespace Simulation.Simulators
{
    /// <summary>
    /// 环境监测模拟器
    /// 模拟噪声和扬尘监测功能
    /// </summary>
    public class EnvironmentalMonitorSimulator : SimulatorBase
    {
        [Header("监测站基础信息")]
        [SerializeField] private string stationId = "env_monitor_001";
        [SerializeField] private string stationName = "主环境监测站";
        [SerializeField] private string location = "工地中心";
        [SerializeField] private MonitorType monitorTypes = MonitorType.Noise | MonitorType.Dust;
        
        [Header("噪声监测配置")]
        [SerializeField] private bool enableNoiseMonitoring = true;
        [SerializeField] private float noiseThreshold = 70f; // 噪声阈值（dB）
        [SerializeField] private float baseNoiseLevel = 45f; // 基础噪声等级（dB）
        [SerializeField] private float maxNoiseLevel = 90f; // 最大噪声等级（dB）
        [SerializeField] private float noiseVariation = 5f; // 噪声变化幅度
        
        [Header("扬尘监测配置")]
        [SerializeField] private bool enableDustMonitoring = true;
        [SerializeField] private float dustThreshold = 150f; // 扬尘阈值（μg/m³）
        [SerializeField] private float baseDustLevel = 50f; // 基础扬尘浓度（μg/m³）
        [SerializeField] private float maxDustLevel = 300f; // 最大扬尘浓度（μg/m³）
        [SerializeField] private float dustVariation = 20f; // 扬尘变化幅度
        
        [Header("监测配置")]
        [SerializeField] private float monitoringInterval = 30f; // 监测间隔（秒）
        [SerializeField] private float alertCheckInterval = 60f; // 报警检查间隔（秒）
        [SerializeField] private float detectionRadius = 100f; // 检测半径（米）
        [SerializeField] private LayerMask sourceDetectionLayers = -1; // 污染源检测层
        
        [Header("数据记录配置")]
        [SerializeField] private bool enableDataLogging = true;
        [SerializeField] private int maxDataRecords = 1000; // 最大数据记录数
        [SerializeField] private bool enableTrendAnalysis = true; // 启用趋势分析
        [SerializeField] private int trendAnalysisWindow = 10; // 趋势分析窗口
        
        [Header("报警配置")]
        [SerializeField] private bool enableAlerts = true;
        [SerializeField] private float alertCooldown = 300f; // 报警冷却时间（秒）
        [SerializeField] private int consecutiveThresholdCount = 3; // 连续超标次数触发报警
        
        [Header("调试配置")]
        [SerializeField] private bool enableDebugLog = true;
        [SerializeField] private bool showDetectionGizmos = true;
        [SerializeField] private Color detectionColor = Color.green;
        
        // 监测状态
        private MonitoringState currentState = MonitoringState.Monitoring;
        private float currentNoiseLevel = 0f;
        private float currentDustLevel = 0f;
        private DateTime lastAlertTime = DateTime.MinValue;
        
        // 数据记录
        private Queue<EnvironmentalData> dataHistory = new Queue<EnvironmentalData>();
        private List<PollutionSource> detectedSources = new List<PollutionSource>();
        
        // 统计数据
        private int totalMeasurements = 0;
        private int noiseAlertCount = 0;
        private int dustAlertCount = 0;
        private float maxRecordedNoise = 0f;
        private float maxRecordedDust = 0f;
        private int consecutiveNoiseExceeds = 0;
        private int consecutiveDustExceeds = 0;
        
        // 公共属性
        public string StationId => stationId;
        public MonitoringState CurrentState => currentState;
        public float CurrentNoiseLevel => currentNoiseLevel;
        public float CurrentDustLevel => currentDustLevel;
        public int TotalMeasurements => totalMeasurements;
        
        protected override async UniTask OnStartSimulationAsync(CancellationToken cancellationToken)
        {
            LogDebug("环境监测模拟器启动");
            
            // 初始化状态
            currentState = MonitoringState.Monitoring;
            currentNoiseLevel = baseNoiseLevel;
            currentDustLevel = baseDustLevel;
            
            // 发送监测站启动事件
            SendMonitorEvent("monitor_start", "环境监测启动", $"监测站 {stationName} 开始监测");
            
            // 启动监测循环
            MonitoringLoop(cancellationToken).Forget();
            
            // 启动报警检查
            if (enableAlerts)
            {
                AlertCheckLoop(cancellationToken).Forget();
            }
            
            // 启动污染源检测
            SourceDetectionLoop(cancellationToken).Forget();
        }
        
        protected override void OnSimulationUpdate(float deltaTime)
        {
            // 更新环境数据
            UpdateEnvironmentalLevels(deltaTime);
            
            // 清理过期数据
            CleanupOldData();
        }
        
        protected override UniTask OnStopSimulationAsync()
        {
            LogDebug("环境监测模拟器停止");
            
            // 发送监测站停止事件
            SendMonitorEvent("monitor_stop", "环境监测停止", 
                $"监测站 {stationName} 停止监测，共记录{totalMeasurements}次数据，噪声报警{noiseAlertCount}次，扬尘报警{dustAlertCount}次");
            
            return UniTask.CompletedTask;
        }
        
        /// <summary>
        /// 监测循环
        /// </summary>
        private async UniTaskVoid MonitoringLoop(CancellationToken cancellationToken)
        {
            try
            {
                while (!cancellationToken.IsCancellationRequested)
                {
                    await UniTask.Delay(TimeSpan.FromSeconds(monitoringInterval), cancellationToken: cancellationToken);
                    
                    // 执行监测
                    PerformMeasurement();
                }
            }
            catch (OperationCanceledException)
            {
                LogDebug("监测循环被取消");
            }
        }
        
        /// <summary>
        /// 报警检查循环
        /// </summary>
        private async UniTaskVoid AlertCheckLoop(CancellationToken cancellationToken)
        {
            try
            {
                while (!cancellationToken.IsCancellationRequested)
                {
                    await UniTask.Delay(TimeSpan.FromSeconds(alertCheckInterval), cancellationToken: cancellationToken);
                    
                    // 检查报警条件
                    CheckAlertConditions();
                }
            }
            catch (OperationCanceledException)
            {
                LogDebug("报警检查循环被取消");
            }
        }
        
        /// <summary>
        /// 污染源检测循环
        /// </summary>
        private async UniTaskVoid SourceDetectionLoop(CancellationToken cancellationToken)
        {
            try
            {
                while (!cancellationToken.IsCancellationRequested)
                {
                    await UniTask.Delay(TimeSpan.FromSeconds(monitoringInterval * 2), cancellationToken: cancellationToken);
                    
                    // 检测污染源
                    DetectPollutionSources();
                }
            }
            catch (OperationCanceledException)
            {
                LogDebug("污染源检测循环被取消");
            }
        }
        
        /// <summary>
        /// 执行测量
        /// </summary>
        private void PerformMeasurement()
        {
            totalMeasurements++;
            
            // 记录数据
            var data = new EnvironmentalData
            {
                timestamp = DateTime.UtcNow,
                noiseLevel = currentNoiseLevel,
                dustLevel = currentDustLevel,
                stationId = stationId,
                location = transform.position
            };
            
            // 添加到历史记录
            dataHistory.Enqueue(data);
            
            // 限制历史记录数量
            while (dataHistory.Count > maxDataRecords)
            {
                dataHistory.Dequeue();
            }
            
            // 更新统计
            maxRecordedNoise = Mathf.Max(maxRecordedNoise, currentNoiseLevel);
            maxRecordedDust = Mathf.Max(maxRecordedDust, currentDustLevel);
            
            LogDebug($"监测数据: 噪声 {currentNoiseLevel:F1}dB, 扬尘 {currentDustLevel:F1}μg/m³");
            
            // 发送监测数据事件
            SendMeasurementEvent(data);
        }
        
        /// <summary>
        /// 更新环境数据
        /// </summary>
        private void UpdateEnvironmentalLevels(float deltaTime)
        {
            // 基础随机变化
            float noiseVariationValue = UnityEngine.Random.Range(-noiseVariation, noiseVariation) * deltaTime;
            float dustVariationValue = UnityEngine.Random.Range(-dustVariation, dustVariation) * deltaTime;
            
            // 根据检测到的污染源调整
            float sourceNoiseInfluence = 0f;
            float sourceDustInfluence = 0f;
            
            foreach (var source in detectedSources)
            {
                float distance = Vector3.Distance(transform.position, source.position);
                float influence = Mathf.Max(0f, 1f - distance / detectionRadius);
                
                sourceNoiseInfluence += source.noiseContribution * influence;
                sourceDustInfluence += source.dustContribution * influence;
            }
            
            // 更新噪声等级
            if (enableNoiseMonitoring)
            {
                currentNoiseLevel = Mathf.Clamp(
                    baseNoiseLevel + noiseVariationValue + sourceNoiseInfluence,
                    baseNoiseLevel * 0.8f,
                    maxNoiseLevel
                );
            }
            
            // 更新扬尘等级
            if (enableDustMonitoring)
            {
                currentDustLevel = Mathf.Clamp(
                    baseDustLevel + dustVariationValue + sourceDustInfluence,
                    baseDustLevel * 0.5f,
                    maxDustLevel
                );
            }
        }
        
        /// <summary>
        /// 检测污染源
        /// </summary>
        private void DetectPollutionSources()
        {
            detectedSources.Clear();
            
            Collider[] colliders = Physics.OverlapSphere(transform.position, detectionRadius, sourceDetectionLayers);
            
            foreach (var collider in colliders)
            {
                var source = AnalyzePollutionSource(collider);
                if (source != null)
                {
                    detectedSources.Add(source);
                }
            }
            
            LogDebug($"检测到 {detectedSources.Count} 个污染源");
        }
        
        /// <summary>
        /// 分析污染源
        /// </summary>
        private PollutionSource AnalyzePollutionSource(Collider collider)
        {
            // 检查塔吊
            var craneSimulator = collider.GetComponent<CraneSimulator>();
            if (craneSimulator != null && craneSimulator.CurrentState != CraneState.Idle)
            {
                return new PollutionSource
                {
                    sourceId = craneSimulator.CraneId,
                    sourceType = "crane",
                    position = collider.transform.position,
                    noiseContribution = 15f,
                    dustContribution = 10f
                };
            }
            
            // 检查车辆
            var vehicleSimulator = collider.GetComponent<VehicleSimulator>();
            if (vehicleSimulator != null && vehicleSimulator.CurrentSpeed > 0.1f)
            {
                return new PollutionSource
                {
                    sourceId = vehicleSimulator.VehicleId,
                    sourceType = "vehicle",
                    position = collider.transform.position,
                    noiseContribution = 10f,
                    dustContribution = 15f
                };
            }
            
            // 检查升降机
            var elevatorSimulator = collider.GetComponent<ElevatorSimulator>();
            if (elevatorSimulator != null && elevatorSimulator.CurrentState != ElevatorState.Idle)
            {
                return new PollutionSource
                {
                    sourceId = elevatorSimulator.ElevatorId,
                    sourceType = "elevator",
                    position = collider.transform.position,
                    noiseContribution = 8f,
                    dustContribution = 5f
                };
            }
            
            return null;
        }
        
        /// <summary>
        /// 检查报警条件
        /// </summary>
        private void CheckAlertConditions()
        {
            bool noiseExceeded = enableNoiseMonitoring && currentNoiseLevel > noiseThreshold;
            bool dustExceeded = enableDustMonitoring && currentDustLevel > dustThreshold;
            
            // 更新连续超标计数
            if (noiseExceeded)
            {
                consecutiveNoiseExceeds++;
            }
            else
            {
                consecutiveNoiseExceeds = 0;
            }
            
            if (dustExceeded)
            {
                consecutiveDustExceeds++;
            }
            else
            {
                consecutiveDustExceeds = 0;
            }
            
            // 检查是否需要触发报警
            bool shouldAlert = (DateTime.UtcNow - lastAlertTime).TotalSeconds > alertCooldown;
            
            if (shouldAlert)
            {
                if (consecutiveNoiseExceeds >= consecutiveThresholdCount)
                {
                    TriggerNoiseAlert();
                }
                
                if (consecutiveDustExceeds >= consecutiveThresholdCount)
                {
                    TriggerDustAlert();
                }
            }
        }
        
        /// <summary>
        /// 触发噪声报警
        /// </summary>
        private void TriggerNoiseAlert()
        {
            noiseAlertCount++;
            lastAlertTime = DateTime.UtcNow;
            consecutiveNoiseExceeds = 0;
            
            LogDebug($"噪声报警: {currentNoiseLevel:F1}dB 超过阈值 {noiseThreshold}dB");
            
            // 发送噪声报警事件
            SendAlertEvent("noise_alert", "噪声超标报警", 
                $"监测站 {stationName} 检测到噪声 {currentNoiseLevel:F1}dB 超过阈值 {noiseThreshold}dB");
        }
        
        /// <summary>
        /// 触发扬尘报警
        /// </summary>
        private void TriggerDustAlert()
        {
            dustAlertCount++;
            lastAlertTime = DateTime.UtcNow;
            consecutiveDustExceeds = 0;
            
            LogDebug($"扬尘报警: {currentDustLevel:F1}μg/m³ 超过阈值 {dustThreshold}μg/m³");
            
            // 发送扬尘报警事件
            SendAlertEvent("dust_alert", "扬尘超标报警", 
                $"监测站 {stationName} 检测到扬尘 {currentDustLevel:F1}μg/m³ 超过阈值 {dustThreshold}μg/m³");
        }
        
        /// <summary>
        /// 清理过期数据
        /// </summary>
        private void CleanupOldData()
        {
            DateTime cutoffTime = DateTime.UtcNow.AddHours(-24); // 保留24小时数据
            
            while (dataHistory.Count > 0 && dataHistory.Peek().timestamp < cutoffTime)
            {
                dataHistory.Dequeue();
            }
        }
        
        /// <summary>
        /// 发送监测事件
        /// </summary>
        private void SendMonitorEvent(string eventType, string title, string description)
        {
            var eventData = new StatusEventData
            {
                equipmentId = stationId,
                equipmentType = "environmental_monitor",
                currentStatus = currentState.ToString(),
                sourceId = stationId,
                title = title,
                description = description
            };
            
            IoTSystem.IoTSystem.Instance?.SendIOTEvent(stationId, eventType, title, description, eventData);
        }
        
        /// <summary>
        /// 发送测量事件
        /// </summary>
        private void SendMeasurementEvent(EnvironmentalData data)
        {
            var measurementEvent = new IoTEventData
            {
                eventType = "environmental_measurement",
                sourceId = stationId,
                title = "环境监测数据",
                description = $"噪声: {data.noiseLevel:F1}dB, 扬尘: {data.dustLevel:F1}μg/m³",
                data = new 
                { 
                    noiseLevel = data.noiseLevel,
                    dustLevel = data.dustLevel,
                    timestamp = data.timestamp,
                    location = new { x = data.location.x, y = data.location.y, z = data.location.z }
                }
            };
            
            IoTSystem.IoTSystem.Instance?.SendIOTEvent(stationId, "environmental_measurement", "环境监测数据", measurementEvent.description, measurementEvent);
        }
        
        /// <summary>
        /// 发送报警事件
        /// </summary>
        private void SendAlertEvent(string alertType, string title, string description)
        {
            var alertEvent = new SafetyEventData
            {
                violationType = alertType,
                sourceId = stationId,
                title = title,
                description = description,
                severity = "warning"
            };
            
            alertEvent.location.SetPosition(transform.position);
            
            IoTSystem.IoTSystem.Instance?.SendIOTEvent(stationId, "environmental_alert", title, description, alertEvent);
        }
        
        /// <summary>
        /// 调试日志
        /// </summary>
        private void LogDebug(string message)
        {
            if (enableDebugLog)
            {
                Debug.Log($"[EnvironmentalMonitor:{stationId}] {message}");
            }
        }
        
        /// <summary>
        /// 绘制调试信息
        /// </summary>
        private void OnDrawGizmos()
        {
            if (!showDetectionGizmos) return;
            
            // 绘制检测范围
            Gizmos.color = detectionColor;
            Gizmos.DrawWireSphere(transform.position, detectionRadius);
            
            // 绘制监测站
            Gizmos.color = Color.blue;
            Gizmos.DrawWireCube(transform.position, Vector3.one * 2f);
            
            // 绘制污染源
            if (detectedSources != null)
            {
                Gizmos.color = Color.red;
                foreach (var source in detectedSources)
                {
                    Gizmos.DrawWireSphere(source.position, 1f);
                    Gizmos.DrawLine(transform.position, source.position);
                }
            }
            
            // 绘制状态指示
            Color statusColor = currentState == MonitoringState.Monitoring ? Color.green : Color.yellow;
            Gizmos.color = statusColor;
            Gizmos.DrawWireCube(transform.position + Vector3.up * 3f, Vector3.one * 0.5f);
        }
    }
    
    /// <summary>
    /// 监测状态枚举
    /// </summary>
    public enum MonitoringState
    {
        Monitoring,     // 监测中
        Calibrating,    // 校准中
        Maintenance,    // 维护中
        Error          // 错误状态
    }
    
    /// <summary>
    /// 监测类型标志
    /// </summary>
    [Flags]
    public enum MonitorType
    {
        Noise = 1,      // 噪声监测
        Dust = 2,       // 扬尘监测
        Air = 4,        // 空气质量监测
        Weather = 8     // 气象监测
    }
    
    /// <summary>
    /// 环境数据
    /// </summary>
    [Serializable]
    public class EnvironmentalData
    {
        public DateTime timestamp;
        public float noiseLevel;
        public float dustLevel;
        public string stationId;
        public Vector3 location;
    }
    
    /// <summary>
    /// 污染源数据
    /// </summary>
    [Serializable]
    public class PollutionSource
    {
        public string sourceId;
        public string sourceType;
        public Vector3 position;
        public float noiseContribution;
        public float dustContribution;
    }
}
