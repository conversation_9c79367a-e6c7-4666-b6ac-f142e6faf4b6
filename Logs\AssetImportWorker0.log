Using pre-set license
Built from '6000.1/staging' branch; Version is '6000.1.7f1 (13a8ffad9172) revision 1288447'; Using compiler version '194234433'; Build Type 'Release'
OS: 'Windows 11  (10.0.22631) 64bit Professional' Language: 'zh' Physical Memory: 32673 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1
Date: 2025-06-30T16:56:42Z

COMMAND LINE ARGUMENTS:
E:\Unity\6000.1.7f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker0
-projectPath
F:/LongProjects/General Editor
-logFile
Logs/AssetImportWorker0.log
-srvPort
60168
-job-worker-count
3
-background-job-worker-count
8
-gc-helper-count
1
-name
AssetImport
Successfully changed project path to: F:/LongProjects/General Editor
F:/LongProjects/General Editor
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [7616]  Target information:

Player connection [7616]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 269959102 [EditorId] 269959102 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-D9EUJSV) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [7616] Host joined multi-casting on [***********:54997]...
Player connection [7616] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 3
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 193.65 ms, found 9 plugins.
Preloading 2 native plugins for Editor in 1.69 ms.
Initialize engine version: 6000.1.7f1 (13a8ffad9172)
[Subsystems] Discovering subsystems at path E:/Unity/6000.1.7f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path F:/LongProjects/General Editor/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:         Direct3D 12 [level 12.1]
    Renderer:        NVIDIA GeForce RTX 2070 SUPER (ID=0x1e84)
    Vendor:          NVIDIA
    VRAM:            7989 MB
    App VRAM Budget: 7221 MB
    Driver:          32.0.15.7680
Initialize mono
Mono path[0] = 'E:/Unity/6000.1.7f1/Editor/Data/Managed'
Mono path[1] = 'E:/Unity/6000.1.7f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'E:/Unity/6000.1.7f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56532
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: E:/Unity/6000.1.7f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.002689 seconds.
- Loaded All Assemblies, in  0.430 seconds
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.394 seconds
Domain Reload Profiling: 821ms
	BeginReloadAssembly (148ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (41ms)
	RebuildNativeTypeToScriptingClass (13ms)
	initialDomainReloadingComplete (53ms)
	LoadAllAssembliesAndSetupDomain (171ms)
		LoadAssemblies (143ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (168ms)
			TypeCache.Refresh (167ms)
				TypeCache.ScanAssembly (153ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (395ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (326ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (59ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (63ms)
			ProcessInitializeOnLoadAttributes (137ms)
			ProcessInitializeOnLoadMethodAttributes (62ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.566 seconds
Refreshing native plugins compatible for Editor in 117.12 ms, found 9 plugins.
Native extension for WindowsStandalone target not found
Package Manager log level set to [2]
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Refreshing native plugins compatible for Editor in 125.79 ms, found 9 plugins.
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
[MODES] Loading mode Default (0) for mode-current-id-General Editor
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
AssertionException: Error removing KTX for Unity WebGL sub-packages.. Failed fetching installed packages.
Assertion failure. Values are not equal.
Expected: Failure == Success
  at UnityEngine.Assertions.Assert.Fail (System.String message, System.String userMessage) [0x00043] in <7fa9e22a6c4a40e5abbae8599fd0bce3>:0 
  at UnityEngine.Assertions.Assert.AreEqual[T] (T expected, T actual, System.String message, System.Collections.Generic.IEqualityComparer`1[T] comparer) [0x0005f] in <7fa9e22a6c4a40e5abbae8599fd0bce3>:0 
  at UnityEngine.Assertions.Assert.AreEqual[T] (T expected, T actual, System.String message) [0x00009] in <7fa9e22a6c4a40e5abbae8599fd0bce3>:0 
  at SubPackage.SubPackageRemover.GetAllInstalledPackagesAsync (System.Double timeout) [0x000b7] in .\Library\PackageCache\com.unity.cloud.ktx@9cc7165d9d20\Editor\Scripts\SubPackage\SubPackageRemover.cs:122 
  at SubPackage.SubPackageRemover.TryRemoveObsoleteSubPackagesAsync () [0x0002a] in .\Library\PackageCache\com.unity.cloud.ktx@9cc7165d9d20\Editor\Scripts\SubPackage\SubPackageRemover.cs:40 
UnityEngine.DebugLogHandler:Internal_LogException_Injected(Exception, IntPtr)
UnityEngine.DebugLogHandler:Internal_LogException(Exception, Object)
UnityEngine.DebugLogHandler:LogException(Exception, Object)
UnityEngine.Logger:LogException(Exception, Object)
UnityEngine.Debug:LogException(Exception)
SubPackage.<TryRemoveObsoleteSubPackagesAsync>d__5:MoveNext() (at .\Library\PackageCache\com.unity.cloud.ktx@9cc7165d9d20\Editor\Scripts\SubPackage\SubPackageRemover.cs:57)
System.Runtime.CompilerServices.AsyncTaskMethodBuilder:Start(<TryRemoveObsoleteSubPackagesAsync>d__5&)
SubPackage.SubPackageRemover:TryRemoveObsoleteSubPackagesAsync()
System.Reflection.RuntimeMethodInfo:InternalInvoke(RuntimeMethodInfo, Object, Object[], Exception&)
System.Reflection.RuntimeMethodInfo:Invoke(Object, BindingFlags, Binder, Object[], CultureInfo)
System.Reflection.MethodBase:Invoke(Object, Object[])
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes()

(Filename: ./Library/PackageCache/com.unity.cloud.ktx@9cc7165d9d20/Editor/Scripts/SubPackage/SubPackageRemover.cs Line: 57)

[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
AssertionException: Error removing Draco for Unity WebGL sub-packages.. Failed fetching installed packages.
Assertion failure. Values are not equal.
Expected: Failure == Success
  at UnityEngine.Assertions.Assert.Fail (System.String message, System.String userMessage) [0x00043] in <7fa9e22a6c4a40e5abbae8599fd0bce3>:0 
  at UnityEngine.Assertions.Assert.AreEqual[T] (T expected, T actual, System.String message, System.Collections.Generic.IEqualityComparer`1[T] comparer) [0x0005f] in <7fa9e22a6c4a40e5abbae8599fd0bce3>:0 
  at UnityEngine.Assertions.Assert.AreEqual[T] (T expected, T actual, System.String message) [0x00009] in <7fa9e22a6c4a40e5abbae8599fd0bce3>:0 
  at SubPackage.SubPackageRemover.GetAllInstalledPackagesAsync (System.Double timeout) [0x000b7] in .\Library\PackageCache\com.unity.cloud.draco@2ed1ef9a2677\Editor\Scripts\SubPackage\SubPackageRemover.cs:122 
  at SubPackage.SubPackageRemover.TryRemoveObsoleteSubPackagesAsync () [0x0002a] in .\Library\PackageCache\com.unity.cloud.draco@2ed1ef9a2677\Editor\Scripts\SubPackage\SubPackageRemover.cs:40 
UnityEngine.DebugLogHandler:Internal_LogException_Injected(Exception, IntPtr)
UnityEngine.DebugLogHandler:Internal_LogException(Exception, Object)
UnityEngine.DebugLogHandler:LogException(Exception, Object)
UnityEngine.Logger:LogException(Exception, Object)
UnityEngine.Debug:LogException(Exception)
SubPackage.<TryRemoveObsoleteSubPackagesAsync>d__5:MoveNext() (at .\Library\PackageCache\com.unity.cloud.draco@2ed1ef9a2677\Editor\Scripts\SubPackage\SubPackageRemover.cs:57)
System.Runtime.CompilerServices.AsyncTaskMethodBuilder:Start(<TryRemoveObsoleteSubPackagesAsync>d__5&)
SubPackage.SubPackageRemover:TryRemoveObsoleteSubPackagesAsync()
System.Reflection.RuntimeMethodInfo:InternalInvoke(RuntimeMethodInfo, Object, Object[], Exception&)
System.Reflection.RuntimeMethodInfo:Invoke(Object, BindingFlags, Binder, Object[], CultureInfo)
System.Reflection.MethodBase:Invoke(Object, Object[])
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes()

(Filename: ./Library/PackageCache/com.unity.cloud.draco@2ed1ef9a2677/Editor/Scripts/SubPackage/SubPackageRemover.cs Line: 57)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.462 seconds
Domain Reload Profiling: 3017ms
	BeginReloadAssembly (245ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (32ms)
	RebuildCommonClasses (46ms)
	RebuildNativeTypeToScriptingClass (14ms)
	initialDomainReloadingComplete (93ms)
	LoadAllAssembliesAndSetupDomain (1157ms)
		LoadAssemblies (629ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (653ms)
			TypeCache.Refresh (523ms)
				TypeCache.ScanAssembly (494ms)
			BuildScriptInfoCaches (98ms)
			ResolveRequiredComponents (28ms)
	FinalizeReload (1463ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1205ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (213ms)
			ProcessInitializeOnLoadAttributes (742ms)
			ProcessInitializeOnLoadMethodAttributes (234ms)
			AfterProcessingInitializeOnLoad (8ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
WARNING: Shader Unsupported: 'Hidden/PointCloud_GL' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/PointCloud_GL' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Launched and connected shader compiler UnityShaderCompiler.exe after 0.02 seconds
Refreshing native plugins compatible for Editor in 127.92 ms, found 9 plugins.
Preloading 2 native plugins for Editor in 0.13 ms.
Unloading 407 Unused Serialized files (Serialized files now loaded: 0)
Unloading 11040 unused Assets / (7.7 MB). Loaded Objects now: 11599.
Memory consumption went from 289.4 MB to 281.7 MB.
Total: 13.457100 ms (FindLiveObjects: 1.127700 ms CreateObjectMapping: 2.170100 ms MarkObjects: 6.303600 ms  DeleteObjects: 3.854700 ms)

========================================================================
Received Import Request.
  Time since last request: 57227.940500 seconds.
  path: Assets/Simulation/Scripts/Simulators
  artifactKey: Guid(db499bbc4bda2f44782d4a18e30ca28c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Simulation/Scripts/Simulators using Guid(db499bbc4bda2f44782d4a18e30ca28c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6d86353026ddf3a22920cf29e1ae53ad') in 0.0089962 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.620 seconds
Refreshing native plugins compatible for Editor in 103.72 ms, found 9 plugins.
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
AssertionException: Error removing KTX for Unity WebGL sub-packages.. Failed fetching installed packages.
Assertion failure. Values are not equal.
Expected: Failure == Success
  at UnityEngine.Assertions.Assert.Fail (System.String message, System.String userMessage) [0x00043] in <7fa9e22a6c4a40e5abbae8599fd0bce3>:0 
  at UnityEngine.Assertions.Assert.AreEqual[T] (T expected, T actual, System.String message, System.Collections.Generic.IEqualityComparer`1[T] comparer) [0x0005f] in <7fa9e22a6c4a40e5abbae8599fd0bce3>:0 
  at UnityEngine.Assertions.Assert.AreEqual[T] (T expected, T actual, System.String message) [0x00009] in <7fa9e22a6c4a40e5abbae8599fd0bce3>:0 
  at SubPackage.SubPackageRemover.GetAllInstalledPackagesAsync (System.Double timeout) [0x000da] in .\Library\PackageCache\com.unity.cloud.ktx@9cc7165d9d20\Editor\Scripts\SubPackage\SubPackageRemover.cs:122 
  at SubPackage.SubPackageRemover.TryRemoveObsoleteSubPackagesAsync () [0x00038] in .\Library\PackageCache\com.unity.cloud.ktx@9cc7165d9d20\Editor\Scripts\SubPackage\SubPackageRemover.cs:40 
UnityEngine.DebugLogHandler:Internal_LogException_Injected(Exception, IntPtr)
UnityEngine.DebugLogHandler:Internal_LogException(Exception, Object)
UnityEngine.DebugLogHandler:LogException(Exception, Object)
UnityEngine.Logger:LogException(Exception, Object)
UnityEngine.Debug:LogException(Exception)
SubPackage.<TryRemoveObsoleteSubPackagesAsync>d__5:MoveNext() (at .\Library\PackageCache\com.unity.cloud.ktx@9cc7165d9d20\Editor\Scripts\SubPackage\SubPackageRemover.cs:57)
System.Runtime.CompilerServices.AsyncTaskMethodBuilder:Start(<TryRemoveObsoleteSubPackagesAsync>d__5&)
SubPackage.SubPackageRemover:TryRemoveObsoleteSubPackagesAsync()
System.Reflection.RuntimeMethodInfo:InternalInvoke(RuntimeMethodInfo, Object, Object[], Exception&)
System.Reflection.RuntimeMethodInfo:Invoke(Object, BindingFlags, Binder, Object[], CultureInfo)
System.Reflection.MethodBase:Invoke(Object, Object[])
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes()

(Filename: ./Library/PackageCache/com.unity.cloud.ktx@9cc7165d9d20/Editor/Scripts/SubPackage/SubPackageRemover.cs Line: 57)

[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
AssertionException: Error removing Draco for Unity WebGL sub-packages.. Failed fetching installed packages.
Assertion failure. Values are not equal.
Expected: Failure == Success
  at UnityEngine.Assertions.Assert.Fail (System.String message, System.String userMessage) [0x00043] in <7fa9e22a6c4a40e5abbae8599fd0bce3>:0 
  at UnityEngine.Assertions.Assert.AreEqual[T] (T expected, T actual, System.String message, System.Collections.Generic.IEqualityComparer`1[T] comparer) [0x0005f] in <7fa9e22a6c4a40e5abbae8599fd0bce3>:0 
  at UnityEngine.Assertions.Assert.AreEqual[T] (T expected, T actual, System.String message) [0x00009] in <7fa9e22a6c4a40e5abbae8599fd0bce3>:0 
  at SubPackage.SubPackageRemover.GetAllInstalledPackagesAsync (System.Double timeout) [0x000da] in .\Library\PackageCache\com.unity.cloud.draco@2ed1ef9a2677\Editor\Scripts\SubPackage\SubPackageRemover.cs:122 
  at SubPackage.SubPackageRemover.TryRemoveObsoleteSubPackagesAsync () [0x00038] in .\Library\PackageCache\com.unity.cloud.draco@2ed1ef9a2677\Editor\Scripts\SubPackage\SubPackageRemover.cs:40 
UnityEngine.DebugLogHandler:Internal_LogException_Injected(Exception, IntPtr)
UnityEngine.DebugLogHandler:Internal_LogException(Exception, Object)
UnityEngine.DebugLogHandler:LogException(Exception, Object)
UnityEngine.Logger:LogException(Exception, Object)
UnityEngine.Debug:LogException(Exception)
SubPackage.<TryRemoveObsoleteSubPackagesAsync>d__5:MoveNext() (at .\Library\PackageCache\com.unity.cloud.draco@2ed1ef9a2677\Editor\Scripts\SubPackage\SubPackageRemover.cs:57)
System.Runtime.CompilerServices.AsyncTaskMethodBuilder:Start(<TryRemoveObsoleteSubPackagesAsync>d__5&)
SubPackage.SubPackageRemover:TryRemoveObsoleteSubPackagesAsync()
System.Reflection.RuntimeMethodInfo:InternalInvoke(RuntimeMethodInfo, Object, Object[], Exception&)
System.Reflection.RuntimeMethodInfo:Invoke(Object, BindingFlags, Binder, Object[], CultureInfo)
System.Reflection.MethodBase:Invoke(Object, Object[])
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes()

(Filename: ./Library/PackageCache/com.unity.cloud.draco@2ed1ef9a2677/Editor/Scripts/SubPackage/SubPackageRemover.cs Line: 57)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.172 seconds
Domain Reload Profiling: 2776ms
	BeginReloadAssembly (344ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (90ms)
	RebuildCommonClasses (41ms)
	RebuildNativeTypeToScriptingClass (16ms)
	initialDomainReloadingComplete (85ms)
	LoadAllAssembliesAndSetupDomain (1116ms)
		LoadAssemblies (657ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (578ms)
			TypeCache.Refresh (253ms)
				TypeCache.ScanAssembly (232ms)
			BuildScriptInfoCaches (288ms)
			ResolveRequiredComponents (30ms)
	FinalizeReload (1172ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (960ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (223ms)
			ProcessInitializeOnLoadAttributes (506ms)
			ProcessInitializeOnLoadMethodAttributes (217ms)
			AfterProcessingInitializeOnLoad (7ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (18ms)
WARNING: Shader Unsupported: 'Hidden/PointCloud_GL' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/PointCloud_GL' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 102.09 ms, found 9 plugins.
Preloading 2 native plugins for Editor in 0.14 ms.
Unloading 25 Unused Serialized files (Serialized files now loaded: 0)
Unloading 11038 unused Assets / (6.7 MB). Loaded Objects now: 11606.
Memory consumption went from 227.1 MB to 220.3 MB.
Total: 14.308600 ms (FindLiveObjects: 1.348700 ms CreateObjectMapping: 2.176000 ms MarkObjects: 6.538700 ms  DeleteObjects: 4.244300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.387 seconds
Refreshing native plugins compatible for Editor in 118.76 ms, found 9 plugins.
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
AssertionException: Error removing KTX for Unity WebGL sub-packages.. Failed fetching installed packages.
Assertion failure. Values are not equal.
Expected: Failure == Success
  at UnityEngine.Assertions.Assert.Fail (System.String message, System.String userMessage) [0x00043] in <7fa9e22a6c4a40e5abbae8599fd0bce3>:0 
  at UnityEngine.Assertions.Assert.AreEqual[T] (T expected, T actual, System.String message, System.Collections.Generic.IEqualityComparer`1[T] comparer) [0x0005f] in <7fa9e22a6c4a40e5abbae8599fd0bce3>:0 
  at UnityEngine.Assertions.Assert.AreEqual[T] (T expected, T actual, System.String message) [0x00009] in <7fa9e22a6c4a40e5abbae8599fd0bce3>:0 
  at SubPackage.SubPackageRemover.GetAllInstalledPackagesAsync (System.Double timeout) [0x000da] in .\Library\PackageCache\com.unity.cloud.ktx@9cc7165d9d20\Editor\Scripts\SubPackage\SubPackageRemover.cs:122 
  at SubPackage.SubPackageRemover.TryRemoveObsoleteSubPackagesAsync () [0x00038] in .\Library\PackageCache\com.unity.cloud.ktx@9cc7165d9d20\Editor\Scripts\SubPackage\SubPackageRemover.cs:40 
UnityEngine.DebugLogHandler:Internal_LogException_Injected(Exception, IntPtr)
UnityEngine.DebugLogHandler:Internal_LogException(Exception, Object)
UnityEngine.DebugLogHandler:LogException(Exception, Object)
UnityEngine.Logger:LogException(Exception, Object)
UnityEngine.Debug:LogException(Exception)
SubPackage.<TryRemoveObsoleteSubPackagesAsync>d__5:MoveNext() (at .\Library\PackageCache\com.unity.cloud.ktx@9cc7165d9d20\Editor\Scripts\SubPackage\SubPackageRemover.cs:57)
System.Runtime.CompilerServices.AsyncTaskMethodBuilder:Start(<TryRemoveObsoleteSubPackagesAsync>d__5&)
SubPackage.SubPackageRemover:TryRemoveObsoleteSubPackagesAsync()
System.Reflection.RuntimeMethodInfo:InternalInvoke(RuntimeMethodInfo, Object, Object[], Exception&)
System.Reflection.RuntimeMethodInfo:Invoke(Object, BindingFlags, Binder, Object[], CultureInfo)
System.Reflection.MethodBase:Invoke(Object, Object[])
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes()

(Filename: ./Library/PackageCache/com.unity.cloud.ktx@9cc7165d9d20/Editor/Scripts/SubPackage/SubPackageRemover.cs Line: 57)

[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
AssertionException: Error removing Draco for Unity WebGL sub-packages.. Failed fetching installed packages.
Assertion failure. Values are not equal.
Expected: Failure == Success
  at UnityEngine.Assertions.Assert.Fail (System.String message, System.String userMessage) [0x00043] in <7fa9e22a6c4a40e5abbae8599fd0bce3>:0 
  at UnityEngine.Assertions.Assert.AreEqual[T] (T expected, T actual, System.String message, System.Collections.Generic.IEqualityComparer`1[T] comparer) [0x0005f] in <7fa9e22a6c4a40e5abbae8599fd0bce3>:0 
  at UnityEngine.Assertions.Assert.AreEqual[T] (T expected, T actual, System.String message) [0x00009] in <7fa9e22a6c4a40e5abbae8599fd0bce3>:0 
  at SubPackage.SubPackageRemover.GetAllInstalledPackagesAsync (System.Double timeout) [0x000da] in .\Library\PackageCache\com.unity.cloud.draco@2ed1ef9a2677\Editor\Scripts\SubPackage\SubPackageRemover.cs:122 
  at SubPackage.SubPackageRemover.TryRemoveObsoleteSubPackagesAsync () [0x00038] in .\Library\PackageCache\com.unity.cloud.draco@2ed1ef9a2677\Editor\Scripts\SubPackage\SubPackageRemover.cs:40 
UnityEngine.DebugLogHandler:Internal_LogException_Injected(Exception, IntPtr)
UnityEngine.DebugLogHandler:Internal_LogException(Exception, Object)
UnityEngine.DebugLogHandler:LogException(Exception, Object)
UnityEngine.Logger:LogException(Exception, Object)
UnityEngine.Debug:LogException(Exception)
SubPackage.<TryRemoveObsoleteSubPackagesAsync>d__5:MoveNext() (at .\Library\PackageCache\com.unity.cloud.draco@2ed1ef9a2677\Editor\Scripts\SubPackage\SubPackageRemover.cs:57)
System.Runtime.CompilerServices.AsyncTaskMethodBuilder:Start(<TryRemoveObsoleteSubPackagesAsync>d__5&)
SubPackage.SubPackageRemover:TryRemoveObsoleteSubPackagesAsync()
System.Reflection.RuntimeMethodInfo:InternalInvoke(RuntimeMethodInfo, Object, Object[], Exception&)
System.Reflection.RuntimeMethodInfo:Invoke(Object, BindingFlags, Binder, Object[], CultureInfo)
System.Reflection.MethodBase:Invoke(Object, Object[])
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes()

(Filename: ./Library/PackageCache/com.unity.cloud.draco@2ed1ef9a2677/Editor/Scripts/SubPackage/SubPackageRemover.cs Line: 57)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.114 seconds
Domain Reload Profiling: 2493ms
	BeginReloadAssembly (283ms)
		ExecutionOrderSort (2ms)
		DisableScriptedObjects (9ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (74ms)
	RebuildCommonClasses (39ms)
	RebuildNativeTypeToScriptingClass (16ms)
	initialDomainReloadingComplete (75ms)
	LoadAllAssembliesAndSetupDomain (966ms)
		LoadAssemblies (617ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (462ms)
			TypeCache.Refresh (27ms)
				TypeCache.ScanAssembly (6ms)
			BuildScriptInfoCaches (394ms)
			ResolveRequiredComponents (34ms)
	FinalizeReload (1115ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (906ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (182ms)
			ProcessInitializeOnLoadAttributes (525ms)
			ProcessInitializeOnLoadMethodAttributes (185ms)
			AfterProcessingInitializeOnLoad (7ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (19ms)
WARNING: Shader Unsupported: 'Hidden/PointCloud_GL' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/PointCloud_GL' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 119.49 ms, found 9 plugins.
Preloading 2 native plugins for Editor in 0.25 ms.
Unloading 25 Unused Serialized files (Serialized files now loaded: 0)
Unloading 11039 unused Assets / (6.9 MB). Loaded Objects now: 11612.
Memory consumption went from 233.0 MB to 226.1 MB.
Total: 14.928300 ms (FindLiveObjects: 1.318900 ms CreateObjectMapping: 2.605200 ms MarkObjects: 6.536300 ms  DeleteObjects: 4.467200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
WARNING: Shader Unsupported: 'Hidden/PointCloud_GL' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/PointCloud_GL' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 135.37 ms, found 9 plugins.
Preloading 2 native plugins for Editor in 0.17 ms.
Unloading 23 Unused Serialized files (Serialized files now loaded: 0)
Unloading 10656 unused Assets / (7.0 MB). Loaded Objects now: 11613.
Memory consumption went from 236.1 MB to 229.1 MB.
Total: 16.647400 ms (FindLiveObjects: 1.222400 ms CreateObjectMapping: 4.325100 ms MarkObjects: 6.658400 ms  DeleteObjects: 4.440200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 137.018107 seconds.
  path: Assets/Simulation/Scripts/Simulation.asmdef
  artifactKey: Guid(4d8cf6bc911efd64aa72bf708c2ba9f4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Simulation/Scripts/Simulation.asmdef using Guid(4d8cf6bc911efd64aa72bf708c2ba9f4) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'aa612174165ffab04b61c393ee20dbad') in 0.0015926 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 8.068607 seconds.
  path: Assets/Simulation/Scripts/Simulation.asmdef
  artifactKey: Guid(4d8cf6bc911efd64aa72bf708c2ba9f4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Simulation/Scripts/Simulation.asmdef using Guid(4d8cf6bc911efd64aa72bf708c2ba9f4) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '538629b7097a332637cae6040a5e25a4') in 0.0017684 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 34.148437 seconds.
  path: Assets/Simulation/Scripts/Simulation.asmdef
  artifactKey: Guid(4d8cf6bc911efd64aa72bf708c2ba9f4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Simulation/Scripts/Simulation.asmdef using Guid(4d8cf6bc911efd64aa72bf708c2ba9f4) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '42a36948ea7436b4090c51b5be982234') in 0.0006583 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
WARNING: Shader Unsupported: 'Hidden/PointCloud_GL' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/PointCloud_GL' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 119.16 ms, found 9 plugins.
Preloading 2 native plugins for Editor in 0.19 ms.
Unloading 23 Unused Serialized files (Serialized files now loaded: 0)
Unloading 10657 unused Assets / (6.8 MB). Loaded Objects now: 11614.
Memory consumption went from 224.4 MB to 217.6 MB.
Total: 14.401200 ms (FindLiveObjects: 1.190800 ms CreateObjectMapping: 2.459700 ms MarkObjects: 6.486300 ms  DeleteObjects: 4.263300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 36.517960 seconds.
  path: Assets/Simulation/Scripts/Simulation.asmdef
  artifactKey: Guid(4d8cf6bc911efd64aa72bf708c2ba9f4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Simulation/Scripts/Simulation.asmdef using Guid(4d8cf6bc911efd64aa72bf708c2ba9f4) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '8491cfe32d2f3cb41bd1f7bc525846cd') in 0.0025811 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
WARNING: Shader Unsupported: 'Hidden/PointCloud_GL' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/PointCloud_GL' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 127.84 ms, found 9 plugins.
Preloading 2 native plugins for Editor in 0.14 ms.
Unloading 23 Unused Serialized files (Serialized files now loaded: 0)
Unloading 10661 unused Assets / (7.1 MB). Loaded Objects now: 11618.
Memory consumption went from 224.5 MB to 217.4 MB.
Total: 15.748800 ms (FindLiveObjects: 1.163300 ms CreateObjectMapping: 2.518700 ms MarkObjects: 7.753400 ms  DeleteObjects: 4.312400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
WARNING: Shader Unsupported: 'Hidden/PointCloud_GL' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/PointCloud_GL' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 93.12 ms, found 9 plugins.
Preloading 2 native plugins for Editor in 0.13 ms.
Unloading 23 Unused Serialized files (Serialized files now loaded: 0)
Unloading 10664 unused Assets / (7.1 MB). Loaded Objects now: 11621.
Memory consumption went from 230.3 MB to 223.2 MB.
Total: 14.078200 ms (FindLiveObjects: 1.206100 ms CreateObjectMapping: 2.288700 ms MarkObjects: 6.426400 ms  DeleteObjects: 4.155800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 405.975243 seconds.
  path: Assets/Simulation/Scripts/SimulationManager.cs
  artifactKey: Guid(b5ba2bca0409b274589389079c686557) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Simulation/Scripts/SimulationManager.cs using Guid(b5ba2bca0409b274589389079c686557) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'febff1af09c6ca1302b79841c36e3410') in 0.0007626 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

