using System;
using System.Threading;
using Cysharp.Threading.Tasks;
using UnityEngine;
using Simulation.IoTSystem;
using Simulation.Data;

namespace Simulation.Simulators
{
    /// <summary>
    /// 工人模拟器
    /// 模拟工人的日常作业流程和安全违规行为
    /// </summary>
    public class WorkerSimulator : SimulatorBase
    {
        [Header("工人基础信息")]
        [SerializeField] private string workerId = "worker_001";
        [SerializeField] private string workerName = "张三";
        [SerializeField] private string department = "施工部";
        [SerializeField] private string role = "普通工人";
        
        [Header("工作配置")]
        [SerializeField] private float workDayDuration = 480f; // 工作日时长（分钟）
        [SerializeField] private float taskDuration = 30f; // 单个任务时长（分钟）
        [SerializeField] private float breakDuration = 10f; // 休息时长（分钟）
        [SerializeField] private int tasksPerDay = 12; // 每日任务数量
        
        [Header("移动配置")]
        [SerializeField] private float moveSpeed = 2f; // 移动速度（米/秒）
        [SerializeField] private Vector3[] workPositions; // 工作位置点
        [SerializeField] private Vector3 restPosition; // 休息位置
        [SerializeField] private Vector3 entryPosition; // 入口位置
        
        [Header("安全违规配置")]
        [SerializeField] private bool enableSafetyViolations = true;
        [SerializeField] private float violationProbability = 0.1f; // 违规概率（0-1）
        [SerializeField] private float violationCheckInterval = 60f; // 违规检查间隔（秒）
        [SerializeField] private string[] violationTypes = {
            "未佩戴安全帽",
            "未系安全带",
            "违规操作设备",
            "进入危险区域",
            "未穿防护服"
        };
        
        [Header("调试配置")]
        [SerializeField] private bool enableDebugLog = true;
        [SerializeField] private bool showMovementGizmos = true;
        
        // 工人状态
        private WorkerState currentState = WorkerState.Idle;
        private int currentTaskIndex = 0;
        private Vector3 targetPosition;
        private float stateStartTime;
        
        // 工作统计
        private int completedTasks = 0;
        private int totalViolations = 0;
        private float totalWorkTime = 0f;
        
        // 公共属性
        public string WorkerId => workerId;
        public string WorkerName => workerName;
        public WorkerState CurrentState => currentState;
        public int CompletedTasks => completedTasks;
        public int TotalViolations => totalViolations;
        
        protected override async UniTask OnStartSimulationAsync(CancellationToken cancellationToken)
        {
            LogDebug("工人模拟器启动");
            
            // 初始化位置
            if (workPositions == null || workPositions.Length == 0)
            {
                // 如果没有设置工作位置，使用默认位置
                workPositions = new Vector3[]
                {
                    transform.position + Vector3.forward * 5,
                    transform.position + Vector3.right * 5,
                    transform.position + Vector3.back * 5,
                    transform.position + Vector3.left * 5
                };
            }
            
            if (entryPosition == Vector3.zero)
            {
                entryPosition = transform.position;
            }
            
            if (restPosition == Vector3.zero)
            {
                restPosition = transform.position + Vector3.up * 2;
            }
            
            // 设置初始状态
            ChangeState(WorkerState.EnteringWorksite);
            targetPosition = entryPosition;
            
            // 启动安全违规检查
            if (enableSafetyViolations)
            {
                SafetyViolationCheckLoop(cancellationToken).Forget();
            }
            
            // 发送工人进入工地事件
            SendWorkerEvent("worker_entry", "工人进入工地", $"{workerName}开始工作");
        }
        
        protected override void OnSimulationUpdate(float deltaTime)
        {
            totalWorkTime += deltaTime;
            
            switch (currentState)
            {
                case WorkerState.EnteringWorksite:
                    UpdateEnteringWorksite(deltaTime);
                    break;
                case WorkerState.MovingToWork:
                    UpdateMovingToWork(deltaTime);
                    break;
                case WorkerState.Working:
                    UpdateWorking(deltaTime);
                    break;
                case WorkerState.Resting:
                    UpdateResting(deltaTime);
                    break;
                case WorkerState.LeavingWorksite:
                    UpdateLeavingWorksite(deltaTime);
                    break;
            }
        }
        
        protected override async UniTask OnStopSimulationAsync()
        {
            LogDebug("工人模拟器停止");
            
            // 发送工人离开工地事件
            SendWorkerEvent("worker_exit", "工人离开工地", 
                $"{workerName}完成工作，共完成{completedTasks}个任务，发生{totalViolations}次违规");
        }
        
        /// <summary>
        /// 更新进入工地状态
        /// </summary>
        private void UpdateEnteringWorksite(float deltaTime)
        {
            if (MoveTowards(entryPosition, deltaTime))
            {
                LogDebug("工人已进入工地");
                ChangeState(WorkerState.MovingToWork);
                SetNextWorkPosition();
            }
        }
        
        /// <summary>
        /// 更新移动到工作位置状态
        /// </summary>
        private void UpdateMovingToWork(float deltaTime)
        {
            if (MoveTowards(targetPosition, deltaTime))
            {
                LogDebug($"工人到达工作位置 {currentTaskIndex + 1}");
                ChangeState(WorkerState.Working);
            }
        }
        
        /// <summary>
        /// 更新工作状态
        /// </summary>
        private void UpdateWorking(float deltaTime)
        {
            float workTime = Time.time - stateStartTime;
            
            if (workTime >= taskDuration * 60f) // 转换为秒
            {
                completedTasks++;
                LogDebug($"工人完成任务 {completedTasks}");
                
                // 发送任务完成事件
                SendWorkerEvent("task_completed", "任务完成", 
                    $"{workerName}完成第{completedTasks}个任务");
                
                // 检查是否需要休息
                if (completedTasks % 3 == 0) // 每3个任务休息一次
                {
                    ChangeState(WorkerState.Resting);
                    targetPosition = restPosition;
                }
                else if (completedTasks >= tasksPerDay)
                {
                    // 完成所有任务，准备离开
                    ChangeState(WorkerState.LeavingWorksite);
                    targetPosition = entryPosition;
                }
                else
                {
                    // 移动到下一个工作位置
                    ChangeState(WorkerState.MovingToWork);
                    SetNextWorkPosition();
                }
            }
        }
        
        /// <summary>
        /// 更新休息状态
        /// </summary>
        private void UpdateResting(float deltaTime)
        {
            // 移动到休息位置
            if (Vector3.Distance(transform.position, targetPosition) > 0.1f)
            {
                MoveTowards(targetPosition, deltaTime);
                return;
            }
            
            float restTime = Time.time - stateStartTime;
            
            if (restTime >= breakDuration * 60f) // 转换为秒
            {
                LogDebug("工人休息结束");
                
                if (completedTasks >= tasksPerDay)
                {
                    ChangeState(WorkerState.LeavingWorksite);
                    targetPosition = entryPosition;
                }
                else
                {
                    ChangeState(WorkerState.MovingToWork);
                    SetNextWorkPosition();
                }
            }
        }
        
        /// <summary>
        /// 更新离开工地状态
        /// </summary>
        private void UpdateLeavingWorksite(float deltaTime)
        {
            if (MoveTowards(entryPosition, deltaTime))
            {
                LogDebug("工人已离开工地");
                ChangeState(WorkerState.Idle);
                
                // 可以选择重新开始新的工作日
                if (Application.isPlaying)
                {
                    RestartWorkDayAsync().Forget();
                }
            }
        }
        
        /// <summary>
        /// 移动到目标位置
        /// </summary>
        private bool MoveTowards(Vector3 target, float deltaTime)
        {
            Vector3 direction = (target - transform.position).normalized;
            float distance = Vector3.Distance(transform.position, target);
            
            if (distance <= 0.1f)
            {
                transform.position = target;
                return true;
            }
            
            transform.position += direction * moveSpeed * deltaTime;
            
            // 更新朝向
            if (direction != Vector3.zero)
            {
                transform.rotation = Quaternion.LookRotation(direction);
            }
            
            return false;
        }
        
        /// <summary>
        /// 设置下一个工作位置
        /// </summary>
        private void SetNextWorkPosition()
        {
            currentTaskIndex = (currentTaskIndex + 1) % workPositions.Length;
            targetPosition = workPositions[currentTaskIndex];
        }
        
        /// <summary>
        /// 改变工人状态
        /// </summary>
        private void ChangeState(WorkerState newState)
        {
            if (currentState != newState)
            {
                LogDebug($"工人状态变化: {currentState} -> {newState}");
                currentState = newState;
                stateStartTime = Time.time;
                
                // 发送状态变化事件
                SendStatusEvent(currentState.ToString(), newState.ToString());
            }
        }
        
        /// <summary>
        /// 安全违规检查循环
        /// </summary>
        private async UniTaskVoid SafetyViolationCheckLoop(CancellationToken cancellationToken)
        {
            try
            {
                while (!cancellationToken.IsCancellationRequested)
                {
                    await UniTask.Delay(TimeSpan.FromSeconds(violationCheckInterval), cancellationToken: cancellationToken);
                    
                    // 只在工作状态下检查违规
                    if (currentState == WorkerState.Working)
                    {
                        CheckSafetyViolation();
                    }
                }
            }
            catch (OperationCanceledException)
            {
                LogDebug("安全违规检查循环被取消");
            }
        }
        
        /// <summary>
        /// 检查安全违规
        /// </summary>
        private void CheckSafetyViolation()
        {
            if (UnityEngine.Random.Range(0f, 1f) <= violationProbability)
            {
                string violationType = violationTypes[UnityEngine.Random.Range(0, violationTypes.Length)];
                totalViolations++;
                
                LogDebug($"检测到安全违规: {violationType}");
                
                // 发送安全违规事件
                SendSafetyViolationEvent(violationType);
            }
        }
        
        /// <summary>
        /// 重新开始工作日
        /// </summary>
        private async UniTaskVoid RestartWorkDayAsync()
        {
            await UniTask.Delay(TimeSpan.FromSeconds(5)); // 等待5秒后重新开始
            
            completedTasks = 0;
            currentTaskIndex = 0;
            ChangeState(WorkerState.EnteringWorksite);
            targetPosition = entryPosition;
        }
        
        /// <summary>
        /// 发送工人事件
        /// </summary>
        private void SendWorkerEvent(string eventType, string title, string description)
        {
            var eventData = new OperationEventData
            {
                operationType = eventType,
                operatorId = workerId,
                operatorName = workerName,
                targetId = workerId,
                targetType = "worker",
                title = title,
                description = description,
                sourceId = workerId
            };
            
            eventData.location.SetPosition(transform.position);
            
            IoTSystem.IoTSystem.Instance?.SendIOTEvent(workerId, eventType, title, description, eventData);
        }
        
        /// <summary>
        /// 发送状态变化事件
        /// </summary>
        private void SendStatusEvent(string previousStatus, string currentStatus)
        {
            var statusEvent = new StatusEventData
            {
                previousStatus = previousStatus,
                currentStatus = currentStatus,
                equipmentId = workerId,
                equipmentType = "worker",
                sourceId = workerId,
                title = "工人状态变化",
                description = $"{workerName}状态从{previousStatus}变为{currentStatus}"
            };
            
            IoTSystem.IoTSystem.Instance?.SendIOTEvent(workerId, "status_change", "工人状态变化", statusEvent.description, statusEvent);
        }
        
        /// <summary>
        /// 发送安全违规事件
        /// </summary>
        private void SendSafetyViolationEvent(string violationType)
        {
            var safetyEvent = new SafetyEventData
            {
                violationType = violationType,
                workerId = workerId,
                workerName = workerName,
                sourceId = workerId,
                title = "安全违规",
                description = $"{workerName}发生安全违规: {violationType}",
                severity = "warning"
            };
            
            safetyEvent.location.SetPosition(transform.position);
            
            IoTSystem.IoTSystem.Instance?.SendIOTEvent(workerId, "safety_violation", "安全违规", safetyEvent.description, safetyEvent);
        }
        
        /// <summary>
        /// 调试日志
        /// </summary>
        private void LogDebug(string message)
        {
            if (enableDebugLog)
            {
                Debug.Log($"[WorkerSimulator:{workerId}] {message}");
            }
        }
        
        /// <summary>
        /// 绘制调试信息
        /// </summary>
        private void OnDrawGizmos()
        {
            if (!showMovementGizmos) return;
            
            // 绘制工作位置
            if (workPositions != null)
            {
                Gizmos.color = Color.blue;
                foreach (var pos in workPositions)
                {
                    Gizmos.DrawWireSphere(pos, 0.5f);
                }
            }
            
            // 绘制休息位置
            Gizmos.color = Color.green;
            Gizmos.DrawWireSphere(restPosition, 0.5f);
            
            // 绘制入口位置
            Gizmos.color = Color.yellow;
            Gizmos.DrawWireSphere(entryPosition, 0.5f);
            
            // 绘制目标位置
            Gizmos.color = Color.red;
            Gizmos.DrawWireSphere(targetPosition, 0.3f);
            
            // 绘制移动路径
            Gizmos.color = Color.white;
            Gizmos.DrawLine(transform.position, targetPosition);
        }
    }
    
    /// <summary>
    /// 工人状态枚举
    /// </summary>
    public enum WorkerState
    {
        Idle,               // 空闲
        EnteringWorksite,   // 进入工地
        MovingToWork,       // 移动到工作位置
        Working,            // 工作中
        Resting,            // 休息中
        LeavingWorksite     // 离开工地
    }
}
