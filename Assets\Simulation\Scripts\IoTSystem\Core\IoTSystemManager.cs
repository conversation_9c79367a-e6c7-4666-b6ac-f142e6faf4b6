using UnityEngine;
using Cysharp.Threading.Tasks;
using System.Threading;
using System;
using System.Collections.Generic;
using Simulation.IoTSystem.MQTT;
using Simulation.IoTSystem.Data;

namespace Simulation.IoTSystem.Core
{
    /// <summary>
    /// IoT系统管理器
    /// 负责协调传感器、数据处理和MQTT通信
    /// </summary>
    public class IoTSystemManager : MonoBehaviour, IIoTSystem
    {
        [Header("系统配置")]
        [SerializeField] private bool autoInitialize = true;
        [SerializeField] private bool enableDebugLog = true;
        
        [Header("组件引用")]
        [SerializeField] private MQTTManager mqttManager;
        [SerializeField] private DataProcessor dataProcessor;
        
        // 系统状态
        public bool IsInitialized { get; private set; }
        public bool IsRunning { get; private set; }
        
        // 传感器管理
        private readonly Dictionary<string, ISensorComponent> registeredSensors = new();
        private readonly Dictionary<string, Action<string, object>> subscriptions = new();
        
        // 取消令牌
        private CancellationTokenSource cancellationTokenSource;
        
        // 事件
        public event Action OnSystemInitialized;
        public event Action OnSystemStarted;
        public event Action OnSystemStopped;
        public event Action<string> OnSystemError;
        
        private void Awake()
        {
            // 确保单例
            if (FindObjectsOfType<IoTSystemManager>().Length > 1)
            {
                Destroy(gameObject);
                return;
            }
            
            DontDestroyOnLoad(gameObject);
        }
        
        private void Start()
        {
            if (autoInitialize)
            {
                InitializeAsync().Forget();
            }
        }
        
        private void OnDestroy()
        {
            StopAsync().Forget();
        }
        
        /// <summary>
        /// 初始化IoT系统
        /// </summary>
        public async UniTask<bool> InitializeAsync(CancellationToken cancellationToken = default)
        {
            if (IsInitialized)
            {
                LogDebug("IoT系统已初始化");
                return true;
            }
            
            try
            {
                LogDebug("正在初始化IoT系统...");
                
                // 初始化组件
                if (!await InitializeComponentsAsync(cancellationToken))
                {
                    LogError("组件初始化失败");
                    return false;
                }
                
                // 设置事件处理
                SetupEventHandlers();
                
                IsInitialized = true;
                LogDebug("IoT系统初始化完成");
                OnSystemInitialized?.Invoke();
                
                return true;
            }
            catch (Exception ex)
            {
                LogError($"IoT系统初始化异常: {ex.Message}");
                OnSystemError?.Invoke(ex.Message);
                return false;
            }
        }
        
        /// <summary>
        /// 启动IoT系统
        /// </summary>
        public async UniTask<bool> StartAsync(CancellationToken cancellationToken = default)
        {
            if (!IsInitialized)
            {
                LogError("系统未初始化，无法启动");
                return false;
            }
            
            if (IsRunning)
            {
                LogDebug("IoT系统已在运行中");
                return true;
            }
            
            try
            {
                LogDebug("正在启动IoT系统...");
                
                cancellationTokenSource = new CancellationTokenSource();
                
                // 启动MQTT连接
                if (mqttManager != null)
                {
                    if (!await mqttManager.ConnectAsync(cancellationToken))
                    {
                        LogError("MQTT连接失败");
                        return false;
                    }
                }
                
                // 启动所有注册的传感器
                StartAllSensors();
                
                IsRunning = true;
                LogDebug("IoT系统启动完成");
                OnSystemStarted?.Invoke();
                
                return true;
            }
            catch (Exception ex)
            {
                LogError($"IoT系统启动异常: {ex.Message}");
                OnSystemError?.Invoke(ex.Message);
                return false;
            }
        }
        
        /// <summary>
        /// 停止IoT系统
        /// </summary>
        public async UniTask StopAsync()
        {
            if (!IsRunning)
            {
                return;
            }
            
            try
            {
                LogDebug("正在停止IoT系统...");
                
                IsRunning = false;
                
                // 停止所有传感器
                StopAllSensors();
                
                // 断开MQTT连接
                if (mqttManager != null)
                {
                    await mqttManager.DisconnectAsync();
                }
                
                // 取消所有异步操作
                cancellationTokenSource?.Cancel();
                cancellationTokenSource?.Dispose();
                cancellationTokenSource = null;
                
                LogDebug("IoT系统已停止");
                OnSystemStopped?.Invoke();
            }
            catch (Exception ex)
            {
                LogError($"IoT系统停止异常: {ex.Message}");
                OnSystemError?.Invoke(ex.Message);
            }
        }
        
        /// <summary>
        /// 注册传感器
        /// </summary>
        public bool RegisterSensor(string sensorId, ISensorComponent sensor)
        {
            if (string.IsNullOrEmpty(sensorId) || sensor == null)
            {
                LogError("传感器ID或传感器对象为空");
                return false;
            }
            
            if (registeredSensors.ContainsKey(sensorId))
            {
                LogWarning($"传感器 {sensorId} 已注册，将替换现有传感器");
                UnregisterSensor(sensorId);
            }
            
            registeredSensors[sensorId] = sensor;
            
            // 订阅传感器数据事件
            sensor.OnDataGenerated += OnSensorDataGenerated;
            sensor.OnSensorError += OnSensorErrorOccurred;
            
            LogDebug($"传感器 {sensorId} 注册成功");
            
            // 如果系统正在运行，立即启动传感器
            if (IsRunning)
            {
                sensor.StartSensor();
            }
            
            return true;
        }
        
        /// <summary>
        /// 注销传感器
        /// </summary>
        public bool UnregisterSensor(string sensorId)
        {
            if (!registeredSensors.TryGetValue(sensorId, out var sensor))
            {
                LogWarning($"传感器 {sensorId} 未注册");
                return false;
            }
            
            // 停止传感器
            sensor.StopSensor();
            
            // 取消事件订阅
            sensor.OnDataGenerated -= OnSensorDataGenerated;
            sensor.OnSensorError -= OnSensorErrorOccurred;
            
            registeredSensors.Remove(sensorId);
            LogDebug($"传感器 {sensorId} 注销成功");
            
            return true;
        }
        
        /// <summary>
        /// 发送数据
        /// </summary>
        public async UniTask<bool> SendDataAsync(string topic, object data, CancellationToken cancellationToken = default)
        {
            if (!IsRunning)
            {
                LogError("系统未运行，无法发送数据");
                return false;
            }
            
            if (mqttManager == null || !mqttManager.IsConnected)
            {
                LogError("MQTT未连接，无法发送数据");
                return false;
            }
            
            try
            {
                // 处理数据
                var processedData = await dataProcessor.ProcessSensorDataAsync("", data, cancellationToken);
                var message = System.Text.Encoding.UTF8.GetString(processedData);
                
                // 发送MQTT消息
                return mqttManager.PublishMessage(topic, message);
            }
            catch (Exception ex)
            {
                LogError($"发送数据异常: {ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// 订阅数据
        /// </summary>
        public bool Subscribe(string topic, Action<string, object> onDataReceived)
        {
            if (string.IsNullOrEmpty(topic) || onDataReceived == null)
            {
                LogError("主题或回调函数为空");
                return false;
            }
            
            subscriptions[topic] = onDataReceived;
            
            // 订阅MQTT主题
            if (mqttManager != null && mqttManager.IsConnected)
            {
                return mqttManager.Subscribe(topic);
            }
            
            return true;
        }
        
        /// <summary>
        /// 取消订阅
        /// </summary>
        public bool Unsubscribe(string topic)
        {
            subscriptions.Remove(topic);
            
            // 取消MQTT订阅
            if (mqttManager != null && mqttManager.IsConnected)
            {
                return mqttManager.Unsubscribe(topic);
            }
            
            return true;
        }
        
        /// <summary>
        /// 初始化组件
        /// </summary>
        private async UniTask<bool> InitializeComponentsAsync(CancellationToken cancellationToken)
        {
            // 查找组件
            if (mqttManager == null)
            {
                mqttManager = FindObjectOfType<MQTTManager>();
            }
            
            if (dataProcessor == null)
            {
                dataProcessor = FindObjectOfType<DataProcessor>();
                if (dataProcessor == null)
                {
                    // 创建默认数据处理器
                    var go = new GameObject("DataProcessor");
                    go.transform.SetParent(transform);
                    dataProcessor = go.AddComponent<DataProcessor>();
                }
            }
            
            return true;
        }
        
        /// <summary>
        /// 设置事件处理器
        /// </summary>
        private void SetupEventHandlers()
        {
            if (mqttManager != null)
            {
                mqttManager.OnMessageReceived += OnMQTTMessageReceived;
                mqttManager.OnError += OnMQTTError;
            }
        }
        
        /// <summary>
        /// 启动所有传感器
        /// </summary>
        private void StartAllSensors()
        {
            foreach (var sensor in registeredSensors.Values)
            {
                sensor.StartSensor();
            }
            
            LogDebug($"启动了 {registeredSensors.Count} 个传感器");
        }
        
        /// <summary>
        /// 停止所有传感器
        /// </summary>
        private void StopAllSensors()
        {
            foreach (var sensor in registeredSensors.Values)
            {
                sensor.StopSensor();
            }
            
            LogDebug($"停止了 {registeredSensors.Count} 个传感器");
        }
        
        /// <summary>
        /// 传感器数据生成事件处理
        /// </summary>
        private void OnSensorDataGenerated(string sensorId, object data)
        {
            if (registeredSensors.TryGetValue(sensorId, out var sensor))
            {
                // 生成MQTT主题
                var topic = $"sensors/{sensor.DeviceId}/{sensor.SensorType}/data";
                
                // 异步发送数据
                SendDataAsync(topic, data, cancellationTokenSource?.Token ?? CancellationToken.None).Forget();
            }
        }
        
        /// <summary>
        /// 传感器错误事件处理
        /// </summary>
        private void OnSensorErrorOccurred(string error)
        {
            LogError($"传感器错误: {error}");
            OnSystemError?.Invoke(error);
        }
        
        /// <summary>
        /// MQTT消息接收事件处理
        /// </summary>
        private void OnMQTTMessageReceived(string topic, byte[] payload)
        {
            if (subscriptions.TryGetValue(topic, out var callback))
            {
                try
                {
                    // 解析数据
                    var data = dataProcessor.ParseReceivedDataAsync(topic, payload, CancellationToken.None).GetAwaiter().GetResult();
                    callback.Invoke(topic, data);
                }
                catch (Exception ex)
                {
                    LogError($"处理接收消息异常: {ex.Message}");
                }
            }
        }
        
        /// <summary>
        /// MQTT错误事件处理
        /// </summary>
        private void OnMQTTError(string error)
        {
            LogError($"MQTT错误: {error}");
            OnSystemError?.Invoke(error);
        }
        
        /// <summary>
        /// 调试日志
        /// </summary>
        private void LogDebug(string message)
        {
            if (enableDebugLog)
            {
                Debug.Log($"[IoTSystemManager] {message}");
            }
        }
        
        /// <summary>
        /// 错误日志
        /// </summary>
        private void LogError(string message)
        {
            Debug.LogError($"[IoTSystemManager] {message}");
        }
        
        /// <summary>
        /// 警告日志
        /// </summary>
        private void LogWarning(string message)
        {
            Debug.LogWarning($"[IoTSystemManager] {message}");
        }
    }
}
