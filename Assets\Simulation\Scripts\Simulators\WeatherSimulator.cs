using System;
using System.Collections.Generic;
using System.Threading;
using Cysharp.Threading.Tasks;
using UnityEngine;
using Simulation.IoT;
using Simulation.Data;

namespace Simulation.Simulators
{
    /// <summary>
    /// 气象模拟器
    /// 模拟天气变化和风力条件，影响其他模拟器的作业条件
    /// </summary>
    public class WeatherSimulator : SimulatorBase
    {
        [Header("气象站基础信息")]
        [SerializeField] private string stationId = "weather_station_001";
        [SerializeField] private string stationName = "主气象站";
        [SerializeField] private string location = "工地气象观测点";
        
        [Header("天气配置")]
        [SerializeField] private WeatherType initialWeather = WeatherType.Sunny;
        [SerializeField] private float weatherChangeInterval = 3600f; // 天气变化间隔（秒）
        [SerializeField] private float weatherChangeChance = 0.3f; // 天气变化概率
        [SerializeField] private bool enableSeasonalVariation = true; // 启用季节变化
        
        [Header("温度配置")]
        [SerializeField] private float baseTemperature = 20f; // 基础温度（℃）
        [SerializeField] private float temperatureVariation = 15f; // 温度变化幅度
        [SerializeField] private float dailyTemperatureRange = 10f; // 日温差
        [SerializeField] private float minTemperature = -10f; // 最低温度
        [SerializeField] private float maxTemperature = 40f; // 最高温度
        
        [Header("湿度配置")]
        [SerializeField] private float baseHumidity = 60f; // 基础湿度（%）
        [SerializeField] private float humidityVariation = 30f; // 湿度变化幅度
        [SerializeField] private float minHumidity = 20f; // 最低湿度
        [SerializeField] private float maxHumidity = 95f; // 最高湿度
        
        [Header("风力配置")]
        [SerializeField] private float baseWindSpeed = 3f; // 基础风速（m/s）
        [SerializeField] private float windSpeedVariation = 5f; // 风速变化幅度
        [SerializeField] private float maxWindSpeed = 20f; // 最大风速
        [SerializeField] private float windDirectionChangeRate = 30f; // 风向变化速度（度/分钟）
        [SerializeField] private bool enableGustSimulation = true; // 启用阵风模拟
        
        [Header("降水配置")]
        [SerializeField] private bool enablePrecipitation = true; // 启用降水
        [SerializeField] private float precipitationIntensity = 0f; // 降水强度（mm/h）
        [SerializeField] private float maxPrecipitationIntensity = 50f; // 最大降水强度
        [SerializeField] private float precipitationDuration = 1800f; // 降水持续时间（秒）
        
        [Header("能见度配置")]
        [SerializeField] private float baseVisibility = 10000f; // 基础能见度（米）
        [SerializeField] private float minVisibility = 100f; // 最低能见度
        [SerializeField] private float maxVisibility = 20000f; // 最高能见度
        
        [Header("监测配置")]
        [SerializeField] private float monitoringInterval = 60f; // 监测间隔（秒）
        [SerializeField] private bool enableWeatherAlerts = true; // 启用天气预警
        [SerializeField] private float extremeWeatherThreshold = 15f; // 极端天气阈值（风速）
        
        [Header("调试配置")]
        [SerializeField] private bool enableDebugLog = true;
        [SerializeField] private bool showWeatherGizmos = true;
        [SerializeField] private Color weatherColor = Color.cyan;
        
        // 当前天气状态
        private WeatherType currentWeather = WeatherType.Sunny;
        private float currentTemperature = 20f;
        private float currentHumidity = 60f;
        private float currentWindSpeed = 3f;
        private float currentWindDirection = 0f; // 风向（度）
        private float currentPrecipitation = 0f;
        private float currentVisibility = 10000f;
        private float currentPressure = 1013.25f; // 大气压（hPa）
        
        // 天气变化状态
        private DateTime lastWeatherChange = DateTime.UtcNow;
        private DateTime precipitationStartTime = DateTime.MinValue;
        private bool isRaining = false;
        private bool isExtremeWeather = false;
        
        // 统计数据
        private int totalMeasurements = 0;
        private int weatherChangeCount = 0;
        private int extremeWeatherEvents = 0;
        private float maxRecordedWindSpeed = 0f;
        private float maxRecordedTemperature = float.MinValue;
        private float minRecordedTemperature = float.MaxValue;
        
        // 公共属性
        public string StationId => stationId;
        public WeatherType CurrentWeather => currentWeather;
        public float CurrentTemperature => currentTemperature;
        public float CurrentHumidity => currentHumidity;
        public float CurrentWindSpeed => currentWindSpeed;
        public float CurrentWindDirection => currentWindDirection;
        public float CurrentPrecipitation => currentPrecipitation;
        public float CurrentVisibility => currentVisibility;
        public bool IsRaining => isRaining;
        public bool IsExtremeWeather => isExtremeWeather;
        
        protected override async UniTask OnStartSimulationAsync(CancellationToken cancellationToken)
        {
            LogDebug("气象模拟器启动");
            
            // 初始化天气状态
            currentWeather = initialWeather;
            InitializeWeatherConditions();
            
            // 发送气象站启动事件
            SendWeatherEvent("weather_station_start", "气象站启动", $"气象站 {stationName} 开始监测");
            
            // 启动天气变化循环
            WeatherChangeLoop(cancellationToken).Forget();
            
            // 启动监测循环
            MonitoringLoop(cancellationToken).Forget();
            
            // 启动风向变化
            WindDirectionLoop(cancellationToken).Forget();
        }
        
        protected override void OnSimulationUpdate(float deltaTime)
        {
            // 更新天气参数
            UpdateWeatherParameters(deltaTime);
            
            // 检查极端天气
            CheckExtremeWeather();
            
            // 更新降水状态
            UpdatePrecipitation();
        }
        
        protected override UniTask OnStopSimulationAsync()
        {
            LogDebug("气象模拟器停止");
            
            // 发送气象站停止事件
            SendWeatherEvent("weather_station_stop", "气象站停止", 
                $"气象站 {stationName} 停止监测，共记录{totalMeasurements}次数据，天气变化{weatherChangeCount}次，极端天气{extremeWeatherEvents}次");
            
            return UniTask.CompletedTask;
        }
        
        /// <summary>
        /// 天气变化循环
        /// </summary>
        private async UniTaskVoid WeatherChangeLoop(CancellationToken cancellationToken)
        {
            try
            {
                while (!cancellationToken.IsCancellationRequested)
                {
                    await UniTask.Delay(TimeSpan.FromSeconds(weatherChangeInterval), cancellationToken: cancellationToken);
                    
                    // 检查是否需要变化天气
                    if (UnityEngine.Random.Range(0f, 1f) < weatherChangeChance)
                    {
                        ChangeWeather();
                    }
                }
            }
            catch (OperationCanceledException)
            {
                LogDebug("天气变化循环被取消");
            }
        }
        
        /// <summary>
        /// 监测循环
        /// </summary>
        private async UniTaskVoid MonitoringLoop(CancellationToken cancellationToken)
        {
            try
            {
                while (!cancellationToken.IsCancellationRequested)
                {
                    await UniTask.Delay(TimeSpan.FromSeconds(monitoringInterval), cancellationToken: cancellationToken);
                    
                    // 记录气象数据
                    RecordWeatherData();
                }
            }
            catch (OperationCanceledException)
            {
                LogDebug("监测循环被取消");
            }
        }
        
        /// <summary>
        /// 风向变化循环
        /// </summary>
        private async UniTaskVoid WindDirectionLoop(CancellationToken cancellationToken)
        {
            try
            {
                while (!cancellationToken.IsCancellationRequested)
                {
                    await UniTask.Delay(TimeSpan.FromSeconds(60f), cancellationToken: cancellationToken);
                    
                    // 缓慢改变风向
                    float directionChange = UnityEngine.Random.Range(-windDirectionChangeRate, windDirectionChangeRate);
                    currentWindDirection = (currentWindDirection + directionChange) % 360f;
                    if (currentWindDirection < 0f) currentWindDirection += 360f;
                }
            }
            catch (OperationCanceledException)
            {
                LogDebug("风向变化循环被取消");
            }
        }
        
        /// <summary>
        /// 初始化天气条件
        /// </summary>
        private void InitializeWeatherConditions()
        {
            currentTemperature = baseTemperature;
            currentHumidity = baseHumidity;
            currentWindSpeed = baseWindSpeed;
            currentWindDirection = UnityEngine.Random.Range(0f, 360f);
            currentPrecipitation = 0f;
            currentVisibility = baseVisibility;
            
            ApplyWeatherEffects();
        }
        
        /// <summary>
        /// 改变天气
        /// </summary>
        private void ChangeWeather()
        {
            WeatherType previousWeather = currentWeather;
            
            // 随机选择新天气
            Array weatherTypes = Enum.GetValues(typeof(WeatherType));
            WeatherType newWeather;
            
            do
            {
                newWeather = (WeatherType)weatherTypes.GetValue(UnityEngine.Random.Range(0, weatherTypes.Length));
            }
            while (newWeather == currentWeather);
            
            currentWeather = newWeather;
            weatherChangeCount++;
            lastWeatherChange = DateTime.UtcNow;
            
            LogDebug($"天气变化: {previousWeather} -> {currentWeather}");
            
            // 应用新天气效果
            ApplyWeatherEffects();
            
            // 发送天气变化事件
            SendWeatherChangeEvent(previousWeather, currentWeather);
        }
        
        /// <summary>
        /// 应用天气效果
        /// </summary>
        private void ApplyWeatherEffects()
        {
            switch (currentWeather)
            {
                case WeatherType.Sunny:
                    ApplySunnyWeather();
                    break;
                case WeatherType.Cloudy:
                    ApplyCloudyWeather();
                    break;
                case WeatherType.Rainy:
                    ApplyRainyWeather();
                    break;
                case WeatherType.Stormy:
                    ApplyStormyWeather();
                    break;
                case WeatherType.Foggy:
                    ApplyFoggyWeather();
                    break;
                case WeatherType.Snowy:
                    ApplySnowyWeather();
                    break;
            }
        }
        
        /// <summary>
        /// 应用晴天天气
        /// </summary>
        private void ApplySunnyWeather()
        {
            currentTemperature = baseTemperature + UnityEngine.Random.Range(-5f, 10f);
            currentHumidity = baseHumidity + UnityEngine.Random.Range(-20f, 10f);
            currentWindSpeed = baseWindSpeed + UnityEngine.Random.Range(-1f, 3f);
            currentPrecipitation = 0f;
            currentVisibility = baseVisibility;
            isRaining = false;
        }
        
        /// <summary>
        /// 应用多云天气
        /// </summary>
        private void ApplyCloudyWeather()
        {
            currentTemperature = baseTemperature + UnityEngine.Random.Range(-8f, 5f);
            currentHumidity = baseHumidity + UnityEngine.Random.Range(-10f, 20f);
            currentWindSpeed = baseWindSpeed + UnityEngine.Random.Range(-2f, 4f);
            currentPrecipitation = 0f;
            currentVisibility = baseVisibility * 0.8f;
            isRaining = false;
        }
        
        /// <summary>
        /// 应用雨天天气
        /// </summary>
        private void ApplyRainyWeather()
        {
            currentTemperature = baseTemperature + UnityEngine.Random.Range(-10f, 0f);
            currentHumidity = baseHumidity + UnityEngine.Random.Range(10f, 30f);
            currentWindSpeed = baseWindSpeed + UnityEngine.Random.Range(0f, 6f);
            currentPrecipitation = UnityEngine.Random.Range(1f, 15f);
            currentVisibility = baseVisibility * 0.6f;
            isRaining = true;
            precipitationStartTime = DateTime.UtcNow;
        }
        
        /// <summary>
        /// 应用暴风雨天气
        /// </summary>
        private void ApplyStormyWeather()
        {
            currentTemperature = baseTemperature + UnityEngine.Random.Range(-15f, -5f);
            currentHumidity = baseHumidity + UnityEngine.Random.Range(20f, 35f);
            currentWindSpeed = baseWindSpeed + UnityEngine.Random.Range(8f, 15f);
            currentPrecipitation = UnityEngine.Random.Range(15f, maxPrecipitationIntensity);
            currentVisibility = baseVisibility * 0.3f;
            isRaining = true;
            precipitationStartTime = DateTime.UtcNow;
        }
        
        /// <summary>
        /// 应用雾天天气
        /// </summary>
        private void ApplyFoggyWeather()
        {
            currentTemperature = baseTemperature + UnityEngine.Random.Range(-5f, 5f);
            currentHumidity = baseHumidity + UnityEngine.Random.Range(25f, 35f);
            currentWindSpeed = baseWindSpeed + UnityEngine.Random.Range(-2f, 1f);
            currentPrecipitation = 0f;
            currentVisibility = UnityEngine.Random.Range(minVisibility, baseVisibility * 0.2f);
            isRaining = false;
        }
        
        /// <summary>
        /// 应用雪天天气
        /// </summary>
        private void ApplySnowyWeather()
        {
            currentTemperature = UnityEngine.Random.Range(minTemperature, 2f);
            currentHumidity = baseHumidity + UnityEngine.Random.Range(15f, 25f);
            currentWindSpeed = baseWindSpeed + UnityEngine.Random.Range(-1f, 8f);
            currentPrecipitation = UnityEngine.Random.Range(0.5f, 10f);
            currentVisibility = baseVisibility * 0.5f;
            isRaining = false; // 下雪不算下雨
        }
        
        /// <summary>
        /// 更新天气参数
        /// </summary>
        private void UpdateWeatherParameters(float deltaTime)
        {
            // 应用日温差
            float timeOfDay = (Time.time % 86400f) / 86400f; // 0-1之间
            float dailyTempVariation = Mathf.Sin(timeOfDay * Mathf.PI * 2f - Mathf.PI * 0.5f) * dailyTemperatureRange * 0.5f;
            currentTemperature += dailyTempVariation * deltaTime / 3600f; // 每小时变化
            
            // 限制温度范围
            currentTemperature = Mathf.Clamp(currentTemperature, minTemperature, maxTemperature);
            
            // 限制湿度范围
            currentHumidity = Mathf.Clamp(currentHumidity, minHumidity, maxHumidity);
            
            // 应用阵风效果
            if (enableGustSimulation)
            {
                float gustFactor = 1f + Mathf.Sin(Time.time * 2f) * 0.3f;
                currentWindSpeed = Mathf.Clamp(currentWindSpeed * gustFactor, 0f, maxWindSpeed);
            }
        }
        
        /// <summary>
        /// 检查极端天气
        /// </summary>
        private void CheckExtremeWeather()
        {
            bool wasExtreme = isExtremeWeather;
            isExtremeWeather = currentWindSpeed > extremeWeatherThreshold || 
                              currentPrecipitation > maxPrecipitationIntensity * 0.8f ||
                              currentVisibility < minVisibility * 2f;
            
            if (isExtremeWeather && !wasExtreme)
            {
                extremeWeatherEvents++;
                LogDebug($"极端天气事件: 风速{currentWindSpeed:F1}m/s, 降水{currentPrecipitation:F1}mm/h, 能见度{currentVisibility:F0}m");
                
                if (enableWeatherAlerts)
                {
                    SendExtremeWeatherAlert();
                }
            }
        }
        
        /// <summary>
        /// 更新降水状态
        /// </summary>
        private void UpdatePrecipitation()
        {
            if (isRaining && precipitationStartTime != DateTime.MinValue)
            {
                double rainDuration = (DateTime.UtcNow - precipitationStartTime).TotalSeconds;
                
                // 降水逐渐减弱
                if (rainDuration > precipitationDuration)
                {
                    currentPrecipitation = Mathf.Max(0f, currentPrecipitation - Time.deltaTime * 5f);
                    
                    if (currentPrecipitation <= 0.1f)
                    {
                        isRaining = false;
                        currentPrecipitation = 0f;
                    }
                }
            }
        }
        
        /// <summary>
        /// 记录气象数据
        /// </summary>
        private void RecordWeatherData()
        {
            totalMeasurements++;
            
            // 更新统计
            maxRecordedWindSpeed = Mathf.Max(maxRecordedWindSpeed, currentWindSpeed);
            maxRecordedTemperature = Mathf.Max(maxRecordedTemperature, currentTemperature);
            minRecordedTemperature = Mathf.Min(minRecordedTemperature, currentTemperature);
            
            LogDebug($"气象数据: {currentWeather}, 温度{currentTemperature:F1}℃, 湿度{currentHumidity:F0}%, 风速{currentWindSpeed:F1}m/s, 风向{currentWindDirection:F0}°");
            
            // 发送气象数据事件
            SendWeatherDataEvent();
        }
        
        /// <summary>
        /// 发送天气事件
        /// </summary>
        private void SendWeatherEvent(string eventType, string title, string description)
        {
            var eventData = new StatusEventData
            {
                equipmentId = stationId,
                equipmentType = "weather_station",
                currentStatus = currentWeather.ToString(),
                sourceId = stationId,
                title = title,
                description = description
            };
            
            IoTSystem.Instance?.SendIOTEvent(stationId, eventType, title, description, eventData);
        }
        
        /// <summary>
        /// 发送天气变化事件
        /// </summary>
        private void SendWeatherChangeEvent(WeatherType previousWeather, WeatherType newWeather)
        {
            var changeEvent = new
            {
                stationId = stationId,
                previousWeather = previousWeather.ToString(),
                newWeather = newWeather.ToString(),
                timestamp = DateTime.UtcNow,
                temperature = currentTemperature,
                humidity = currentHumidity,
                windSpeed = currentWindSpeed,
                windDirection = currentWindDirection,
                precipitation = currentPrecipitation,
                visibility = currentVisibility
            };
            
            IoTSystem.Instance?.SendIOTEvent(
                stationId, 
                "weather_change", 
                "天气变化", 
                $"天气从{previousWeather}变为{newWeather}",
                changeEvent
            );
        }
        
        /// <summary>
        /// 发送气象数据事件
        /// </summary>
        private void SendWeatherDataEvent()
        {
            var weatherData = new
            {
                stationId = stationId,
                weather = currentWeather.ToString(),
                temperature = currentTemperature,
                humidity = currentHumidity,
                windSpeed = currentWindSpeed,
                windDirection = currentWindDirection,
                precipitation = currentPrecipitation,
                visibility = currentVisibility,
                pressure = currentPressure,
                isExtremeWeather = isExtremeWeather,
                timestamp = DateTime.UtcNow,
                location = new { x = transform.position.x, y = transform.position.y, z = transform.position.z }
            };
            
            IoTSystem.Instance?.SendIOTEvent(
                stationId, 
                "weather_data", 
                "气象数据", 
                $"温度{currentTemperature:F1}℃, 湿度{currentHumidity:F0}%, 风速{currentWindSpeed:F1}m/s",
                weatherData
            );
        }
        
        /// <summary>
        /// 发送极端天气预警
        /// </summary>
        private void SendExtremeWeatherAlert()
        {
            var alertData = new SafetyEventData
            {
                violationType = "extreme_weather",
                sourceId = stationId,
                title = "极端天气预警",
                description = $"检测到极端天气: {currentWeather}, 风速{currentWindSpeed:F1}m/s",
                severity = "warning"
            };
            
            alertData.location.SetPosition(transform.position);
            
            IoTSystem.Instance?.SendIOTEvent(stationId, "extreme_weather_alert", "极端天气预警", alertData.description, alertData);
        }
        
        /// <summary>
        /// 调试日志
        /// </summary>
        private void LogDebug(string message)
        {
            if (enableDebugLog)
            {
                Debug.Log($"[WeatherSimulator:{stationId}] {message}");
            }
        }
        
        /// <summary>
        /// 绘制调试信息
        /// </summary>
        private void OnDrawGizmos()
        {
            if (!showWeatherGizmos) return;
            
            // 绘制气象站
            Gizmos.color = weatherColor;
            Gizmos.DrawWireCube(transform.position, Vector3.one * 3f);
            
            // 绘制风向
            Vector3 windDirection = new Vector3(
                Mathf.Sin(currentWindDirection * Mathf.Deg2Rad),
                0f,
                Mathf.Cos(currentWindDirection * Mathf.Deg2Rad)
            ) * currentWindSpeed;
            
            Gizmos.color = Color.white;
            Gizmos.DrawRay(transform.position, windDirection);
            
            // 绘制天气状态
            Color statusColor = isExtremeWeather ? Color.red : Color.green;
            Gizmos.color = statusColor;
            Gizmos.DrawWireSphere(transform.position + Vector3.up * 4f, 1f);
        }
    }
    
    /// <summary>
    /// 天气类型枚举
    /// </summary>
    public enum WeatherType
    {
        Sunny,      // 晴天
        Cloudy,     // 多云
        Rainy,      // 雨天
        Stormy,     // 暴风雨
        Foggy,      // 雾天
        Snowy       // 雪天
    }
}
