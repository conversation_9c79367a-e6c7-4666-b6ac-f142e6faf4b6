using System;
using System.Collections.Generic;
using System.Threading;
using Cysharp.Threading.Tasks;
using UnityEngine;
using Simulation.IoT;
using Simulation.Data;

namespace Simulation.Simulators
{
    /// <summary>
    /// 物料管理模拟器
    /// 模拟建筑工地的物料存储、运输和消耗过程
    /// </summary>
    public class MaterialManagementSimulator : SimulatorBase
    {
        [Header("物料管理基础信息")]
        [SerializeField] private string warehouseId = "warehouse_001";
        [SerializeField] private string warehouseName = "主仓库";
        [SerializeField] private float warehouseCapacity = 10000f; // 仓库总容量（立方米）
        [SerializeField] private Vector3 warehouseSize = new Vector3(50f, 10f, 30f); // 仓库尺寸

        [Header("物料类型配置")]
        [SerializeField] private List<MaterialTypeConfig> materialTypes = new List<MaterialTypeConfig>();
        [SerializeField] private bool autoGenerateMaterials = true; // 自动生成物料类型
        [SerializeField] private int maxMaterialTypes = 10; // 最大物料类型数量

        [Header("库存管理配置")]
        [SerializeField] private float inventoryCheckInterval = 300f; // 库存检查间隔（秒）
        [SerializeField] private float lowStockThreshold = 0.2f; // 低库存阈值（比例）
        [SerializeField] private float highStockThreshold = 0.9f; // 高库存阈值（比例）
        [SerializeField] private bool enableAutoReplenishment = true; // 启用自动补货

        [Header("运输配置")]
        [SerializeField] private float deliveryInterval = 1800f; // 配送间隔（秒）
        [SerializeField] private float deliveryVariation = 600f; // 配送时间变化
        [SerializeField] private float minDeliveryAmount = 100f; // 最小配送量
        [SerializeField] private float maxDeliveryAmount = 1000f; // 最大配送量

        [Header("消耗配置")]
        [SerializeField] private float consumptionInterval = 120f; // 消耗检查间隔（秒）
        [SerializeField] private float baseConsumptionRate = 10f; // 基础消耗率（单位/小时）
        [SerializeField] private float consumptionVariation = 5f; // 消耗变化幅度
        [SerializeField] private bool enableWorkTimeConsumption = true; // 启用工作时间消耗

        [Header("质量管理配置")]
        [SerializeField] private bool enableQualityCheck = true; // 启用质量检查
        [SerializeField] private float qualityCheckChance = 0.1f; // 质量检查概率
        [SerializeField] private float defectiveRate = 0.05f; // 次品率
        [SerializeField] private bool enableExpirationCheck = true; // 启用过期检查

        [Header("监控配置")]
        [SerializeField] private bool enableInventoryAlerts = true; // 启用库存预警
        [SerializeField] private bool enableTemperatureMonitoring = false; // 启用温度监控
        [SerializeField] private float optimalTemperature = 20f; // 最适温度
        [SerializeField] private float temperatureRange = 10f; // 温度范围

        [Header("调试配置")]
        [SerializeField] private bool enableDebugLog = true;
        [SerializeField] private bool showInventoryGizmos = true;
        [SerializeField] private Color warehouseColor = Color.blue;

        // 物料库存
        private Dictionary<string, MaterialInventory> materialInventories = new Dictionary<string, MaterialInventory>();
        private List<MaterialTransaction> transactionHistory = new List<MaterialTransaction>();

        // 运营状态
        private float currentCapacityUsed = 0f;
        private int totalDeliveries = 0;
        private int totalConsumptions = 0;
        private int qualityIssues = 0;
        private int lowStockAlerts = 0;

        // 统计数据
        private float totalMaterialsReceived = 0f;
        private float totalMaterialsConsumed = 0f;
        private float totalMaterialsWasted = 0f;
        private DateTime lastInventoryCheck = DateTime.UtcNow;

        // 公共属性
        public string WarehouseId => warehouseId;
        public float CurrentCapacityUsed => currentCapacityUsed;
        public float CapacityUtilization => currentCapacityUsed / warehouseCapacity;
        public int MaterialTypeCount => materialInventories.Count;
        public int TotalDeliveries => totalDeliveries;
        public int TotalConsumptions => totalConsumptions;
        public int QualityIssues => qualityIssues;

        protected override async UniTask OnStartSimulationAsync(CancellationToken cancellationToken)
        {
            LogDebug("物料管理模拟器启动");

            // 初始化物料类型
            InitializeMaterialTypes();

            // 初始化库存
            InitializeInventory();

            // 发送仓库启动事件
            SendWarehouseEvent("warehouse_start", "仓库启动", $"仓库 {warehouseName} 开始运营");

            // 启动库存检查循环
            InventoryCheckLoop(cancellationToken).Forget();

            // 启动配送循环
            DeliveryLoop(cancellationToken).Forget();

            // 启动消耗循环
            ConsumptionLoop(cancellationToken).Forget();

            // 启动质量检查循环
            if (enableQualityCheck)
            {
                QualityCheckLoop(cancellationToken).Forget();
            }
        }

        protected override void OnSimulationUpdate(float deltaTime)
        {
            // 更新容量使用情况
            UpdateCapacityUsage();

            // 检查温度监控
            if (enableTemperatureMonitoring)
            {
                CheckTemperatureConditions();
            }
        }

        protected override UniTask OnStopSimulationAsync()
        {
            LogDebug("物料管理模拟器停止");

            // 发送仓库停止事件
            SendWarehouseEvent("warehouse_stop", "仓库停止",
                $"仓库 {warehouseName} 停止运营，共处理{totalDeliveries}次配送，{totalConsumptions}次消耗，{qualityIssues}次质量问题");

            return UniTask.CompletedTask;
        }

        /// <summary>
        /// 库存检查循环
        /// </summary>
        private async UniTaskVoid InventoryCheckLoop(CancellationToken cancellationToken)
        {
            try
            {
                while (!cancellationToken.IsCancellationRequested)
                {
                    await UniTask.Delay(TimeSpan.FromSeconds(inventoryCheckInterval), cancellationToken: cancellationToken);

                    CheckInventoryLevels();
                    CheckExpirations();

                    lastInventoryCheck = DateTime.UtcNow;
                }
            }
            catch (OperationCanceledException)
            {
                LogDebug("库存检查循环被取消");
            }
        }

        /// <summary>
        /// 配送循环
        /// </summary>
        private async UniTaskVoid DeliveryLoop(CancellationToken cancellationToken)
        {
            try
            {
                while (!cancellationToken.IsCancellationRequested)
                {
                    float nextDeliveryTime = deliveryInterval + UnityEngine.Random.Range(-deliveryVariation, deliveryVariation);
                    await UniTask.Delay(TimeSpan.FromSeconds(nextDeliveryTime), cancellationToken: cancellationToken);

                    ProcessDelivery();
                }
            }
            catch (OperationCanceledException)
            {
                LogDebug("配送循环被取消");
            }
        }

        /// <summary>
        /// 消耗循环
        /// </summary>
        private async UniTaskVoid ConsumptionLoop(CancellationToken cancellationToken)
        {
            try
            {
                while (!cancellationToken.IsCancellationRequested)
                {
                    await UniTask.Delay(TimeSpan.FromSeconds(consumptionInterval), cancellationToken: cancellationToken);

                    ProcessConsumption();
                }
            }
            catch (OperationCanceledException)
            {
                LogDebug("消耗循环被取消");
            }
        }

        /// <summary>
        /// 质量检查循环
        /// </summary>
        private async UniTaskVoid QualityCheckLoop(CancellationToken cancellationToken)
        {
            try
            {
                while (!cancellationToken.IsCancellationRequested)
                {
                    await UniTask.Delay(TimeSpan.FromSeconds(600f), cancellationToken: cancellationToken);

                    if (UnityEngine.Random.Range(0f, 1f) < qualityCheckChance)
                    {
                        ProcessQualityCheck();
                    }
                }
            }
            catch (OperationCanceledException)
            {
                LogDebug("质量检查循环被取消");
            }
        }

        /// <summary>
        /// 初始化物料类型
        /// </summary>
        private void InitializeMaterialTypes()
        {
            if (autoGenerateMaterials && materialTypes.Count == 0)
            {
                GenerateDefaultMaterialTypes();
            }

            // 验证物料类型配置
            foreach (var materialType in materialTypes)
            {
                if (string.IsNullOrEmpty(materialType.materialId))
                {
                    materialType.materialId = $"material_{UnityEngine.Random.Range(1000, 9999)}";
                }
            }
        }

        /// <summary>
        /// 生成默认物料类型
        /// </summary>
        private void GenerateDefaultMaterialTypes()
        {
            string[] defaultMaterials = {
                "水泥", "钢筋", "砂石", "混凝土", "砖块",
                "木材", "玻璃", "电线", "管道", "涂料"
            };

            string[] units = { "吨", "立方米", "根", "块", "升" };

            for (int i = 0; i < Mathf.Min(defaultMaterials.Length, maxMaterialTypes); i++)
            {
                var materialType = new MaterialTypeConfig
                {
                    materialId = $"mat_{i:D3}",
                    materialName = defaultMaterials[i],
                    unit = units[i % units.Length],
                    density = UnityEngine.Random.Range(0.5f, 3.0f),
                    unitPrice = UnityEngine.Random.Range(50f, 500f),
                    shelfLife = UnityEngine.Random.Range(30, 365),
                    storageTemperature = UnityEngine.Random.Range(15f, 25f),
                    isHazardous = UnityEngine.Random.Range(0f, 1f) < 0.1f
                };

                materialTypes.Add(materialType);
            }
        }

        /// <summary>
        /// 初始化库存
        /// </summary>
        private void InitializeInventory()
        {
            materialInventories.Clear();

            foreach (var materialType in materialTypes)
            {
                var inventory = new MaterialInventory
                {
                    materialId = materialType.materialId,
                    materialName = materialType.materialName,
                    currentStock = UnityEngine.Random.Range(100f, 1000f),
                    maxCapacity = UnityEngine.Random.Range(1000f, 5000f),
                    unit = materialType.unit,
                    unitPrice = materialType.unitPrice,
                    lastUpdated = DateTime.UtcNow,
                    batches = new List<MaterialBatch>()
                };

                // 创建初始批次
                CreateInitialBatch(inventory, materialType);

                materialInventories[materialType.materialId] = inventory;
            }

            UpdateCapacityUsage();
        }

        /// <summary>
        /// 创建初始批次
        /// </summary>
        private void CreateInitialBatch(MaterialInventory inventory, MaterialTypeConfig materialType)
        {
            var batch = new MaterialBatch
            {
                batchId = $"batch_{DateTime.UtcNow:yyyyMMdd}_{UnityEngine.Random.Range(1000, 9999)}",
                quantity = inventory.currentStock,
                receivedDate = DateTime.UtcNow.AddDays(-UnityEngine.Random.Range(1, 30)),
                expirationDate = DateTime.UtcNow.AddDays(materialType.shelfLife),
                quality = UnityEngine.Random.Range(0.8f, 1.0f),
                supplierId = $"supplier_{UnityEngine.Random.Range(1, 10):D2}",
                isDefective = false
            };

            inventory.batches.Add(batch);
        }

        /// <summary>
        /// 检查库存水平
        /// </summary>
        private void CheckInventoryLevels()
        {
            foreach (var inventory in materialInventories.Values)
            {
                float stockRatio = inventory.currentStock / inventory.maxCapacity;

                // 检查低库存
                if (stockRatio < lowStockThreshold)
                {
                    lowStockAlerts++;
                    LogDebug($"低库存预警: {inventory.materialName} 库存比例 {stockRatio:P1}");

                    if (enableInventoryAlerts)
                    {
                        SendLowStockAlert(inventory);
                    }

                    if (enableAutoReplenishment)
                    {
                        TriggerAutoReplenishment(inventory);
                    }
                }

                // 检查高库存
                if (stockRatio > highStockThreshold)
                {
                    LogDebug($"高库存提醒: {inventory.materialName} 库存比例 {stockRatio:P1}");

                    if (enableInventoryAlerts)
                    {
                        SendHighStockAlert(inventory);
                    }
                }
            }
        }

        /// <summary>
        /// 检查过期物料
        /// </summary>
        private void CheckExpirations()
        {
            if (!enableExpirationCheck) return;

            foreach (var inventory in materialInventories.Values)
            {
                var expiredBatches = inventory.batches.FindAll(b =>
                    b.expirationDate < DateTime.UtcNow && !b.isDefective);

                foreach (var expiredBatch in expiredBatches)
                {
                    expiredBatch.isDefective = true;
                    totalMaterialsWasted += expiredBatch.quantity;

                    LogDebug($"物料过期: {inventory.materialName} 批次 {expiredBatch.batchId} 数量 {expiredBatch.quantity}");

                    SendExpirationAlert(inventory, expiredBatch);
                }
            }
        }

        /// <summary>
        /// 处理配送
        /// </summary>
        private void ProcessDelivery()
        {
            if (materialTypes.Count == 0) return;

            // 随机选择物料类型
            var materialType = materialTypes[UnityEngine.Random.Range(0, materialTypes.Count)];
            var inventory = materialInventories[materialType.materialId];

            // 计算配送量
            float deliveryAmount = UnityEngine.Random.Range(minDeliveryAmount, maxDeliveryAmount);

            // 检查容量限制
            float availableCapacity = inventory.maxCapacity - inventory.currentStock;
            deliveryAmount = Mathf.Min(deliveryAmount, availableCapacity);

            if (deliveryAmount > 0)
            {
                // 创建新批次
                var newBatch = new MaterialBatch
                {
                    batchId = $"batch_{DateTime.UtcNow:yyyyMMdd}_{UnityEngine.Random.Range(1000, 9999)}",
                    quantity = deliveryAmount,
                    receivedDate = DateTime.UtcNow,
                    expirationDate = DateTime.UtcNow.AddDays(materialType.shelfLife),
                    quality = UnityEngine.Random.Range(0.85f, 1.0f),
                    supplierId = $"supplier_{UnityEngine.Random.Range(1, 10):D2}",
                    isDefective = UnityEngine.Random.Range(0f, 1f) < defectiveRate
                };

                inventory.batches.Add(newBatch);
                inventory.currentStock += deliveryAmount;
                inventory.lastUpdated = DateTime.UtcNow;

                totalDeliveries++;
                totalMaterialsReceived += deliveryAmount;

                LogDebug($"配送到货: {inventory.materialName} 数量 {deliveryAmount} {inventory.unit}");

                // 记录交易
                RecordTransaction("delivery", inventory, deliveryAmount, newBatch.supplierId);

                // 发送配送事件
                SendDeliveryEvent(inventory, newBatch);
            }
        }

        /// <summary>
        /// 处理消耗
        /// </summary>
        private void ProcessConsumption()
        {
            if (materialInventories.Count == 0) return;

            // 检查是否在工作时间
            if (enableWorkTimeConsumption && !IsWorkTime())
            {
                return;
            }

            foreach (var inventory in materialInventories.Values)
            {
                if (inventory.currentStock <= 0) continue;

                // 计算消耗量
                float consumptionRate = baseConsumptionRate + UnityEngine.Random.Range(-consumptionVariation, consumptionVariation);
                float consumptionAmount = consumptionRate * (consumptionInterval / 3600f); // 转换为小时

                // 限制消耗量不超过当前库存
                consumptionAmount = Mathf.Min(consumptionAmount, inventory.currentStock);

                if (consumptionAmount > 0)
                {
                    // 从最旧的批次开始消耗
                    ConsumeFromOldestBatches(inventory, consumptionAmount);

                    inventory.currentStock -= consumptionAmount;
                    inventory.lastUpdated = DateTime.UtcNow;

                    totalConsumptions++;
                    totalMaterialsConsumed += consumptionAmount;

                    LogDebug($"物料消耗: {inventory.materialName} 数量 {consumptionAmount} {inventory.unit}");

                    // 记录交易
                    RecordTransaction("consumption", inventory, -consumptionAmount, "construction");

                    // 发送消耗事件
                    SendConsumptionEvent(inventory, consumptionAmount);
                }
            }
        }

        /// <summary>
        /// 从最旧的批次消耗物料
        /// </summary>
        private void ConsumeFromOldestBatches(MaterialInventory inventory, float amount)
        {
            float remainingAmount = amount;
            var batchesToRemove = new List<MaterialBatch>();

            // 按接收日期排序
            inventory.batches.Sort((a, b) => a.receivedDate.CompareTo(b.receivedDate));

            foreach (var batch in inventory.batches)
            {
                if (remainingAmount <= 0 || batch.isDefective) continue;

                if (batch.quantity <= remainingAmount)
                {
                    // 整个批次被消耗
                    remainingAmount -= batch.quantity;
                    batchesToRemove.Add(batch);
                }
                else
                {
                    // 部分消耗
                    batch.quantity -= remainingAmount;
                    remainingAmount = 0;
                }
            }

            // 移除空批次
            foreach (var batch in batchesToRemove)
            {
                inventory.batches.Remove(batch);
            }
        }

        /// <summary>
        /// 处理质量检查
        /// </summary>
        private void ProcessQualityCheck()
        {
            if (materialInventories.Count == 0) return;

            // 随机选择物料进行质量检查
            var inventoryList = new List<MaterialInventory>(materialInventories.Values);
            var inventory = inventoryList[UnityEngine.Random.Range(0, inventoryList.Count)];

            if (inventory.batches.Count == 0) return;

            // 随机选择批次
            var batch = inventory.batches[UnityEngine.Random.Range(0, inventory.batches.Count)];

            // 进行质量检查
            bool qualityPassed = batch.quality > 0.8f && UnityEngine.Random.Range(0f, 1f) > defectiveRate;

            if (!qualityPassed && !batch.isDefective)
            {
                batch.isDefective = true;
                qualityIssues++;
                totalMaterialsWasted += batch.quantity;

                LogDebug($"质量问题: {inventory.materialName} 批次 {batch.batchId} 质量 {batch.quality:P1}");

                SendQualityIssueAlert(inventory, batch);
            }
            else
            {
                LogDebug($"质量检查通过: {inventory.materialName} 批次 {batch.batchId}");
            }
        }

        /// <summary>
        /// 更新容量使用情况
        /// </summary>
        private void UpdateCapacityUsage()
        {
            currentCapacityUsed = 0f;

            foreach (var inventory in materialInventories.Values)
            {
                // 根据密度计算体积
                var materialType = materialTypes.Find(m => m.materialId == inventory.materialId);
                if (materialType != null)
                {
                    currentCapacityUsed += inventory.currentStock / materialType.density;
                }
            }
        }

        /// <summary>
        /// 检查温度条件
        /// </summary>
        private void CheckTemperatureConditions()
        {
            float currentTemperature = optimalTemperature + UnityEngine.Random.Range(-temperatureRange, temperatureRange);

            foreach (var inventory in materialInventories.Values)
            {
                var materialType = materialTypes.Find(m => m.materialId == inventory.materialId);
                if (materialType != null)
                {
                    float tempDifference = Mathf.Abs(currentTemperature - materialType.storageTemperature);

                    if (tempDifference > 5f)
                    {
                        LogDebug($"温度异常: {inventory.materialName} 当前温度 {currentTemperature:F1}℃, 适宜温度 {materialType.storageTemperature:F1}℃");

                        SendTemperatureAlert(inventory, currentTemperature, materialType.storageTemperature);
                    }
                }
            }
        }

        /// <summary>
        /// 检查是否为工作时间
        /// </summary>
        private bool IsWorkTime()
        {
            var now = DateTime.Now;
            int hour = now.Hour;
            int dayOfWeek = (int)now.DayOfWeek;

            // 工作日8:00-18:00
            return dayOfWeek >= 1 && dayOfWeek <= 5 && hour >= 8 && hour < 18;
        }

        /// <summary>
        /// 触发自动补货
        /// </summary>
        private void TriggerAutoReplenishment(MaterialInventory inventory)
        {
            float replenishAmount = (inventory.maxCapacity - inventory.currentStock) * 0.8f;

            LogDebug($"触发自动补货: {inventory.materialName} 补货量 {replenishAmount}");

            // 模拟补货延迟
            AutoReplenishmentAsync(inventory, replenishAmount).Forget();
        }

        /// <summary>
        /// 自动补货异步处理
        /// </summary>
        private async UniTaskVoid AutoReplenishmentAsync(MaterialInventory inventory, float amount)
        {
            try
            {
                // 模拟补货时间
                await UniTask.Delay(TimeSpan.FromSeconds(UnityEngine.Random.Range(300f, 1800f)));

                var materialType = materialTypes.Find(m => m.materialId == inventory.materialId);
                if (materialType != null)
                {
                    // 创建补货批次
                    var replenishBatch = new MaterialBatch
                    {
                        batchId = $"auto_{DateTime.UtcNow:yyyyMMdd}_{UnityEngine.Random.Range(1000, 9999)}",
                        quantity = amount,
                        receivedDate = DateTime.UtcNow,
                        expirationDate = DateTime.UtcNow.AddDays(materialType.shelfLife),
                        quality = UnityEngine.Random.Range(0.9f, 1.0f),
                        supplierId = "auto_supplier",
                        isDefective = false
                    };

                    inventory.batches.Add(replenishBatch);
                    inventory.currentStock += amount;
                    inventory.lastUpdated = DateTime.UtcNow;

                    totalMaterialsReceived += amount;

                    LogDebug($"自动补货完成: {inventory.materialName} 数量 {amount}");

                    SendAutoReplenishmentEvent(inventory, replenishBatch);
                }
            }
            catch (Exception ex)
            {
                LogDebug($"自动补货异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 记录交易
        /// </summary>
        private void RecordTransaction(string type, MaterialInventory inventory, float amount, string sourceId)
        {
            var transaction = new MaterialTransaction
            {
                transactionId = $"trans_{DateTime.UtcNow:yyyyMMddHHmmss}_{UnityEngine.Random.Range(100, 999)}",
                materialId = inventory.materialId,
                materialName = inventory.materialName,
                transactionType = type,
                amount = amount,
                unit = inventory.unit,
                unitPrice = inventory.unitPrice,
                totalValue = amount * inventory.unitPrice,
                sourceId = sourceId,
                timestamp = DateTime.UtcNow,
                stockBefore = inventory.currentStock - amount,
                stockAfter = inventory.currentStock
            };

            transactionHistory.Add(transaction);

            // 限制历史记录数量
            if (transactionHistory.Count > 1000)
            {
                transactionHistory.RemoveAt(0);
            }
        }

        // 事件发送方法
        private void SendWarehouseEvent(string eventType, string title, string description)
        {
            var eventData = new StatusEventData
            {
                equipmentId = warehouseId,
                equipmentType = "warehouse",
                currentStatus = "operational",
                sourceId = warehouseId,
                title = title,
                description = description
            };

            IoTSystem.Instance?.SendIOTEvent(warehouseId, eventType, title, description, eventData);
        }

        private void SendLowStockAlert(MaterialInventory inventory)
        {
            var alertData = new
            {
                warehouseId = warehouseId,
                materialId = inventory.materialId,
                materialName = inventory.materialName,
                currentStock = inventory.currentStock,
                maxCapacity = inventory.maxCapacity,
                stockRatio = inventory.currentStock / inventory.maxCapacity,
                threshold = lowStockThreshold,
                unit = inventory.unit,
                timestamp = DateTime.UtcNow
            };

            IoTSystem.Instance?.SendIOTEvent(
                warehouseId,
                "low_stock_alert",
                "低库存预警",
                $"{inventory.materialName} 库存不足，当前库存 {inventory.currentStock:F1} {inventory.unit}",
                alertData
            );
        }

        private void SendHighStockAlert(MaterialInventory inventory)
        {
            var alertData = new
            {
                warehouseId = warehouseId,
                materialId = inventory.materialId,
                materialName = inventory.materialName,
                currentStock = inventory.currentStock,
                maxCapacity = inventory.maxCapacity,
                stockRatio = inventory.currentStock / inventory.maxCapacity,
                threshold = highStockThreshold,
                unit = inventory.unit,
                timestamp = DateTime.UtcNow
            };

            IoTSystem.Instance?.SendIOTEvent(
                warehouseId,
                "high_stock_alert",
                "高库存提醒",
                $"{inventory.materialName} 库存过高，当前库存 {inventory.currentStock:F1} {inventory.unit}",
                alertData
            );
        }

        private void SendExpirationAlert(MaterialInventory inventory, MaterialBatch batch)
        {
            var alertData = new
            {
                warehouseId = warehouseId,
                materialId = inventory.materialId,
                materialName = inventory.materialName,
                batchId = batch.batchId,
                quantity = batch.quantity,
                expirationDate = batch.expirationDate,
                unit = inventory.unit,
                timestamp = DateTime.UtcNow
            };

            IoTSystem.Instance?.SendIOTEvent(
                warehouseId,
                "material_expiration",
                "物料过期",
                $"{inventory.materialName} 批次 {batch.batchId} 已过期，数量 {batch.quantity:F1} {inventory.unit}",
                alertData
            );
        }

        private void SendDeliveryEvent(MaterialInventory inventory, MaterialBatch batch)
        {
            var deliveryData = new
            {
                warehouseId = warehouseId,
                materialId = inventory.materialId,
                materialName = inventory.materialName,
                batchId = batch.batchId,
                quantity = batch.quantity,
                supplierId = batch.supplierId,
                quality = batch.quality,
                receivedDate = batch.receivedDate,
                expirationDate = batch.expirationDate,
                unit = inventory.unit,
                unitPrice = inventory.unitPrice,
                totalValue = batch.quantity * inventory.unitPrice,
                timestamp = DateTime.UtcNow
            };

            IoTSystem.Instance?.SendIOTEvent(
                warehouseId,
                "material_delivery",
                "物料配送",
                $"{inventory.materialName} 配送到货，数量 {batch.quantity:F1} {inventory.unit}",
                deliveryData
            );
        }

        private void SendConsumptionEvent(MaterialInventory inventory, float amount)
        {
            var consumptionData = new
            {
                warehouseId = warehouseId,
                materialId = inventory.materialId,
                materialName = inventory.materialName,
                consumedAmount = amount,
                remainingStock = inventory.currentStock,
                unit = inventory.unit,
                timestamp = DateTime.UtcNow
            };

            IoTSystem.Instance?.SendIOTEvent(
                warehouseId,
                "material_consumption",
                "物料消耗",
                $"{inventory.materialName} 消耗 {amount:F1} {inventory.unit}，剩余 {inventory.currentStock:F1} {inventory.unit}",
                consumptionData
            );
        }

        private void SendQualityIssueAlert(MaterialInventory inventory, MaterialBatch batch)
        {
            var qualityData = new SafetyEventData
            {
                violationType = "quality_issue",
                sourceId = warehouseId,
                title = "质量问题",
                description = $"{inventory.materialName} 批次 {batch.batchId} 质量不合格",
                severity = "warning"
            };

            qualityData.location.SetPosition(transform.position);

            IoTSystem.Instance?.SendIOTEvent(warehouseId, "quality_issue", "质量问题", qualityData.description, qualityData);
        }

        private void SendTemperatureAlert(MaterialInventory inventory, float currentTemp, float optimalTemp)
        {
            var tempData = new
            {
                warehouseId = warehouseId,
                materialId = inventory.materialId,
                materialName = inventory.materialName,
                currentTemperature = currentTemp,
                optimalTemperature = optimalTemp,
                temperatureDifference = Mathf.Abs(currentTemp - optimalTemp),
                timestamp = DateTime.UtcNow
            };

            IoTSystem.Instance?.SendIOTEvent(
                warehouseId,
                "temperature_alert",
                "温度异常",
                $"{inventory.materialName} 存储温度异常，当前 {currentTemp:F1}℃，适宜 {optimalTemp:F1}℃",
                tempData
            );
        }

        private void SendAutoReplenishmentEvent(MaterialInventory inventory, MaterialBatch batch)
        {
            var replenishData = new
            {
                warehouseId = warehouseId,
                materialId = inventory.materialId,
                materialName = inventory.materialName,
                batchId = batch.batchId,
                quantity = batch.quantity,
                newStock = inventory.currentStock,
                unit = inventory.unit,
                timestamp = DateTime.UtcNow
            };

            IoTSystem.Instance?.SendIOTEvent(
                warehouseId,
                "auto_replenishment",
                "自动补货",
                $"{inventory.materialName} 自动补货 {batch.quantity:F1} {inventory.unit}",
                replenishData
            );
        }

        /// <summary>
        /// 调试日志
        /// </summary>
        private void LogDebug(string message)
        {
            if (enableDebugLog)
            {
                Debug.Log($"[MaterialManagement:{warehouseId}] {message}");
            }
        }

        /// <summary>
        /// 绘制调试信息
        /// </summary>
        private void OnDrawGizmos()
        {
            if (!showInventoryGizmos) return;

            // 绘制仓库
            Gizmos.color = warehouseColor;
            Gizmos.DrawWireCube(transform.position, warehouseSize);

            // 绘制容量使用情况
            float capacityRatio = CapacityUtilization;
            Color capacityColor = capacityRatio > 0.8f ? Color.red : (capacityRatio > 0.6f ? Color.yellow : Color.green);
            Gizmos.color = capacityColor;
            Vector3 fillSize = new Vector3(warehouseSize.x, warehouseSize.y * capacityRatio, warehouseSize.z);
            Vector3 fillPosition = transform.position - new Vector3(0f, warehouseSize.y * (1f - capacityRatio) * 0.5f, 0f);
            Gizmos.DrawCube(fillPosition, fillSize);

            // 绘制物料类型标识
            if (materialInventories.Count > 0)
            {
                int index = 0;
                foreach (var inventory in materialInventories.Values)
                {
                    Vector3 labelPosition = transform.position + new Vector3(
                        -warehouseSize.x * 0.4f + (index % 5) * warehouseSize.x * 0.2f,
                        warehouseSize.y * 0.6f,
                        -warehouseSize.z * 0.4f + (index / 5) * warehouseSize.z * 0.2f
                    );

                    float stockRatio = inventory.currentStock / inventory.maxCapacity;
                    Color stockColor = stockRatio < lowStockThreshold ? Color.red :
                                     (stockRatio > highStockThreshold ? Color.yellow : Color.green);

                    Gizmos.color = stockColor;
                    Gizmos.DrawWireCube(labelPosition, Vector3.one * 0.5f);

                    index++;
                }
            }
        }
    }

    /// <summary>
    /// 物料类型配置
    /// </summary>
    [Serializable]
    public class MaterialTypeConfig
    {
        public string materialId;
        public string materialName;
        public string unit;
        public float density = 1.0f; // 密度（吨/立方米）
        public float unitPrice = 100f; // 单价
        public int shelfLife = 365; // 保质期（天）
        public float storageTemperature = 20f; // 存储温度
        public bool isHazardous = false; // 是否危险品
    }

    /// <summary>
    /// 物料库存
    /// </summary>
    [Serializable]
    public class MaterialInventory
    {
        public string materialId;
        public string materialName;
        public float currentStock;
        public float maxCapacity;
        public string unit;
        public float unitPrice;
        public DateTime lastUpdated;
        public List<MaterialBatch> batches;
    }

    /// <summary>
    /// 物料批次
    /// </summary>
    [Serializable]
    public class MaterialBatch
    {
        public string batchId;
        public float quantity;
        public DateTime receivedDate;
        public DateTime expirationDate;
        public float quality; // 质量评分 0-1
        public string supplierId;
        public bool isDefective;
    }

    /// <summary>
    /// 物料交易记录
    /// </summary>
    [Serializable]
    public class MaterialTransaction
    {
        public string transactionId;
        public string materialId;
        public string materialName;
        public string transactionType; // delivery, consumption, waste
        public float amount;
        public string unit;
        public float unitPrice;
        public float totalValue;
        public string sourceId;
        public DateTime timestamp;
        public float stockBefore;
        public float stockAfter;
    }
}