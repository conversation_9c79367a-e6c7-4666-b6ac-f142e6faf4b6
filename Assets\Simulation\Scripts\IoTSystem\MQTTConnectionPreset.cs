using System;
using UnityEngine;
using Best.MQTT;

namespace Simulation.IoT
{
    /// <summary>
    /// MQTT连接预设配置
    /// 提供常用MQTT代理的预设配置
    /// </summary>
    [CreateAssetMenu(fileName = "MQTTConnectionPreset", menuName = "Simulation/MQTT Connection Preset")]
    public class MQTTConnectionPreset : ScriptableObject
    {
        [Header("基本信息")]
        [SerializeField] private string presetName = "默认配置";
        [SerializeField] private string description = "MQTT连接配置描述";
        
        [Header("连接配置")]
        [SerializeField] private string brokerAddress = "broker.emqx.io";
        [SerializeField] private int brokerPort = 1883;
        [SerializeField] private string clientId = "simulation_client";
        [SerializeField] private string username = "";
        [SerializeField] private string password = "";
        [SerializeField] private bool useSSL = false;
        [SerializeField] private int keepAliveInterval = 60;
        [SerializeField] private int connectionTimeout = 30;
        [SerializeField] private SupportedTransports transport = SupportedTransports.TCP;
        [SerializeField] private string websocketPath = "/mqtt";
        
        [Header("重连配置")]
        [SerializeField] private bool autoReconnect = true;
        [SerializeField] private int reconnectDelay = 5;
        [SerializeField] private int maxReconnectAttempts = 10;
        
        [Header("主题配置")]
        [SerializeField] private string topicPrefix = "simulation";
        [SerializeField] private int qosLevel = 1;
        [SerializeField] private bool retainMessages = false;
        
        // 公共属性
        public string PresetName => presetName;
        public string Description => description;
        public string BrokerAddress => brokerAddress;
        public int BrokerPort => brokerPort;
        public string ClientId => clientId;
        public string Username => username;
        public string Password => password;
        public bool UseSSL => useSSL;
        public int KeepAliveInterval => keepAliveInterval;
        public int ConnectionTimeout => connectionTimeout;
        public SupportedTransports Transport => transport;
        public string WebsocketPath => websocketPath;
        public bool AutoReconnect => autoReconnect;
        public int ReconnectDelay => reconnectDelay;
        public int MaxReconnectAttempts => maxReconnectAttempts;
        public string TopicPrefix => topicPrefix;
        public int QosLevel => qosLevel;
        public bool RetainMessages => retainMessages;
        
        /// <summary>
        /// 应用配置到MQTTManager
        /// </summary>
        public void ApplyToMQTTManager(MQTTManager mqttManager)
        {
            if (mqttManager == null)
            {
                Debug.LogError("MQTTManager为空，无法应用配置");
                return;
            }
            
            // 使用反射设置私有字段（仅用于配置应用）
            var type = typeof(MQTTManager);
            
            SetField(type, mqttManager, "brokerAddress", brokerAddress);
            SetField(type, mqttManager, "brokerPort", brokerPort);
            SetField(type, mqttManager, "clientId", clientId);
            SetField(type, mqttManager, "username", username);
            SetField(type, mqttManager, "password", password);
            SetField(type, mqttManager, "useSSL", useSSL);
            SetField(type, mqttManager, "keepAliveInterval", keepAliveInterval);
            SetField(type, mqttManager, "connectionTimeout", connectionTimeout);
            SetField(type, mqttManager, "transport", transport);
            SetField(type, mqttManager, "websocketPath", websocketPath);
            SetField(type, mqttManager, "autoReconnect", autoReconnect);
            SetField(type, mqttManager, "reconnectDelay", reconnectDelay);
            SetField(type, mqttManager, "maxReconnectAttempts", maxReconnectAttempts);
            SetField(type, mqttManager, "topicPrefix", topicPrefix);
            SetField(type, mqttManager, "qosLevel", qosLevel);
            SetField(type, mqttManager, "retainMessages", retainMessages);
            
            Debug.Log($"已应用MQTT配置预设: {presetName}");
        }
        
        /// <summary>
        /// 设置私有字段值
        /// </summary>
        private void SetField(Type type, object instance, string fieldName, object value)
        {
            var field = type.GetField(fieldName, System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            if (field != null)
            {
                field.SetValue(instance, value);
            }
            else
            {
                Debug.LogWarning($"未找到字段: {fieldName}");
            }
        }
        
        /// <summary>
        /// 创建默认预设
        /// </summary>
        [ContextMenu("创建默认预设")]
        public void CreateDefaultPresets()
        {
            // 这个方法可以在编辑器中调用来创建常用的预设配置
            Debug.Log("请在编辑器中手动创建预设配置文件");
        }
        
        /// <summary>
        /// 验证配置有效性
        /// </summary>
        public bool ValidateConfiguration()
        {
            if (string.IsNullOrEmpty(brokerAddress))
            {
                Debug.LogError("代理地址不能为空");
                return false;
            }
            
            if (brokerPort <= 0 || brokerPort > 65535)
            {
                Debug.LogError("端口号必须在1-65535范围内");
                return false;
            }
            
            if (string.IsNullOrEmpty(clientId))
            {
                Debug.LogError("客户端ID不能为空");
                return false;
            }
            
            if (keepAliveInterval < 0)
            {
                Debug.LogError("保活间隔不能为负数");
                return false;
            }
            
            if (connectionTimeout <= 0)
            {
                Debug.LogError("连接超时时间必须大于0");
                return false;
            }
            
            if (qosLevel < 0 || qosLevel > 2)
            {
                Debug.LogError("QoS等级必须在0-2范围内");
                return false;
            }
            
            return true;
        }
        
        /// <summary>
        /// 获取配置摘要
        /// </summary>
        public string GetConfigurationSummary()
        {
            return $"预设: {presetName}\n" +
                   $"代理: {brokerAddress}:{brokerPort}\n" +
                   $"传输: {transport}" + (useSSL ? " (SSL)" : "") + "\n" +
                   $"客户端: {clientId}\n" +
                   $"主题前缀: {topicPrefix}\n" +
                   $"QoS: {qosLevel}";
        }
    }
}
