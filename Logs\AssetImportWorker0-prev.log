Using pre-set license
Built from '6000.1/staging' branch; Version is '6000.1.7f1 (13a8ffad9172) revision 1288447'; Using compiler version '194234433'; Build Type 'Release'
OS: 'Windows 11  (10.0.22631) 64bit Professional' Language: 'zh' Physical Memory: 32673 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1
Date: 2025-06-30T07:30:04Z

COMMAND LINE ARGUMENTS:
E:\Unity\6000.1.7f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker0
-projectPath
F:/LongProjects/General Editor
-logFile
Logs/AssetImportWorker0.log
-srvPort
60349
-job-worker-count
3
-background-job-worker-count
8
-gc-helper-count
1
-name
AssetImport
Successfully changed project path to: F:/LongProjects/General Editor
F:/LongProjects/General Editor
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [27456]  Target information:

Player connection [27456]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 1859455627 [EditorId] 1859455627 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-D9EUJSV) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [27456] Host joined multi-casting on [***********:54997]...
Player connection [27456] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 3
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 251.91 ms, found 9 plugins.
Preloading 2 native plugins for Editor in 1.13 ms.
Initialize engine version: 6000.1.7f1 (13a8ffad9172)
[Subsystems] Discovering subsystems at path E:/Unity/6000.1.7f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path F:/LongProjects/General Editor/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:         Direct3D 12 [level 12.1]
    Renderer:        NVIDIA GeForce RTX 2070 SUPER (ID=0x1e84)
    Vendor:          NVIDIA
    VRAM:            7989 MB
    App VRAM Budget: 7221 MB
    Driver:          32.0.15.7680
Initialize mono
Mono path[0] = 'E:/Unity/6000.1.7f1/Editor/Data/Managed'
Mono path[1] = 'E:/Unity/6000.1.7f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'E:/Unity/6000.1.7f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56264
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: E:/Unity/6000.1.7f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.001201 seconds.
- Loaded All Assemblies, in  0.550 seconds
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.467 seconds
Domain Reload Profiling: 1016ms
	BeginReloadAssembly (150ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (45ms)
	RebuildNativeTypeToScriptingClass (15ms)
	initialDomainReloadingComplete (50ms)
	LoadAllAssembliesAndSetupDomain (288ms)
		LoadAssemblies (147ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (285ms)
			TypeCache.Refresh (284ms)
				TypeCache.ScanAssembly (269ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (468ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (378ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (61ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (89ms)
			ProcessInitializeOnLoadAttributes (164ms)
			ProcessInitializeOnLoadMethodAttributes (57ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.689 seconds
Refreshing native plugins compatible for Editor in 63.20 ms, found 9 plugins.
Native extension for WindowsStandalone target not found
Package Manager log level set to [2]
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Refreshing native plugins compatible for Editor in 56.40 ms, found 9 plugins.
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
[MODES] Loading mode Default (0) for mode-current-id-General Editor
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
AssertionException: Error removing KTX for Unity WebGL sub-packages.. Failed fetching installed packages.
Assertion failure. Values are not equal.
Expected: Failure == Success
  at UnityEngine.Assertions.Assert.Fail (System.String message, System.String userMessage) [0x00043] in <7fa9e22a6c4a40e5abbae8599fd0bce3>:0 
  at UnityEngine.Assertions.Assert.AreEqual[T] (T expected, T actual, System.String message, System.Collections.Generic.IEqualityComparer`1[T] comparer) [0x0005f] in <7fa9e22a6c4a40e5abbae8599fd0bce3>:0 
  at UnityEngine.Assertions.Assert.AreEqual[T] (T expected, T actual, System.String message) [0x00009] in <7fa9e22a6c4a40e5abbae8599fd0bce3>:0 
  at SubPackage.SubPackageRemover.GetAllInstalledPackagesAsync (System.Double timeout) [0x000b7] in .\Library\PackageCache\com.unity.cloud.ktx@9cc7165d9d20\Editor\Scripts\SubPackage\SubPackageRemover.cs:122 
  at SubPackage.SubPackageRemover.TryRemoveObsoleteSubPackagesAsync () [0x0002a] in .\Library\PackageCache\com.unity.cloud.ktx@9cc7165d9d20\Editor\Scripts\SubPackage\SubPackageRemover.cs:40 
UnityEngine.DebugLogHandler:Internal_LogException_Injected(Exception, IntPtr)
UnityEngine.DebugLogHandler:Internal_LogException(Exception, Object)
UnityEngine.DebugLogHandler:LogException(Exception, Object)
UnityEngine.Logger:LogException(Exception, Object)
UnityEngine.Debug:LogException(Exception)
SubPackage.<TryRemoveObsoleteSubPackagesAsync>d__5:MoveNext() (at .\Library\PackageCache\com.unity.cloud.ktx@9cc7165d9d20\Editor\Scripts\SubPackage\SubPackageRemover.cs:57)
System.Runtime.CompilerServices.AsyncTaskMethodBuilder:Start(<TryRemoveObsoleteSubPackagesAsync>d__5&)
SubPackage.SubPackageRemover:TryRemoveObsoleteSubPackagesAsync()
System.Reflection.RuntimeMethodInfo:InternalInvoke(RuntimeMethodInfo, Object, Object[], Exception&)
System.Reflection.RuntimeMethodInfo:Invoke(Object, BindingFlags, Binder, Object[], CultureInfo)
System.Reflection.MethodBase:Invoke(Object, Object[])
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes()

(Filename: ./Library/PackageCache/com.unity.cloud.ktx@9cc7165d9d20/Editor/Scripts/SubPackage/SubPackageRemover.cs Line: 57)

[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
AssertionException: Error removing Draco for Unity WebGL sub-packages.. Failed fetching installed packages.
Assertion failure. Values are not equal.
Expected: Failure == Success
  at UnityEngine.Assertions.Assert.Fail (System.String message, System.String userMessage) [0x00043] in <7fa9e22a6c4a40e5abbae8599fd0bce3>:0 
  at UnityEngine.Assertions.Assert.AreEqual[T] (T expected, T actual, System.String message, System.Collections.Generic.IEqualityComparer`1[T] comparer) [0x0005f] in <7fa9e22a6c4a40e5abbae8599fd0bce3>:0 
  at UnityEngine.Assertions.Assert.AreEqual[T] (T expected, T actual, System.String message) [0x00009] in <7fa9e22a6c4a40e5abbae8599fd0bce3>:0 
  at SubPackage.SubPackageRemover.GetAllInstalledPackagesAsync (System.Double timeout) [0x000b7] in .\Library\PackageCache\com.unity.cloud.draco@2ed1ef9a2677\Editor\Scripts\SubPackage\SubPackageRemover.cs:122 
  at SubPackage.SubPackageRemover.TryRemoveObsoleteSubPackagesAsync () [0x0002a] in .\Library\PackageCache\com.unity.cloud.draco@2ed1ef9a2677\Editor\Scripts\SubPackage\SubPackageRemover.cs:40 
UnityEngine.DebugLogHandler:Internal_LogException_Injected(Exception, IntPtr)
UnityEngine.DebugLogHandler:Internal_LogException(Exception, Object)
UnityEngine.DebugLogHandler:LogException(Exception, Object)
UnityEngine.Logger:LogException(Exception, Object)
UnityEngine.Debug:LogException(Exception)
SubPackage.<TryRemoveObsoleteSubPackagesAsync>d__5:MoveNext() (at .\Library\PackageCache\com.unity.cloud.draco@2ed1ef9a2677\Editor\Scripts\SubPackage\SubPackageRemover.cs:57)
System.Runtime.CompilerServices.AsyncTaskMethodBuilder:Start(<TryRemoveObsoleteSubPackagesAsync>d__5&)
SubPackage.SubPackageRemover:TryRemoveObsoleteSubPackagesAsync()
System.Reflection.RuntimeMethodInfo:InternalInvoke(RuntimeMethodInfo, Object, Object[], Exception&)
System.Reflection.RuntimeMethodInfo:Invoke(Object, BindingFlags, Binder, Object[], CultureInfo)
System.Reflection.MethodBase:Invoke(Object, Object[])
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes()

(Filename: ./Library/PackageCache/com.unity.cloud.draco@2ed1ef9a2677/Editor/Scripts/SubPackage/SubPackageRemover.cs Line: 57)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.291 seconds
Domain Reload Profiling: 2975ms
	BeginReloadAssembly (207ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (32ms)
	RebuildCommonClasses (85ms)
	RebuildNativeTypeToScriptingClass (28ms)
	initialDomainReloadingComplete (86ms)
	LoadAllAssembliesAndSetupDomain (1278ms)
		LoadAssemblies (752ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (653ms)
			TypeCache.Refresh (494ms)
				TypeCache.ScanAssembly (463ms)
			BuildScriptInfoCaches (120ms)
			ResolveRequiredComponents (33ms)
	FinalizeReload (1291ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1035ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (168ms)
			ProcessInitializeOnLoadAttributes (625ms)
			ProcessInitializeOnLoadMethodAttributes (232ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (9ms)
WARNING: Shader Unsupported: 'Hidden/PointCloud_GL' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/PointCloud_GL' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Launched and connected shader compiler UnityShaderCompiler.exe after 0.03 seconds
Refreshing native plugins compatible for Editor in 60.18 ms, found 9 plugins.
Preloading 2 native plugins for Editor in 0.11 ms.
Unloading 407 Unused Serialized files (Serialized files now loaded: 0)
Unloading 11088 unused Assets / (7.5 MB). Loaded Objects now: 11647.
Memory consumption went from 289.5 MB to 282.0 MB.
Total: 14.045000 ms (FindLiveObjects: 1.208100 ms CreateObjectMapping: 2.211400 ms MarkObjects: 6.589400 ms  DeleteObjects: 4.035100 ms)

========================================================================
Received Import Request.
  Time since last request: 23229.503177 seconds.
  path: Assets/Simulation/Scripts/SimulationManager.cs
  artifactKey: Guid(b5ba2bca0409b274589389079c686557) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/Simulation/Scripts/SimulationManager.cs using Guid(b5ba2bca0409b274589389079c686557) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) (ScriptedImporter) -> (artifact id: '763f0983b24fd9fffd317950532fd4fa') in 0.2902395 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/Simulation/Scripts/Core/SimulationEntityBase.cs
  artifactKey: Guid(00c0addff2f99f544a0e2e086c592d15) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/Simulation/Scripts/Core/SimulationEntityBase.cs using Guid(00c0addff2f99f544a0e2e086c592d15) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) (ScriptedImporter) -> (artifact id: '8a014d4c32bb5acf4e21752264a4a654') in 0.0057882 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000020 seconds.
  path: Assets/Simulation/Scripts/Tests/Core/EventSystemSimpleTests.cs
  artifactKey: Guid(6c1742f2fd1020e44a6cc899f66c1090) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/Simulation/Scripts/Tests/Core/EventSystemSimpleTests.cs using Guid(6c1742f2fd1020e44a6cc899f66c1090) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) (ScriptedImporter) -> (artifact id: '6fd6e33783ba16e8c8fe3b79606a3f8a') in 0.0094534 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000013 seconds.
  path: Assets/Simulation/Docs/原始需求.txt
  artifactKey: Guid(ae80068496ece394786fb96bce11260e) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/Simulation/Docs/原始需求.txt using Guid(ae80068496ece394786fb96bce11260e) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) (ScriptedImporter) -> (artifact id: 'a953995ba4a0e1cb93548f7f96bb3392') in 0.004963 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000012 seconds.
  path: Assets/GeneralEditor/Others/Boucy Physics Material.physicMaterial
  artifactKey: Guid(c3b2899e9eadc1348a8b3499b4e70472) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/GeneralEditor/Others/Boucy Physics Material.physicMaterial using Guid(c3b2899e9eadc1348a8b3499b4e70472) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) (ScriptedImporter) -> (artifact id: '1eb6b6982d833c4fe8a5ff84c600d69a') in 0.0208464 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000012 seconds.
  path: Assets/Simulation/Scripts/Core/Events/EventBase.cs
  artifactKey: Guid(f902204984884974c9e9b282a7d41536) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/Simulation/Scripts/Core/Events/EventBase.cs using Guid(f902204984884974c9e9b282a7d41536) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) (ScriptedImporter) -> (artifact id: 'e9538d32bff7a58ece34a4c41ec346a2') in 0.0043658 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000013 seconds.
  path: Assets/Simulation/Docs/需求文档.md
  artifactKey: Guid(f3d1645400ff908449e69a14ce068097) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/Simulation/Docs/需求文档.md using Guid(f3d1645400ff908449e69a14ce068097) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) (ScriptedImporter) -> (artifact id: 'bbc04777245bbc81d1ad8dee6e4aa904') in 0.0034629 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Simulation/Scripts/Tests/Core/InfrastructureTest.cs
  artifactKey: Guid(cb256310c715f0b468004e8fd5651bf2) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/Simulation/Scripts/Tests/Core/InfrastructureTest.cs using Guid(cb256310c715f0b468004e8fd5651bf2) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) (ScriptedImporter) -> (artifact id: '02e580c8e445509b85922844ef8af24d') in 0.0042068 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000013 seconds.
  path: Assets/GeneralEditor/Scenes/Test.unity
  artifactKey: Guid(748c8389276acce4496c69de72d0be39) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/GeneralEditor/Scenes/Test.unity using Guid(748c8389276acce4496c69de72d0be39) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) (ScriptedImporter) -> (artifact id: '573f3b9ae543cae31cf693a182393fa2') in 0.003578 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000012 seconds.
  path: Assets/Simulation/Docs/技术框架.md
  artifactKey: Guid(ab1b87ce242166444963785d66947d6f) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/Simulation/Docs/技术框架.md using Guid(ab1b87ce242166444963785d66947d6f) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) (ScriptedImporter) -> (artifact id: '06f6d4fb0dcd38959ac6917440dfe961') in 0.0037842 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.000013 seconds.
  path: Assets/GeneralEditor/Others
  artifactKey: Guid(5710aa8f793353d40a78fa3af8a57947) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23)
Start importing Assets/GeneralEditor/Others using Guid(5710aa8f793353d40a78fa3af8a57947) Importer(2089858483,0ecaa5967403d082aa24f35e1b516d23) (ScriptedImporter) -> (artifact id: 'fac2588e85ee7fe6b8bb4f7917d43c27') in 0.008015 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 85.325900 seconds.
  path: Assets/Simulation/Scripts/Materials/WarehouseManager.cs
  artifactKey: Guid(dd405edbe47cef946bb92288382d7e06) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Simulation/Scripts/Materials/WarehouseManager.cs using Guid(dd405edbe47cef946bb92288382d7e06) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '44f26ab6def1c5326d35cce1b81095c8') in 0.0006088 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 7.053533 seconds.
  path: Assets/Simulation/Scripts/Workers/Worker.cs
  artifactKey: Guid(a2cc87349cc520a4693118eb4d2bdbdb) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Simulation/Scripts/Workers/Worker.cs using Guid(a2cc87349cc520a4693118eb4d2bdbdb) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '84472d5e68c9abd543a31f0827330e25') in 0.0005323 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.731 seconds
Refreshing native plugins compatible for Editor in 66.98 ms, found 9 plugins.
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
AssertionException: Error removing KTX for Unity WebGL sub-packages.. Failed fetching installed packages.
Assertion failure. Values are not equal.
Expected: Failure == Success
  at UnityEngine.Assertions.Assert.Fail (System.String message, System.String userMessage) [0x00043] in <7fa9e22a6c4a40e5abbae8599fd0bce3>:0 
  at UnityEngine.Assertions.Assert.AreEqual[T] (T expected, T actual, System.String message, System.Collections.Generic.IEqualityComparer`1[T] comparer) [0x0005f] in <7fa9e22a6c4a40e5abbae8599fd0bce3>:0 
  at UnityEngine.Assertions.Assert.AreEqual[T] (T expected, T actual, System.String message) [0x00009] in <7fa9e22a6c4a40e5abbae8599fd0bce3>:0 
  at SubPackage.SubPackageRemover.GetAllInstalledPackagesAsync (System.Double timeout) [0x000da] in .\Library\PackageCache\com.unity.cloud.ktx@9cc7165d9d20\Editor\Scripts\SubPackage\SubPackageRemover.cs:122 
  at SubPackage.SubPackageRemover.TryRemoveObsoleteSubPackagesAsync () [0x00038] in .\Library\PackageCache\com.unity.cloud.ktx@9cc7165d9d20\Editor\Scripts\SubPackage\SubPackageRemover.cs:40 
UnityEngine.DebugLogHandler:Internal_LogException_Injected(Exception, IntPtr)
UnityEngine.DebugLogHandler:Internal_LogException(Exception, Object)
UnityEngine.DebugLogHandler:LogException(Exception, Object)
UnityEngine.Logger:LogException(Exception, Object)
UnityEngine.Debug:LogException(Exception)
SubPackage.<TryRemoveObsoleteSubPackagesAsync>d__5:MoveNext() (at .\Library\PackageCache\com.unity.cloud.ktx@9cc7165d9d20\Editor\Scripts\SubPackage\SubPackageRemover.cs:57)
System.Runtime.CompilerServices.AsyncTaskMethodBuilder:Start(<TryRemoveObsoleteSubPackagesAsync>d__5&)
SubPackage.SubPackageRemover:TryRemoveObsoleteSubPackagesAsync()
System.Reflection.RuntimeMethodInfo:InternalInvoke(RuntimeMethodInfo, Object, Object[], Exception&)
System.Reflection.RuntimeMethodInfo:Invoke(Object, BindingFlags, Binder, Object[], CultureInfo)
System.Reflection.MethodBase:Invoke(Object, Object[])
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes()

(Filename: ./Library/PackageCache/com.unity.cloud.ktx@9cc7165d9d20/Editor/Scripts/SubPackage/SubPackageRemover.cs Line: 57)

[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
AssertionException: Error removing Draco for Unity WebGL sub-packages.. Failed fetching installed packages.
Assertion failure. Values are not equal.
Expected: Failure == Success
  at UnityEngine.Assertions.Assert.Fail (System.String message, System.String userMessage) [0x00043] in <7fa9e22a6c4a40e5abbae8599fd0bce3>:0 
  at UnityEngine.Assertions.Assert.AreEqual[T] (T expected, T actual, System.String message, System.Collections.Generic.IEqualityComparer`1[T] comparer) [0x0005f] in <7fa9e22a6c4a40e5abbae8599fd0bce3>:0 
  at UnityEngine.Assertions.Assert.AreEqual[T] (T expected, T actual, System.String message) [0x00009] in <7fa9e22a6c4a40e5abbae8599fd0bce3>:0 
  at SubPackage.SubPackageRemover.GetAllInstalledPackagesAsync (System.Double timeout) [0x000da] in .\Library\PackageCache\com.unity.cloud.draco@2ed1ef9a2677\Editor\Scripts\SubPackage\SubPackageRemover.cs:122 
  at SubPackage.SubPackageRemover.TryRemoveObsoleteSubPackagesAsync () [0x00038] in .\Library\PackageCache\com.unity.cloud.draco@2ed1ef9a2677\Editor\Scripts\SubPackage\SubPackageRemover.cs:40 
UnityEngine.DebugLogHandler:Internal_LogException_Injected(Exception, IntPtr)
UnityEngine.DebugLogHandler:Internal_LogException(Exception, Object)
UnityEngine.DebugLogHandler:LogException(Exception, Object)
UnityEngine.Logger:LogException(Exception, Object)
UnityEngine.Debug:LogException(Exception)
SubPackage.<TryRemoveObsoleteSubPackagesAsync>d__5:MoveNext() (at .\Library\PackageCache\com.unity.cloud.draco@2ed1ef9a2677\Editor\Scripts\SubPackage\SubPackageRemover.cs:57)
System.Runtime.CompilerServices.AsyncTaskMethodBuilder:Start(<TryRemoveObsoleteSubPackagesAsync>d__5&)
SubPackage.SubPackageRemover:TryRemoveObsoleteSubPackagesAsync()
System.Reflection.RuntimeMethodInfo:InternalInvoke(RuntimeMethodInfo, Object, Object[], Exception&)
System.Reflection.RuntimeMethodInfo:Invoke(Object, BindingFlags, Binder, Object[], CultureInfo)
System.Reflection.MethodBase:Invoke(Object, Object[])
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadMethodAttributes()

(Filename: ./Library/PackageCache/com.unity.cloud.draco@2ed1ef9a2677/Editor/Scripts/SubPackage/SubPackageRemover.cs Line: 57)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.430 seconds
Domain Reload Profiling: 3161ms
	BeginReloadAssembly (579ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (14ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (200ms)
	RebuildCommonClasses (45ms)
	RebuildNativeTypeToScriptingClass (20ms)
	initialDomainReloadingComplete (77ms)
	LoadAllAssembliesAndSetupDomain (1010ms)
		LoadAssemblies (490ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (625ms)
			TypeCache.Refresh (275ms)
				TypeCache.ScanAssembly (253ms)
			BuildScriptInfoCaches (315ms)
			ResolveRequiredComponents (29ms)
	FinalizeReload (1430ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1151ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (9ms)
			BeforeProcessingInitializeOnLoad (379ms)
			ProcessInitializeOnLoadAttributes (545ms)
			ProcessInitializeOnLoadMethodAttributes (210ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (20ms)
WARNING: Shader Unsupported: 'Hidden/PointCloud_GL' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/PointCloud_GL' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 59.22 ms, found 9 plugins.
Preloading 2 native plugins for Editor in 0.08 ms.
Unloading 25 Unused Serialized files (Serialized files now loaded: 0)
Unloading 11086 unused Assets / (7.2 MB). Loaded Objects now: 11654.
Memory consumption went from 227.2 MB to 220.0 MB.
Total: 15.475700 ms (FindLiveObjects: 1.612800 ms CreateObjectMapping: 3.032600 ms MarkObjects: 6.693200 ms  DeleteObjects: 4.135900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
WARNING: Shader Unsupported: 'Hidden/PointCloud_GL' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Hidden/PointCloud_GL' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 83.52 ms, found 9 plugins.
Preloading 2 native plugins for Editor in 0.16 ms.
Unloading 23 Unused Serialized files (Serialized files now loaded: 0)
Unloading 10654 unused Assets / (6.9 MB). Loaded Objects now: 11606.
Memory consumption went from 230.2 MB to 223.2 MB.
Total: 26.015500 ms (FindLiveObjects: 1.415900 ms CreateObjectMapping: 2.912600 ms MarkObjects: 16.998500 ms  DeleteObjects: 4.687200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 4910.947709 seconds.
  path: Assets/Simulation/Scripts/SimulationManager.cs
  artifactKey: Guid(b5ba2bca0409b274589389079c686557) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Simulation/Scripts/SimulationManager.cs using Guid(b5ba2bca0409b274589389079c686557) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd85be49507941c90a05c0b78c5763b4d') in 0.0026655 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.288054 seconds.
  path: Assets/Simulation/Scripts/Simulation.asmdef
  artifactKey: Guid(4d8cf6bc911efd64aa72bf708c2ba9f4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Simulation/Scripts/Simulation.asmdef using Guid(4d8cf6bc911efd64aa72bf708c2ba9f4) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c896dcaf059da0f0bf446b8db0b3a593') in 0.0005381 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 1.589491 seconds.
  path: Assets/Simulation/Scripts/SimulatorBase.cs
  artifactKey: Guid(a2b6b3d4ce201ff4ca02c4ef8507f844) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Simulation/Scripts/SimulatorBase.cs using Guid(a2b6b3d4ce201ff4ca02c4ef8507f844) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '619fad61b050259722869a3526caa456') in 0.0007796 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

