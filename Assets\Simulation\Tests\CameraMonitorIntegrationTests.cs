using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.TestTools;
using NUnit.Framework;
using Cysharp.Threading.Tasks;
using System.Threading;
using Simulation.IoT;

namespace Simulation.Tests
{
    /// <summary>
    /// 监控摄像头集成测试
    /// 验证监控摄像头模拟器与整体系统的集成
    /// </summary>
    public class CameraMonitorIntegrationTests : TestBase
    {
        private IoTSystem iotSystem;
        private CameraMonitorSimulator cameraSimulator;
        private GameObject targetObject;
        
        [SetUp]
        public override void SetUp()
        {
            base.SetUp();
            
            // 获取IoT系统实例
            iotSystem = IoTSystem.Instance;
            Assert.IsNotNull(iotSystem, "IoT系统实例不应为空");
            
            // 创建监控摄像头模拟器
            cameraSimulator = CreateTestComponent<CameraMonitorSimulator>();
            
            // 创建目标对象
            targetObject = new GameObject("TestTarget");
            targetObject.transform.position = Vector3.zero;
        }
        
        [TearDown]
        public override void TearDown()
        {
            if (targetObject != null)
            {
                Object.DestroyImmediate(targetObject);
            }
            base.TearDown();
        }
        
        /// <summary>
        /// 测试摄像头模拟器基础功能
        /// </summary>
        [UnityTest]
        public IEnumerator TestCameraSimulatorBasics()
        {
            // 配置摄像头
            cameraSimulator.cameraId = "CAM_001";
            cameraSimulator.cameraName = "测试摄像头";
            cameraSimulator.monitoringRange = 10f;
            cameraSimulator.fieldOfView = 60f;
            
            Assert.IsFalse(cameraSimulator.IsRunning, "摄像头初始状态应该是停止的");
            
            // 启动摄像头模拟器
            cameraSimulator.StartSimulationAsync().Forget();
            yield return new WaitForSeconds(0.1f);
            
            Assert.IsTrue(cameraSimulator.IsRunning, "摄像头应该正在运行");
            
            // 停止摄像头模拟器
            cameraSimulator.StopSimulation();
            yield return new WaitForSeconds(0.1f);
            
            Assert.IsFalse(cameraSimulator.IsRunning, "摄像头应该已停止");
        }
        
        /// <summary>
        /// 测试摄像头数据生成和上报
        /// </summary>
        [UnityTest]
        public IEnumerator TestCameraDataGeneration()
        {
            // 配置摄像头
            cameraSimulator.cameraId = "CAM_002";
            cameraSimulator.dataCollectionInterval = 0.5f;
            
            bool dataGenerated = false;
            CameraData receivedData = null;
            
            // 监听数据生成事件
            cameraSimulator.OnDataGenerated += (id, data) =>
            {
                if (data is CameraData camData)
                {
                    dataGenerated = true;
                    receivedData = camData;
                }
            };
            
            // 启动摄像头
            cameraSimulator.StartSimulationAsync().Forget();
            
            // 等待数据生成
            yield return WaitForCondition(() => dataGenerated, 2f, "摄像头数据生成超时");
            
            Assert.IsTrue(dataGenerated, "应该生成摄像头数据");
            Assert.IsNotNull(receivedData, "接收到的数据不应为空");
            Assert.AreEqual("CAM_002", receivedData.cameraId, "摄像头ID应该匹配");
            Assert.AreEqual("camera", receivedData.sensorType, "传感器类型应该是camera");
            
            cameraSimulator.StopSimulation();
        }
        
        /// <summary>
        /// 测试摄像头监控范围检测
        /// </summary>
        [UnityTest]
        public IEnumerator TestCameraMonitoringRange()
        {
            // 配置摄像头
            cameraSimulator.cameraId = "CAM_003";
            cameraSimulator.monitoringRange = 5f;
            cameraSimulator.transform.position = Vector3.zero;
            
            // 将目标对象放在监控范围内
            targetObject.transform.position = new Vector3(3f, 0f, 0f);
            
            bool targetDetected = false;
            
            // 监听目标检测事件
            cameraSimulator.OnTargetDetected += (target) =>
            {
                if (target == targetObject)
                {
                    targetDetected = true;
                }
            };
            
            // 启动摄像头
            cameraSimulator.StartSimulationAsync().Forget();
            
            // 等待目标检测
            yield return WaitForCondition(() => targetDetected, 3f, "目标检测超时");
            
            Assert.IsTrue(targetDetected, "应该检测到监控范围内的目标");
            
            cameraSimulator.StopSimulation();
        }
        
        /// <summary>
        /// 测试摄像头异常事件检测
        /// </summary>
        [UnityTest]
        public IEnumerator TestCameraAnomalyDetection()
        {
            // 配置摄像头
            cameraSimulator.cameraId = "CAM_004";
            cameraSimulator.anomalyDetectionRate = 1.0f; // 100%概率检测异常
            
            bool anomalyDetected = false;
            string anomalyType = null;
            
            // 监听异常检测事件
            cameraSimulator.OnAnomalyDetected += (type, description) =>
            {
                anomalyDetected = true;
                anomalyType = type;
            };
            
            // 启动摄像头
            cameraSimulator.StartSimulationAsync().Forget();
            
            // 等待异常检测
            yield return WaitForCondition(() => anomalyDetected, 5f, "异常检测超时");
            
            Assert.IsTrue(anomalyDetected, "应该检测到异常事件");
            Assert.IsNotNull(anomalyType, "异常类型不应为空");
            
            cameraSimulator.StopSimulation();
        }
        
        /// <summary>
        /// 测试摄像头与IoT系统集成
        /// </summary>
        [UnityTest]
        public IEnumerator TestCameraIoTIntegration()
        {
            // 配置摄像头
            cameraSimulator.cameraId = "CAM_005";
            cameraSimulator.dataCollectionInterval = 0.5f;
            
            bool iotEventSent = false;
            string eventType = null;
            
            // 监听IoT事件（通过模拟器的事件发送）
            cameraSimulator.OnEventSent += (type, data) =>
            {
                iotEventSent = true;
                eventType = type;
            };
            
            // 启动摄像头
            cameraSimulator.StartSimulationAsync().Forget();
            
            // 触发事件发送
            cameraSimulator.TriggerTestEvent();
            
            // 等待IoT事件发送
            yield return WaitForCondition(() => iotEventSent, 3f, "IoT事件发送超时");
            
            Assert.IsTrue(iotEventSent, "应该发送IoT事件");
            Assert.IsNotNull(eventType, "事件类型不应为空");
            
            cameraSimulator.StopSimulation();
        }
        
        /// <summary>
        /// 测试摄像头云台控制功能
        /// </summary>
        [UnityTest]
        public IEnumerator TestCameraPanTiltControl()
        {
            // 配置摄像头
            cameraSimulator.cameraId = "CAM_006";
            cameraSimulator.supportPanTilt = true;
            
            // 记录初始角度
            Vector3 initialRotation = cameraSimulator.transform.eulerAngles;
            
            // 执行云台控制
            float targetPan = 45f;
            float targetTilt = 30f;
            
            cameraSimulator.SetPanTilt(targetPan, targetTilt);
            
            // 等待云台移动完成
            yield return new WaitForSeconds(1f);
            
            // 验证云台角度
            Vector3 currentRotation = cameraSimulator.transform.eulerAngles;
            
            // 由于Unity的角度系统，需要进行适当的比较
            AssertApproximately(targetPan, currentRotation.y, 5f, "水平角度应该匹配");
            AssertApproximately(targetTilt, currentRotation.x, 5f, "垂直角度应该匹配");
        }
        
        /// <summary>
        /// 测试摄像头录像模拟功能
        /// </summary>
        [UnityTest]
        public IEnumerator TestCameraRecordingSimulation()
        {
            // 配置摄像头
            cameraSimulator.cameraId = "CAM_007";
            cameraSimulator.supportRecording = true;
            
            Assert.IsFalse(cameraSimulator.IsRecording, "初始状态应该不在录像");
            
            // 开始录像
            cameraSimulator.StartRecording();
            Assert.IsTrue(cameraSimulator.IsRecording, "应该开始录像");
            
            // 等待一段时间
            yield return new WaitForSeconds(1f);
            
            // 停止录像
            cameraSimulator.StopRecording();
            Assert.IsFalse(cameraSimulator.IsRecording, "应该停止录像");
            
            // 验证录像时长
            float recordingDuration = cameraSimulator.GetLastRecordingDuration();
            Assert.Greater(recordingDuration, 0.5f, "录像时长应该大于0.5秒");
        }
        
        /// <summary>
        /// 测试多摄像头协同工作
        /// </summary>
        [UnityTest]
        public IEnumerator TestMultipleCamerasCoordination()
        {
            // 创建第二个摄像头
            var camera2 = CreateTestComponent<CameraMonitorSimulator>();
            
            // 配置两个摄像头
            cameraSimulator.cameraId = "CAM_008A";
            camera2.cameraId = "CAM_008B";
            
            cameraSimulator.transform.position = new Vector3(-5f, 0f, 0f);
            camera2.transform.position = new Vector3(5f, 0f, 0f);
            
            int dataCount = 0;
            
            // 监听两个摄像头的数据生成
            System.Action<string, object> dataHandler = (id, data) => dataCount++;
            cameraSimulator.OnDataGenerated += dataHandler;
            camera2.OnDataGenerated += dataHandler;
            
            // 启动两个摄像头
            cameraSimulator.StartSimulationAsync().Forget();
            camera2.StartSimulationAsync().Forget();
            
            // 等待数据生成
            yield return WaitForCondition(() => dataCount >= 2, 3f, "多摄像头数据生成超时");
            
            Assert.GreaterOrEqual(dataCount, 2, "应该从两个摄像头接收到数据");
            
            // 停止摄像头
            cameraSimulator.StopSimulation();
            camera2.StopSimulation();
        }
    }
}
