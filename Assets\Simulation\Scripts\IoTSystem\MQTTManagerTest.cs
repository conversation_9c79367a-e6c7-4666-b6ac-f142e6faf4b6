using System;
using UnityEngine;
using Cysharp.Threading.Tasks;

namespace Simulation.IoT
{
    /// <summary>
    /// MQTT管理器测试脚本
    /// 用于验证BestMQTT集成的功能
    /// </summary>
    public class MQTTManagerTest : MonoBehaviour
    {
        [Header("测试配置")]
        [SerializeField] private bool autoStartTest = false;
        [SerializeField] private string testTopic = "test/simulation";
        [SerializeField] private float publishInterval = 5f;
        
        private MQTTManager mqttManager;
        private bool isTestRunning = false;
        private int messageCounter = 0;
        
        private void Start()
        {
            // 获取或创建MQTT管理器
            mqttManager = GetComponent<MQTTManager>();
            if (mqttManager == null)
            {
                mqttManager = gameObject.AddComponent<MQTTManager>();
            }
            
            // 订阅事件
            mqttManager.OnConnected += OnMQTTConnected;
            mqttManager.OnDisconnected += OnMQTTDisconnected;
            mqttManager.OnConnectionError += OnMQTTError;
            mqttManager.OnMessageReceived += OnMQTTMessageReceived;
            mqttManager.OnMessagePublished += OnMQTTMessagePublished;
            
            if (autoStartTest)
            {
                StartTest();
            }
        }
        
        private void OnDestroy()
        {
            if (mqttManager != null)
            {
                mqttManager.OnConnected -= OnMQTTConnected;
                mqttManager.OnDisconnected -= OnMQTTDisconnected;
                mqttManager.OnConnectionError -= OnMQTTError;
                mqttManager.OnMessageReceived -= OnMQTTMessageReceived;
                mqttManager.OnMessagePublished -= OnMQTTMessagePublished;
            }
        }
        
        [ContextMenu("开始测试")]
        public void StartTest()
        {
            if (isTestRunning)
            {
                Debug.LogWarning("[MQTTTest] 测试已在运行中");
                return;
            }
            
            Debug.Log("[MQTTTest] 开始MQTT测试");
            TestMQTTAsync().Forget();
        }
        
        [ContextMenu("停止测试")]
        public void StopTest()
        {
            isTestRunning = false;
            Debug.Log("[MQTTTest] 停止MQTT测试");
        }
        
        private async UniTaskVoid TestMQTTAsync()
        {
            try
            {
                isTestRunning = true;
                
                // 1. 测试连接
                Debug.Log("[MQTTTest] 测试MQTT连接...");
                bool connected = await mqttManager.ConnectAsync();
                
                if (!connected)
                {
                    Debug.LogError("[MQTTTest] MQTT连接失败");
                    isTestRunning = false;
                    return;
                }
                
                // 等待连接稳定
                await UniTask.Delay(1000);
                
                // 2. 测试订阅
                Debug.Log("[MQTTTest] 测试主题订阅...");
                bool subscribed = mqttManager.SubscribeToTopic(testTopic);
                
                if (!subscribed)
                {
                    Debug.LogError("[MQTTTest] 主题订阅失败");
                }
                
                // 3. 测试发布消息
                Debug.Log("[MQTTTest] 开始发布测试消息...");
                
                while (isTestRunning && mqttManager.IsConnected)
                {
                    messageCounter++;
                    
                    var testMessage = new TestMessage
                    {
                        Id = messageCounter,
                        Timestamp = DateTime.UtcNow,
                        Content = $"测试消息 #{messageCounter}",
                        Source = "MQTTManagerTest"
                    };
                    
                    bool published = mqttManager.PublishMessage(testTopic, testMessage, false);
                    
                    if (published)
                    {
                        Debug.Log($"[MQTTTest] 发布消息 #{messageCounter}");
                    }
                    else
                    {
                        Debug.LogError($"[MQTTTest] 发布消息失败 #{messageCounter}");
                    }
                    
                    await UniTask.Delay(TimeSpan.FromSeconds(publishInterval));
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"[MQTTTest] 测试异常: {ex.Message}");
            }
            finally
            {
                isTestRunning = false;
            }
        }
        
        #region MQTT事件处理
        
        private void OnMQTTConnected()
        {
            Debug.Log("[MQTTTest] MQTT连接成功");
        }
        
        private void OnMQTTDisconnected()
        {
            Debug.Log("[MQTTTest] MQTT连接断开");
            isTestRunning = false;
        }
        
        private void OnMQTTError(string error)
        {
            Debug.LogError($"[MQTTTest] MQTT错误: {error}");
        }
        
        private void OnMQTTMessageReceived(string topic, string message)
        {
            Debug.Log($"[MQTTTest] 收到消息 - 主题: {topic}, 内容: {message}");
        }
        
        private void OnMQTTMessagePublished(string topic)
        {
            Debug.Log($"[MQTTTest] 消息已发布到主题: {topic}");
        }
        
        #endregion
    }
    
    /// <summary>
    /// 测试消息结构
    /// </summary>
    [Serializable]
    public class TestMessage
    {
        public int Id;
        public DateTime Timestamp;
        public string Content;
        public string Source;
    }
}
