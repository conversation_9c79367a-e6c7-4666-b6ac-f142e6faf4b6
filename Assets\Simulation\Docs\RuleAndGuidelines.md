# Unity建筑工地数字孪生模拟系统 - 项目规则与指导

## 📋 项目概述

本项目是基于Unity引擎构建的建筑工地数字孪生模拟沙盘系统，专注于生成测试数据。采用简化的模块化架构设计，避免过度工程化，确保系统的简单性和可维护性。

## 🎯 核心设计原则

### 1. 技术原则
- **简单高效，敏捷开发**：专注业务逻辑实现，避免复杂的架构设计
- **数据优先，模拟为本**：专注于生成高质量的结构化数据
- **直接方法调用**：使用传统的方法调用，移除复杂的事件系统
- **异步编程优先**：采用异步编程模式替代传统状态机，保持业务逻辑高内聚性
- **技术精简**：基于现有Unity框架和UniTask，避免引入额外技术栈
- **渐进式开发**：先实现核心功能，后续迭代优化

### 2. 架构偏好
- **避免过度抽象**：不使用复杂的接口和继承层次
- **直接实现业务逻辑**：专注于实际功能而非抽象架构模式
- **简化核心框架**：避免过度工程化和复杂性
- **统一异步模式**：使用UniTask实现高性能异步操作

## 🏗️ 项目结构

### 文件目录结构
```
Assets/Simulation/
├── Docs/                           # 项目文档
│   ├── 需求文档.md                 # 业务需求说明
│   ├── 开发指导.md                 # 开发规范指导
│   ├── 项目业务逻辑.md             # 业务逻辑文档
│   ├── RuleAndGuidelines.md        # 本文档
│   └── QuickReference.md           # 快速参考卡片
├── Scripts/                        # 核心代码
│   ├── Data/                       # 数据结构定义
│   ├── IoTSystem/                  # IoT系统核心
│   ├── Sensors/                    # 传感器系统
│   ├── Simulators/                 # 业务模拟器
│   ├── Systems/                    # 辅助系统
│   └── SimulationManager.cs        # 主管理器
└── Tests/                          # 测试框架
    ├── TestDataTypes.cs            # 统一测试数据类型
    ├── *Tests.cs                   # 各种测试类
    └── README.md                   # 测试文档
```

### 系统架构图
项目采用分层模块化架构，详细的系统架构图已在上方的Mermaid图表中展示。核心架构包括：

- **核心管理层**：SimulationManager、IoTSystem、MQTTManager
- **传感器系统**：各种专用传感器继承自SensorBase
- **业务模拟器**：各种业务模拟器继承自SimulatorBase
- **辅助系统**：门禁、监控等支持系统
- **数据层**：统一的数据结构和事件类型定义
- **测试框架**：完整的单元测试和集成测试体系

## 🔧 开发规范

### 1. 命名规范
- **命名空间**：避免命名空间名称与类型名称重复
  ```csharp
  // ✅ 正确
  namespace Simulation.IoTSystem
  public class IoTSystem { }
  
  // ❌ 错误 - 命名冲突
  namespace Simulation.IoTSystem
  public class IoTSystem { }
  ```

- **类名**：使用PascalCase，描述性命名
- **方法名**：使用PascalCase，动词开头
- **字段名**：私有字段使用camelCase，公共字段使用PascalCase
- **常量**：使用UPPER_CASE

### 2. 异步编程规范
- **优先使用UniTask**：替代传统的协程和状态机
- **正确的超时处理**：使用TimeoutController或CancellationTokenSource.CancelAfterSlim
  ```csharp
  // ✅ 推荐的超时处理
  using var timeoutController = new TimeoutController();
  await SomeAsyncOperation().WithCancellation(timeoutController.Timeout(TimeSpan.FromSeconds(5)));
  
  // ❌ 避免使用WhenAny方式
  ```

- **统一取消机制**：使用CancellationToken进行流程控制
- **避免状态机**：直接使用异步方法组织业务逻辑

### 3. 数据结构规范
- **简化构造函数**：避免复杂的参数传递
  ```csharp
  // ✅ 简化的构造函数
  public class SensorData : SensorDataBase
  {
      public SensorData() : base("sensor_type") { }
  }
  ```

- **统一基类设计**：硬编码必要参数，减少配置复杂性
- **序列化友好**：确保所有数据类都支持Unity的JsonUtility

### 4. 测试规范
- **统一测试数据类型**：使用TestDataTypes.cs中定义的类型
- **避免重复定义**：不在测试文件中重复定义相同的测试类
- **测试环境隔离**：使用SimulationManager.IsInTestEnvironment区分环境
- **异步测试模式**：使用IEnumerator和UniTask进行异步测试

## 📦 依赖管理

### 1. 包管理器使用
- **始终使用包管理器**：安装、更新、移除依赖时使用npm、pip等工具
- **避免手动编辑**：不直接编辑package.json、requirements.txt等配置文件
- **版本控制**：让包管理器自动处理版本冲突和依赖关系

### 2. 核心依赖
- **UniTask**：异步编程框架
- **BestMQTT**：MQTT通信插件
- **Unity Test Framework**：单元测试框架

## 🧪 测试策略

### 1. 测试层次
- **单元测试**：测试独立组件功能
- **集成测试**：测试组件间协作
- **系统测试**：测试完整业务流程

### 2. 测试覆盖率要求
- **最低覆盖率**：80%
- **关键组件**：IoT系统、传感器系统、模拟器系统必须100%覆盖
- **测试验证**：使用TestCoverageValidator验证覆盖率

### 3. 测试执行
- **编译验证**：每个子任务完成后必须验证编译通过
- **渐进式测试**：逐步执行测试，及时发现问题
- **自动化运行**：使用IntegrationTestRunner执行完整测试套件

## 🔄 开发流程

### 1. 任务执行模式
- **分步执行**：将复杂任务分解为小的子任务
- **编译验证**：每个子任务完成后验证编译状态
- **任务总结**：每个任务完成后提供总结供审查调整

### 2. 代码修改流程
1. **信息收集**：使用codebase-retrieval获取详细上下文
2. **谨慎修改**：尊重现有代码结构，保守修改
3. **编译检查**：修改后立即检查编译状态
4. **测试验证**：运行相关测试确保功能正常

### 3. 问题解决策略
- **重复调用检测**：如果多次调用相同工具无进展，主动寻求帮助
- **错误处理**：遇到编译错误时，优先修复而非重写
- **渐进式修复**：分步骤解决复杂问题

## 📝 配置管理

### 1. Unity配置偏好
- **序列化字段**：使用Unity的SerializeField和Inspector面板配置参数
- **避免外部配置文件**：不使用JSON、XML等外部配置文件
- **Inspector友好**：确保重要参数在Inspector中可见可调

### 2. 环境配置
- **测试环境标识**：使用SimulationManager.IsInTestEnvironment区分环境
- **条件化行为**：根据环境标识调整组件行为
- **资源管理**：测试环境下避免不必要的资源消耗

## 🚀 性能优化

### 1. 异步性能
- **零分配异步**：基于UniTask实现高性能异步操作
- **合理的更新频率**：根据业务需求设置合适的采样率
- **资源释放**：正确使用CancellationToken释放资源

### 2. 内存管理
- **对象池**：对频繁创建的对象使用对象池
- **及时释放**：异步操作完成后及时释放资源
- **避免内存泄漏**：正确取消订阅事件和异步操作

## 📚 文档要求

### 1. 代码文档
- **XML注释**：所有公共API必须有XML文档注释
- **业务逻辑说明**：复杂业务逻辑需要详细注释
- **示例代码**：关键API提供使用示例

### 2. 项目文档
- **需求文档**：详细的业务需求说明
- **开发指导**：技术实现指导
- **测试文档**：测试框架使用说明

## ⚠️ 常见陷阱

### 1. 避免的模式
- **复杂继承层次**：避免深层次的继承关系
- **过度抽象**：不为了抽象而抽象
- **状态机滥用**：优先使用异步方法而非状态机
- **事件系统复杂化**：保持事件系统简单直接

### 2. 编译问题预防
- **命名空间冲突**：注意类名与命名空间的冲突
- **重复定义**：避免在多个文件中定义相同的类
- **依赖循环**：注意模块间的依赖关系

## 🔍 调试和监控

### 1. 日志规范
- **分级日志**：使用Debug.Log、Debug.LogWarning、Debug.LogError
- **条件日志**：测试环境下的特殊日志处理
- **性能日志**：关键操作的性能监控

### 2. 调试工具
- **Context Menu**：为关键组件提供Inspector调试菜单
- **测试运行器**：使用专门的测试运行器进行调试
- **覆盖率验证**：定期检查测试覆盖率

## 📞 支持和维护

### 1. 问题报告
- **详细描述**：提供完整的错误信息和重现步骤
- **环境信息**：包含Unity版本、平台信息
- **相关代码**：提供相关的代码片段

### 2. 版本管理
- **语义化版本**：使用语义化版本号
- **变更日志**：详细记录每个版本的变更
- **向后兼容**：尽量保持API的向后兼容性

---

## 📖 快速上手指南

### 新人接手项目的步骤：

1. **阅读文档**：
   - 先读本文档了解项目规则
   - 阅读需求文档了解业务背景
   - 查看开发指导了解技术细节

2. **环境搭建**：
   - 安装Unity 2022.3 LTS
   - 导入UniTask和BestMQTT插件
   - 配置测试环境

3. **代码熟悉**：
   - 从SimulationManager开始了解整体架构
   - 查看IoTSystem了解核心系统
   - 运行测试了解各组件功能

4. **开发实践**：
   - 遵循异步编程模式
   - 使用统一的测试数据类型
   - 每次修改后验证编译和测试

记住：**简单高效，专注业务，渐进迭代**是本项目的核心理念！

## 🛠️ 技术实现细节

### 1. IoT系统架构
```csharp
// IoT系统的标准使用模式
var iotSystem = IoTSystem.Instance;

// 传感器注册（非测试环境自动注册）
if (!SimulationManager.IsInTestEnvironment)
{
    iotSystem.RegisterSensor(sensor);
}

// 数据收集
iotSystem.CollectSensorData(sensorId, sensorData);

// 事件发送
iotSystem.SendIOTEvent(sourceId, eventType, title, description, eventData);
```

### 2. 传感器实现模式
```csharp
public class CustomSensor : SensorBase
{
    public CustomSensor() : base("custom_sensor_type")
    {
        sampleRate = 1f;
        autoStart = true;
        enableDebugLog = false;
    }

    protected override void CollectData()
    {
        var data = new CustomSensorData("custom_sensor_type");
        OnDataGenerated?.Invoke(SensorId, data);
    }
}
```

### 3. 模拟器实现模式
```csharp
public class CustomSimulator : SimulatorBase
{
    public CustomSimulator() : base("custom_simulator")
    {
        updateInterval = 1f;
    }

    protected override async UniTask SimulationLoop(CancellationToken cancellationToken)
    {
        while (!cancellationToken.IsCancellationRequested)
        {
            // 执行模拟逻辑
            await PerformSimulationStep();

            // 等待下一次更新
            await UniTask.Delay(TimeSpan.FromSeconds(updateInterval), cancellationToken: cancellationToken);
        }
    }
}
```

### 4. MQTT集成模式
```csharp
// MQTT管理器配置
mqttManager.brokerAddress = "your.mqtt.broker.com";
mqttManager.brokerPort = 1883;
mqttManager.clientId = "unique_client_id";
mqttManager.useSSL = false;
mqttManager.autoReconnect = true;

// 启动连接
mqttManager.StartConnection();

// 监听连接状态
mqttManager.OnConnected += () => Debug.Log("MQTT已连接");
mqttManager.OnDisconnected += () => Debug.Log("MQTT已断开");
```

## 🎨 UI和Inspector配置

### 1. Inspector配置最佳实践
```csharp
[Header("基础配置")]
[SerializeField] private float sampleRate = 1f;
[SerializeField] private bool autoStart = true;

[Header("调试选项")]
[SerializeField] private bool enableDebugLog = false;

[Header("运行时状态")]
[SerializeField, ReadOnly] private bool isRunning = false;
```

### 2. Context Menu使用
```csharp
[ContextMenu("启动模拟器")]
public void StartSimulationFromMenu()
{
    StartSimulationAsync().Forget();
}

[ContextMenu("停止模拟器")]
public void StopSimulationFromMenu()
{
    StopSimulation();
}

[ContextMenu("重置状态")]
public void ResetSimulationFromMenu()
{
    ResetSimulation();
}
```

## 🔬 测试最佳实践

### 1. 单元测试模板
```csharp
[UnityTest]
public IEnumerator TestAsyncOperation()
{
    // 准备测试数据
    var testComponent = CreateTestComponent<TestComponent>();

    // 设置测试条件
    bool operationCompleted = false;
    testComponent.OnOperationComplete += () => operationCompleted = true;

    // 执行异步操作
    testComponent.StartAsyncOperation();

    // 等待操作完成
    yield return WaitForCondition(() => operationCompleted, 5f, "操作超时");

    // 验证结果
    Assert.IsTrue(operationCompleted, "操作应该完成");
}
```

### 2. 集成测试模板
```csharp
[UnityTest]
public IEnumerator TestSystemIntegration()
{
    // 设置测试环境
    SimulationManager.IsInTestEnvironment = true;

    try
    {
        // 执行集成测试逻辑
        yield return PerformIntegrationTest();
    }
    finally
    {
        // 清理测试环境
        SimulationManager.IsInTestEnvironment = false;
    }
}
```

## 📊 性能监控和优化

### 1. 性能监控点
- **异步操作执行时间**
- **内存分配和GC压力**
- **MQTT消息发送频率**
- **传感器数据生成速率**

### 2. 优化检查清单
- [ ] 异步操作是否正确使用CancellationToken
- [ ] 是否存在内存泄漏（未取消的订阅）
- [ ] 更新频率是否合理
- [ ] 是否正确释放资源

## 🚨 故障排除指南

### 1. 常见编译错误
**命名空间冲突**：
```
错误：The type 'IoTSystem' exists in both 'Simulation.IoTSystem' and 'Simulation.IoT'
解决：统一使用 Simulation.IoTSystem 命名空间
```

**重复定义**：
```
错误：The type 'TestSensorData' already exists
解决：使用 TestDataTypes.cs 中的统一定义
```

### 2. 运行时问题
**MQTT连接失败**：
- 检查网络连接
- 验证broker地址和端口
- 确认认证信息

**传感器数据不生成**：
- 检查传感器是否已启动
- 验证采样率设置
- 确认事件订阅

### 3. 测试问题
**测试超时**：
- 增加超时时间
- 检查异步操作是否正确完成
- 验证测试条件设置

**测试环境污染**：
- 确保正确设置 IsInTestEnvironment
- 检查资源清理逻辑
- 验证测试隔离

## 📈 项目扩展指南

### 1. 添加新传感器
1. 继承 SensorBase
2. 实现 CollectData 方法
3. 定义对应的数据结构
4. 添加单元测试
5. 更新文档

### 2. 添加新模拟器
1. 继承 SimulatorBase
2. 实现 SimulationLoop 方法
3. 定义业务逻辑
4. 添加集成测试
5. 更新配置文档

### 3. 扩展IoT功能
1. 在 IoTSystem 中添加新方法
2. 保持向后兼容性
3. 添加相应测试
4. 更新API文档

## 🔐 安全和质量保证

### 1. 代码审查检查点
- [ ] 是否遵循命名规范
- [ ] 异步操作是否正确实现
- [ ] 是否有适当的错误处理
- [ ] 测试覆盖率是否达标
- [ ] 文档是否更新

### 2. 发布前检查清单
- [ ] 所有测试通过
- [ ] 编译无警告
- [ ] 性能指标达标
- [ ] 文档完整更新
- [ ] 版本号正确更新

---

**项目座右铭**：*"简单的代码是最好的代码，能工作的代码胜过完美的设计"*
