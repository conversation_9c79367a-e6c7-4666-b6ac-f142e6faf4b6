using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.TestTools;
using NUnit.Framework;
using Cysharp.Threading.Tasks;
using System.Threading;
using Simulation.IoT;

namespace Simulation.Tests
{
    /// <summary>
    /// MQTT集成测试
    /// 验证MQTT管理器与IoT系统的完整集成
    /// </summary>
    public class MQTTIntegrationTests : TestBase
    {
        private IoTSystem iotSystem;
        private MQTTManager mqttManager;
        private TestSensor testSensor;
        
        [SetUp]
        public override void SetUp()
        {
            base.SetUp();
            
            // 获取IoT系统实例
            iotSystem = IoTSystem.Instance;
            Assert.IsNotNull(iotSystem, "IoT系统实例不应为空");
            
            // 获取MQTT管理器
            mqttManager = iotSystem.GetComponent<MQTTManager>();
            if (mqttManager == null)
            {
                mqttManager = iotSystem.gameObject.AddComponent<MQTTManager>();
            }
            Assert.IsNotNull(mqttManager, "MQTT管理器不应为空");
            
            // 创建测试传感器
            testSensor = CreateTestComponent<TestSensor>();
        }
        
        /// <summary>
        /// 测试MQTT连接建立
        /// </summary>
        [UnityTest]
        public IEnumerator TestMQTTConnection()
        {
            // 配置MQTT连接参数
            mqttManager.brokerAddress = "test.mosquitto.org";
            mqttManager.brokerPort = 1883;
            mqttManager.clientId = "unity_test_client";
            mqttManager.useSSL = false;
            
            // 启动MQTT连接
            bool connectionResult = false;
            mqttManager.OnConnected += () => connectionResult = true;
            
            mqttManager.StartConnection();
            
            // 等待连接建立（最多10秒）
            yield return WaitForCondition(() => connectionResult, 10f, "MQTT连接超时");
            
            Assert.IsTrue(connectionResult, "MQTT应该成功连接");
            Assert.IsTrue(mqttManager.IsConnected, "MQTT管理器应该显示已连接状态");
            
            // 断开连接
            mqttManager.Disconnect();
        }
        
        /// <summary>
        /// 测试传感器数据通过MQTT发布
        /// </summary>
        [UnityTest]
        public IEnumerator TestSensorDataMQTTPublishing()
        {
            // 配置MQTT连接
            yield return SetupMQTTConnection();
            
            // 注册测试传感器
            string sensorId = "test_sensor_mqtt_001";
            testSensor.SetSensorId(sensorId);
            bool registrationResult = iotSystem.RegisterSensor(testSensor);
            Assert.IsTrue(registrationResult, "传感器注册应该成功");
            
            // 监听MQTT消息发布
            bool messagePublished = false;
            string publishedTopic = null;
            string publishedMessage = null;
            
            mqttManager.OnMessagePublished += (topic, message) =>
            {
                messagePublished = true;
                publishedTopic = topic;
                publishedMessage = message;
            };
            
            // 启动传感器数据收集
            testSensor.StartSensor();
            
            // 等待数据发布
            yield return WaitForCondition(() => messagePublished, 5f, "MQTT消息发布超时");
            
            Assert.IsTrue(messagePublished, "应该发布MQTT消息");
            Assert.IsNotNull(publishedTopic, "发布主题不应为空");
            Assert.IsNotNull(publishedMessage, "发布消息不应为空");
            Assert.IsTrue(publishedTopic.Contains(sensorId), "主题应该包含传感器ID");
            
            testSensor.StopSensor();
            mqttManager.Disconnect();
        }
        
        /// <summary>
        /// 测试IoT事件通过MQTT发布
        /// </summary>
        [UnityTest]
        public IEnumerator TestIoTEventMQTTPublishing()
        {
            // 配置MQTT连接
            yield return SetupMQTTConnection();
            
            // 监听MQTT消息发布
            bool eventPublished = false;
            string eventTopic = null;
            string eventMessage = null;
            
            mqttManager.OnMessagePublished += (topic, message) =>
            {
                if (topic.Contains("events"))
                {
                    eventPublished = true;
                    eventTopic = topic;
                    eventMessage = message;
                }
            };
            
            // 发送IoT事件
            string sourceId = "test_source_001";
            string eventType = "safety_violation";
            string title = "测试安全违规事件";
            string description = "这是一个测试安全违规事件";
            var eventData = new { severity = "high", location = "工地A区" };
            
            iotSystem.SendIOTEvent(sourceId, eventType, title, description, eventData);
            
            // 等待事件发布
            yield return WaitForCondition(() => eventPublished, 5f, "IoT事件MQTT发布超时");
            
            Assert.IsTrue(eventPublished, "IoT事件应该通过MQTT发布");
            Assert.IsNotNull(eventTopic, "事件主题不应为空");
            Assert.IsNotNull(eventMessage, "事件消息不应为空");
            Assert.IsTrue(eventTopic.Contains("events"), "主题应该包含events");
            
            mqttManager.Disconnect();
        }
        
        /// <summary>
        /// 测试MQTT重连机制
        /// </summary>
        [UnityTest]
        public IEnumerator TestMQTTReconnection()
        {
            // 配置MQTT连接
            yield return SetupMQTTConnection();
            
            // 模拟连接断开
            bool disconnected = false;
            bool reconnected = false;
            
            mqttManager.OnDisconnected += () => disconnected = true;
            mqttManager.OnConnected += () => 
            {
                if (disconnected) reconnected = true;
            };
            
            // 强制断开连接
            mqttManager.ForceDisconnect();
            
            // 等待断开
            yield return WaitForCondition(() => disconnected, 3f, "MQTT断开超时");
            Assert.IsTrue(disconnected, "MQTT应该断开连接");
            
            // 等待重连（如果启用了自动重连）
            if (mqttManager.autoReconnect)
            {
                yield return WaitForCondition(() => reconnected, 10f, "MQTT重连超时");
                Assert.IsTrue(reconnected, "MQTT应该自动重连");
            }
            
            mqttManager.Disconnect();
        }
        
        /// <summary>
        /// 测试MQTT消息队列功能
        /// </summary>
        [UnityTest]
        public IEnumerator TestMQTTMessageQueue()
        {
            // 在未连接状态下发送消息
            Assert.IsFalse(mqttManager.IsConnected, "MQTT应该未连接");
            
            // 发送多条消息到队列
            for (int i = 0; i < 5; i++)
            {
                var testData = new TestSensorData($"queue_test_{i}", "device_001", "test");
                iotSystem.CollectSensorData($"queue_test_{i}", testData);
            }
            
            // 建立连接
            yield return SetupMQTTConnection();
            
            // 监听消息发布
            int publishedCount = 0;
            mqttManager.OnMessagePublished += (topic, message) => publishedCount++;
            
            // 等待队列消息发布
            yield return WaitForCondition(() => publishedCount >= 5, 10f, "队列消息发布超时");
            
            Assert.GreaterOrEqual(publishedCount, 5, "应该发布队列中的所有消息");
            
            mqttManager.Disconnect();
        }
        
        /// <summary>
        /// 设置MQTT连接的辅助方法
        /// </summary>
        private IEnumerator SetupMQTTConnection()
        {
            // 配置MQTT连接参数
            mqttManager.brokerAddress = "test.mosquitto.org";
            mqttManager.brokerPort = 1883;
            mqttManager.clientId = $"unity_test_{System.Guid.NewGuid().ToString("N")[..8]}";
            mqttManager.useSSL = false;
            mqttManager.autoReconnect = true;
            
            // 启动连接
            bool connected = false;
            mqttManager.OnConnected += () => connected = true;
            mqttManager.StartConnection();
            
            // 等待连接建立
            yield return WaitForCondition(() => connected, 10f, "MQTT连接建立超时");
            Assert.IsTrue(connected, "MQTT连接应该建立成功");
        }
    }
    
    /// <summary>
    /// 测试用传感器数据类
    /// </summary>
    [System.Serializable]
    public class TestSensorData : SensorDataBase
    {
        public string testValue;
        public int dataIndex;
        
        public TestSensorData(string sensorId, string deviceId, string sensorType) : base(sensorType)
        {
            this.testValue = $"test_data_{sensorId}";
            this.dataIndex = UnityEngine.Random.Range(1, 1000);
        }
    }
}
