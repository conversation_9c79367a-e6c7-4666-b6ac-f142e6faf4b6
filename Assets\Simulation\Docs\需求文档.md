# 建筑工地数字孪生模拟沙盘项目需求文档

## 1. 项目概述

### 1.1 项目背景
本项目旨在使用Unity引擎开发一个建筑工地的模拟沙盘系统，作为工地数字孪生项目在未接入实体设备前的模拟场景，主要用于生成测试数据和验证系统功能。

### 1.2 项目目标
- 构建真实的建筑工地三维模拟环境
- 模拟工地各类实体的行为和状态
- 实现模拟系统与传感器数据采集系统的分离
- 通过MQTT协议提供标准化的数据传输接口
- 生成用于测试的模拟数据
- 为后续数字孪生系统提供数据基础

### 1.3 应用场景
- 工地管理系统测试
- 安全监控算法验证
- 环境监测数据模拟
- 设备运行状态仿真
- 传感器数据采集系统测试
- 数字孪生系统数据接口验证

### 1.4 系统架构设计

#### 1.4.1 职责分离
- **模拟系统**：专注于工地运行模拟，生成模拟事件和状态变化
- **传感器组件系统**：独立的传感器组件，用户可自由添加和配置
- **物联网系统**：统一管理所有传感器，负责数据收集、处理和传输
- **数据传输系统**：基于MQTT协议的标准化数据传输

#### 1.4.2 数据流架构
```
模拟系统 → 运行模拟对象
     ↓
传感器组件 → Update函数获取数据 → 生成传感器数据 → 物联网系统
     ↓
物联网系统 → 数据收集与处理 → 统一MQTT发布
     ↓
数字孪生系统 → MQTT订阅 → 数据处理与分析
```

## 2. 传感器组件系统

### 2.1 传感器类型定义

基于数据采集需求，系统定义5种核心传感器类型：

#### 2.1.1 位置追踪传感器 (Position Tracker)
**功能**：统一处理位置相关数据
- 人员位置追踪
- 车辆位置追踪
- 设备位置状态

**实现方式**：在Update函数中通过transform.position和transform.rotation获取目标对象的位置和旋转信息

**配置选项**：通过Unity Inspector面板可独立配置以下数据采集开关
- **位置数据**：
  - enablePositionX：是否采集X轴位置数据
  - enablePositionY：是否采集Y轴位置数据
  - enablePositionZ：是否采集Z轴位置数据
- **旋转数据**：
  - enableRotationX：是否采集X轴旋转数据
  - enableRotationY：是否采集Y轴旋转数据
  - enableRotationZ：是否采集Z轴旋转数据

**数据格式**：
```json
{
  "sensorId": "pos_001",
  "timestamp": "2024-01-01T10:00:00Z",
  "targetType": "person|vehicle|equipment",
  "targetId": "target_001",
  "position": {
    "x": 100.5,  // 仅在enablePositionX为true时包含
    "y": 50.2,   // 仅在enablePositionY为true时包含
    "z": 10.0    // 仅在enablePositionZ为true时包含
  },
  "rotation": {
    "x": 15.0,   // 仅在enableRotationX为true时包含
    "y": 45.0,   // 仅在enableRotationY为true时包含
    "z": 0.0     // 仅在enableRotationZ为true时包含
  },
  "zone": "construction_area_A"
}
```

**MQTT主题**：`sensors/position/{targetType}/{targetId}`

#### 2.1.2 身份识别传感器 (Identity Sensor)
**功能**：统一处理身份和属性信息
- 人员身份识别
- 车辆信息识别

**实现方式**：通过组件中设置的序列化字段直接读取身份信息

**数据格式**：
```json
{
  "sensorId": "id_001",
  "timestamp": "2024-01-01T10:00:00Z",
  "targetType": "person|vehicle",
  "targetId": "target_001",
  "identity": {
    "name": "张三",
    "role": "工人",
    "department": "施工队A",
    "certification": ["安全证书"]
  },
  "attributes": {
    "vehicleType": "挖掘机",
    "licensePlate": "京A12345",
    "capacity": "20吨"
  }
}
```

**MQTT主题**：`sensors/identity/{targetType}/{targetId}`

#### 2.1.3 运动状态传感器 (Motion Sensor)
**功能**：统一处理运动和工作状态数据
- 车辆速度监测
- 塔吊机工作状态
- 升降机工作状态

**实现方式**：在Update函数中获取transform.position并计算速度，工作状态通过组件属性直接读取

**数据格式**：
```json
{
  "sensorId": "motion_001",
  "timestamp": "2024-01-01T10:00:00Z",
  "targetType": "vehicle|crane|elevator",
  "targetId": "target_001",
  "motion": {
    "speed": 15.5,
    "direction": 45.0,
    "acceleration": 2.1
  },
  "workStatus": {
    "status": "working|idle|maintenance",
    "load": 80.5,
    "operation": "lifting|moving|loading"
  }
}
```

**MQTT主题**：`sensors/motion/{targetType}/{targetId}`

#### 2.1.4 噪声传感器 (Noise Sensor)
**功能**：专门处理噪声数据
- 点噪声数据采集
- 噪声等级监测

**实现方式**：通过调用模拟器数据获取函数（如GetNoiseValue()）获取噪声数据

**数据格式**：
```json
{
  "sensorId": "noise_001",
  "timestamp": "2024-01-01T10:00:00Z",
  "position": {
    "x": 100.5,
    "y": 50.2,
    "z": 10.0
  },
  "noise": {
    "level": 75.5,
    "unit": "dB"
  }
}
```

**MQTT主题**：`sensors/noise/{sensorId}`

#### 2.1.5 扬尘传感器 (Dust Sensor)
**功能**：专门处理扬尘数据
- 点扬尘数据采集
- PM2.5和PM10监测

**实现方式**：通过调用模拟器数据获取函数（如GetDustValue()）获取扬尘数据

**数据格式**：
```json
{
  "sensorId": "dust_001",
  "timestamp": "2024-01-01T10:00:00Z",
  "position": {
    "x": 100.5,
    "y": 50.2,
    "z": 10.0
  },
  "dust": {
    "pm25": 45.2,
    "pm10": 78.1,
    "unit": "μg/m³"
  }
}
```

**MQTT主题**：`sensors/dust/{sensorId}`

### 2.2 传感器组件特性
- **独立部署**：传感器组件可独立添加和配置
- **直接数据获取**：在Update函数中直接获取目标对象的数据
- **统一管理**：所有传感器由物联网系统统一管理和协调
- **标准接口**：统一的数据上报接口，由物联网系统负责MQTT发布
- **灵活配置**：支持传感器参数和采集频率配置

## 3. 物联网系统

### 3.1 物联网系统架构
- **传感器管理**：统一管理所有传感器组件的注册、配置和状态监控
- **数据收集**：从各个传感器组件收集数据
- **数据处理**：对传感器数据进行预处理、验证和格式化
- **事件管理**：统一管理和发布来自各个模拟器的事件通知
- **数据传输**：通过MQTT协议统一发布数据和事件

### 3.2 MQTT数据传输系统

#### 3.2.1 MQTT架构设计
- **Broker**：中央消息代理服务器
- **Publisher**：物联网系统作为统一的数据发布者
- **Subscriber**：数字孪生系统作为数据订阅者

#### 3.2.2 主题命名规范
- **位置数据**：`sensors/position/{targetType}/{targetId}`
- **身份数据**：`sensors/identity/{targetType}/{targetId}`
- **运动数据**：`sensors/motion/{targetType}/{targetId}`
- **噪声数据**：`sensors/noise/{sensorId}`
- **扬尘数据**：`sensors/dust/{sensorId}`
- **系统状态**：`system/status/{componentId}`
- **事件通知**：`events/{eventType}/{sourceId}`

#### 3.2.3 QoS等级设置
- **实时数据**：QoS 0 (最多一次传递)
- **重要事件**：QoS 1 (至少一次传递)
- **关键状态**：QoS 2 (恰好一次传递)

#### 3.2.4 数据传输规范
- **数据格式**：JSON格式
- **时间戳**：ISO 8601格式
- **编码方式**：UTF-8
- **压缩方式**：可选GZIP压缩

## 4. 模拟系统功能需求

### 4.1 人物模拟系统

#### 4.1.1 工人行为模拟
- **日常行为**：模拟工人上下班的走动路径和时间规律
- **工作行为**：模拟工人在工作时间内的移动和作业行为
- **工作状态**：
  - 工作状态：正在施工、操作设备
  - 休息状态：休息区域停留、用餐
  - 空闲状态：等待任务、移动中

#### 4.1.2 施工过程模拟
- 不同工种在指定位置的施工作业
- 多人协作的施工场景
- 施工进度的动态展示

#### 4.1.3 安全问题模拟
- **安全装备违规**：
  - 未佩戴安全帽
  - 未穿着防护服
  - 未佩戴安全带
- **行为违规**：
  - 工作时使用手机
  - 在危险区域停留
  - 违规操作设备

### 4.2 车辆模拟系统

#### 4.2.1 车辆基础信息
- 车辆类型（工程车、运输车、混凝土车等）
- 车牌号码
- 载重信息
- 驾驶员信息

#### 4.2.2 车辆行驶模拟
- 进出工地的路径规划
- 工地内部行驶路线
- 装卸货物过程
- 停车和等待状态

### 4.3 塔吊机模拟系统

#### 4.3.1 设备基础信息
- 塔吊编号和型号
- 工作半径和高度
- 载重能力
- 操作员信息

#### 4.3.2 运行状态模拟
- 工作状态（运行、停机、维护）
- 负载状态（空载、满载、部分负载）
- 故障状态模拟

#### 4.3.3 工作过程模拟
- 起重作业动画
- 旋转和移动轨迹
- 材料吊装过程

### 4.4 升降机模拟系统

#### 4.4.1 设备基础信息
- 升降机编号和类型
- 载重能力
- 运行高度范围
- 安全检查状态

#### 4.4.2 运行状态模拟
- 上升/下降状态
- 停靠楼层
- 载人/载物状态
- 维护和检修状态

#### 4.4.3 工作过程模拟
- 人员和物料运输
- 楼层间移动动画
- 安全门开关控制

### 4.5 监控摄像头模拟系统

#### 4.5.1 设备基础信息
- 摄像头编号和位置
- 监控类型（固定、云台）
- 分辨率和视角参数

#### 4.5.2 监控范围模拟
- 可视化监控覆盖区域
- 视角调节范围

#### 4.5.3 实时画面模拟
- 模拟监控画面输出

### 4.6 环境模拟系统

#### 4.6.1 噪声源模拟
- **位置信息**：噪声源的三维坐标
- **噪声强度**：分贝值的动态变化
- **噪声类型**：
  - 环境噪声（交通、风声）
  - 施工噪声（机械设备、敲击声）
  - 设备噪声（发电机、空压机）

#### 4.6.2 扬尘模拟
- **扬尘源位置**：施工点、道路、堆料场
- **扬尘参数**：
  - 颗粒物浓度（PM2.5、PM10）
  - 颗粒大小分布
  - 扩散速度
- **影响因素**：
  - 风向和风速影响
  - 建筑物遮挡效应
  - 湿度对扬尘的抑制

### 4.7 门禁出入管理模拟系统

#### 4.7.1 门禁设备基础信息
- 门禁点编号和位置
- 门禁类型（人员通道、车辆通道、货物通道）

#### 4.7.2 人员出入管理
- **身份识别**：
  - 员工ID识别
- **出入记录**：
  - 进出时间记录
  - 人员身份信息
- **数据统计**：
  - 每日出入人数统计
  - 人员通行情况分析

#### 4.7.3 车辆出入管理
- **车辆识别**：
  - 车牌号码识别
  - 车辆类型验证
- **通行控制**：
  - 道闸自动控制
- **出入记录**：
  - 进出时间记录
  - 车辆信息
  - 载货信息
- **数据统计**：
  - 每日车辆进出量统计
  - 车辆通行情况分析

### 4.8 物料管理模拟系统

#### 4.8.1 物料存储点管理
- **存储区域信息**：
  - 物料堆放点编号和位置坐标
  - 存储区域类型（室内仓库、露天堆场、临时存放点）
  - 存储容量和当前库存量
- **区域状态监控**：
  - 存储区域占用率

#### 4.8.2 物料分类管理
- **建筑材料**：
  - 钢筋：规格型号、长度、重量
  - 水泥：品牌、标号、包装规格
  - 砂石：粒径规格、含水率、产地
  - 混凝土：强度等级、配合比、坍落度
- **施工辅料**：
  - 模板：材质类型、尺寸规格、使用次数
  - 脚手架：材质、规格、安全等级
  - 防护用品：类型、规格、有效期
- **机械配件**：
  - 设备零部件：型号、适用设备、库存数量
  - 润滑油料：品牌、规格、存储要求
  - 工具器械：类型、规格、使用状态

#### 4.8.3 物料出入库管理
- **入库管理**：
  - 物料到货登记：供应商信息、到货时间、数量规格
  - 入库确认：存放位置分配、库存更新、入库单生成
- **出库管理**：
  - 领料申请：申请部门、用途说明、需求数量
  - 出库审批：审批流程、权限验证、数量核实
  - 出库执行：实际出库数量、领取人员、用途跟踪

## 5. 技术实现规范

### 5.1 事件订阅功能规范

#### 5.1.1 事件类型定义
- **安全事件**：工人安全违规、设备安全警报等
- **状态事件**：设备状态变化、系统状态更新等
- **操作事件**：用户操作、系统操作等
- **异常事件**：故障报警、异常状态等

#### 5.1.2 事件数据格式
```json
{
  "eventId": "unique-event-id",
  "eventType": "safety|status|operation|exception",
  "sourceId": "source-component-id",
  "sourceType": "worker|vehicle|equipment|environment",
  "timestamp": "2024-01-01T12:00:00Z",
  "severity": "low|medium|high|critical",
  "title": "事件标题",
  "description": "事件详细描述",
  "data": {
    // 事件相关的具体数据
  }
}
```

### 5.2 传感器组件接口

#### 5.2.1 基础接口定义
```csharp
public interface ISensorComponent
{
    string SensorId { get; }
    SensorType Type { get; }
    bool IsActive { get; }
    float PublishInterval { get; set; }
    
    void Initialize(SensorConfig config);
    void StartMonitoring();
    void StopMonitoring();
    void UpdateSensorData(); // 在Update中调用，获取传感器数据
    void ReportData(object data); // 向物联网系统上报数据
}
```

#### 5.2.2 传感器组件实现示例
```csharp
public abstract class SensorComponentBase : MonoBehaviour, ISensorComponent
{
    [SerializeField] protected string sensorId;
    [SerializeField] protected float publishInterval = 1.0f;
    [SerializeField] protected SensorType sensorType;
    [SerializeField] protected bool isActive = true;
    
    private float lastPublishTime;
    private IoTSystem iotSystem;
    
    protected virtual void Start()
    {
        iotSystem = IoTSystem.Instance;
        iotSystem?.RegisterSensor(this);
    }
    
    protected virtual void Update()
    {
        if (isActive && Time.time - lastPublishTime >= publishInterval)
        {
            UpdateSensorData();
            lastPublishTime = Time.time;
        }
    }
    
    public abstract void UpdateSensorData();
    
    public virtual void ReportData(object data)
    {
        iotSystem?.CollectSensorData(sensorId, data);
    }
}

// 位置追踪传感器实现示例
public class PositionTracker : SensorComponentBase
{
    [Header("位置数据配置")]
    [SerializeField] private bool enablePositionX = true;
    [SerializeField] private bool enablePositionY = true;
    [SerializeField] private bool enablePositionZ = true;
    
    [Header("旋转数据配置")]
    [SerializeField] private bool enableRotationX = false;
    [SerializeField] private bool enableRotationY = true;
    [SerializeField] private bool enableRotationZ = false;
    
    [SerializeField] private string targetType = "person";
    [SerializeField] private string targetId;
    [SerializeField] private string zone = "construction_area_A";
    
    public override void UpdateSensorData()
    {
        var positionData = new Dictionary<string, object>();
        var rotationData = new Dictionary<string, object>();
        
        // 根据配置采集位置数据
        if (enablePositionX) positionData["x"] = transform.position.x;
        if (enablePositionY) positionData["y"] = transform.position.y;
        if (enablePositionZ) positionData["z"] = transform.position.z;
        
        // 根据配置采集旋转数据
        if (enableRotationX) rotationData["x"] = transform.rotation.eulerAngles.x;
        if (enableRotationY) rotationData["y"] = transform.rotation.eulerAngles.y;
        if (enableRotationZ) rotationData["z"] = transform.rotation.eulerAngles.z;
        
        var sensorData = new
        {
            sensorId = this.sensorId,
            timestamp = DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ssZ"),
            targetType = this.targetType,
            targetId = this.targetId,
            position = positionData.Count > 0 ? positionData : null,
            rotation = rotationData.Count > 0 ? rotationData : null,
            zone = this.zone
        };
        
        ReportData(sensorData);
    }
}
```

### 5.3 物联网系统接口

#### 5.3.1 物联网系统核心接口
```csharp
public class IoTSystem : MonoBehaviour
{
    public static IoTSystem Instance { get; private set; }
    
    private void Awake()
    {
        if (Instance == null)
        {
            Instance = this;
            DontDestroyOnLoad(gameObject);
        }
        else
        {
            Destroy(gameObject);
        }
    }
    
    public void RegisterSensor(ISensorComponent sensor) { /* 实现 */ }
    public void UnregisterSensor(string sensorId) { /* 实现 */ }
    public void CollectSensorData(string sensorId, object data) { /* 实现 */ }
    public void SendIOTEvent(string sourceId, string eventType, string title, string description, object data) { /* 实现 */ }
    public void StartDataTransmission() { /* 实现 */ }
    public void StopDataTransmission() { /* 实现 */ }
    public bool IsConnected { get; private set; }
}
```

### 5.4 MQTT集成规范

#### 5.4.1 底层实现
- **MQTT客户端**：使用BestMQTT插件作为底层MQTT通信实现
- **插件优势**：
  - 高性能的MQTT 3.1.1和5.0协议支持
  - 完整的Unity集成和跨平台兼容性
  - 内置连接管理和自动重连机制
  - 支持TLS/SSL加密连接
  - 优化的内存管理和性能表现

#### 5.4.2 连接配置
- **Broker地址**：可配置的MQTT服务器地址
- **端口**：默认1883（非加密）或8883（TLS加密）
- **认证**：支持用户名/密码认证
- **Keep Alive**：60秒心跳间隔
- **协议版本**：支持MQTT 3.1.1和5.0

#### 5.4.3 发布规范
- **重连机制**：基于BestMQTT的自动重连，最大重试次数5次
- **缓存机制**：离线时缓存数据，重连后批量发送
- **错误处理**：发送失败时记录日志并重试
- **消息持久化**：支持消息持久化存储

#### 5.4.4 数据序列化
- **格式**：JSON格式
- **时间戳**：UTC时间，ISO 8601格式
- **数据压缩**：可选择启用GZIP压缩
- **数据验证**：发送前进行JSON格式验证
- **编码**：UTF-8编码，支持BestMQTT的高效序列化

### 5.5 配置管理

#### 5.5.1 传感器配置
传感器配置通过Unity Inspector面板中的序列化字段进行配置：

**基础配置**：
- **sensorId**: 传感器唯一标识符
- **publishInterval**: 数据发布间隔（秒）
- **sensorType**: 传感器类型枚举
- **isActive**: 传感器是否激活
- **targetTypes**: 目标类型（通过枚举或字符串数组配置）

**通用数据发送开关配置**：
所有传感器类型都支持以下数据发送开关，通过Unity Inspector面板的勾选框进行配置：

- **enableDataTransmission**: 是否启用数据传输（bool类型，总开关）
- **enableTimestampData**: 是否发送时间戳数据（bool类型）
- **enableTargetInfo**: 是否发送目标信息（targetType、targetId）（bool类型）
- **enableSensorMetadata**: 是否发送传感器元数据（sensorId等）（bool类型）

**位置追踪传感器专用配置**：
- **enablePositionData**: 是否采集位置数据（bool类型，位置数据总开关）
  - **enablePositionX**: 是否采集X轴位置数据（bool类型）
  - **enablePositionY**: 是否采集Y轴位置数据（bool类型）
  - **enablePositionZ**: 是否采集Z轴位置数据（bool类型）
- **enableRotationData**: 是否采集旋转数据（bool类型，旋转数据总开关）
  - **enableRotationX**: 是否采集X轴旋转数据（bool类型）
  - **enableRotationY**: 是否采集Y轴旋转数据（bool类型）
  - **enableRotationZ**: 是否采集Z轴旋转数据（bool类型）
- **enableZoneData**: 是否发送区域信息（bool类型）

**身份识别传感器专用配置**：
- **enableIdentityData**: 是否发送身份信息（bool类型，身份数据总开关）
  - **enableNameData**: 是否发送姓名信息（bool类型）
  - **enableRoleData**: 是否发送角色信息（bool类型）
  - **enableDepartmentData**: 是否发送部门信息（bool类型）
  - **enableCertificationData**: 是否发送认证信息（bool类型）
- **enableAttributesData**: 是否发送属性信息（bool类型，属性数据总开关）
  - **enableVehicleTypeData**: 是否发送车辆类型（bool类型）
  - **enableLicensePlateData**: 是否发送车牌号（bool类型）
  - **enableCapacityData**: 是否发送载重信息（bool类型）

**运动状态传感器专用配置**：
- **enableMotionData**: 是否发送运动数据（bool类型，运动数据总开关）
  - **enableSpeedData**: 是否发送速度数据（bool类型）
  - **enableDirectionData**: 是否发送方向数据（bool类型）
  - **enableAccelerationData**: 是否发送加速度数据（bool类型）
- **enableWorkStatusData**: 是否发送工作状态数据（bool类型，工作状态总开关）
  - **enableStatusData**: 是否发送状态信息（bool类型）
  - **enableLoadData**: 是否发送负载信息（bool类型）
  - **enableOperationData**: 是否发送操作信息（bool类型）

**噪声传感器专用配置**：
- **enablePositionData**: 是否发送传感器位置信息（bool类型）
- **enableNoiseData**: 是否发送噪声数据（bool类型，噪声数据总开关）
  - **enableNoiseLevelData**: 是否发送噪声等级（bool类型）
  - **enableNoiseUnitData**: 是否发送噪声单位（bool类型）

**扬尘传感器专用配置**：
- **enablePositionData**: 是否发送传感器位置信息（bool类型）
- **enableDustData**: 是否发送扬尘数据（bool类型，扬尘数据总开关）
  - **enablePM25Data**: 是否发送PM2.5数据（bool类型）
  - **enablePM10Data**: 是否发送PM10数据（bool类型）
  - **enableDustUnitData**: 是否发送扬尘单位（bool类型）


### 5.6 部署和集成

#### 5.5.1 组件部署
1. 传感器组件作为独立的Unity组件
2. 组件状态可视化界面

#### 5.5.2 系统集成
1. 模拟系统运行各种模拟对象（人员、车辆、设备等）
2. 传感器组件在Update函数中直接获取目标对象数据并生成传感器数据
3. 传感器组件向物联网系统上报数据
4. 物联网系统统一收集、处理和通过MQTT发布数据
5. 数字孪生系统订阅MQTT主题接收数据

#### 5.5.3 系统集成流程
1. **物联网系统初始化**：启动物联网系统服务
2. **传感器注册**：各传感器组件向物联网系统注册
3. **MQTT连接建立**：物联网系统连接到MQTT服务器
4. **数据传输启动**：开始传感器数据的收集和发布
5. **事件订阅配置**：配置事件主题和订阅规则
6. **状态监控**：持续监控系统运行状态

#### 5.5.4 具体实现方式
- **传感器组件**：通过IoTSystem.Instance获取物联网系统单例实例
- **数据上报**：传感器组件调用 `CollectSensorData` 方法上报数据
- **事件发布**：模拟器组件通过IoTSystem.Instance.SendIOTEvent方法发布事件(注意:事件不要用异步方法)
- **物联网系统**：统一收集、处理和发布数据及事件到MQTT服务器
- **数字孪生系统**：订阅MQTT主题接收传感器数据和事件通知

#### 5.5.5 事件发布示例
```csharp
// 工人模拟器发布安全事件示例
public class WorkerSimulator : MonoBehaviour
{
    private void OnSafetyViolation()
    {
        var eventData = new
        {
            workerId = "worker-001",
            violationType = "未佩戴安全帽",
            location = transform.position,
            riskLevel = "high"
        };
        
        IoTSystem.Instance.SendIOTEvent(
            sourceId: "worker-001",
            eventType: "safety",
            title: "工人安全违规",
            description: "检测到工人未佩戴安全帽",
            data: eventData
        );
    }
}
```
