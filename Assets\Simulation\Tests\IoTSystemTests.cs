using System.Collections;
using System.Collections.Generic;
using System.Threading;
using UnityEngine;
using UnityEngine.TestTools;
using NUnit.Framework;
using Cysharp.Threading.Tasks;
using Simulation.IoT;
using Simulation.Data;
using Simulation.Sensors;

namespace Simulation.Tests
{
    /// <summary>
    /// IoT系统单元测试
    /// </summary>
    public class IoTSystemTests : TestBase
    {
        private IoTSystem iotSystem;
        private MockSensor mockSensor;
        
        [SetUp]
        public override void SetUp()
        {
            base.SetUp();
            
            // 获取IoT系统实例
            iotSystem = IoTSystem.Instance;
            Assert.IsNotNull(iotSystem, "IoT系统实例不应为空");
            
            // 创建模拟传感器
            mockSensor = testGameObject.AddComponent<MockSensor>();
        }
        
        /// <summary>
        /// 测试IoT系统单例模式
        /// </summary>
        [Test]
        public void TestSingletonPattern()
        {
            // 验证单例实例
            var instance1 = IoTSystem.Instance;
            var instance2 = IoTSystem.Instance;
            
            Assert.IsNotNull(instance1, "第一次获取实例不应为空");
            Assert.IsNotNull(instance2, "第二次获取实例不应为空");
            Assert.AreSame(instance1, instance2, "两次获取的实例应该是同一个对象");
        }
        
        /// <summary>
        /// 测试传感器注册功能
        /// </summary>
        [Test]
        public void TestSensorRegistration()
        {
            // 注册传感器
            string sensorId = CreateMockSensorId("register_test");
            mockSensor.SetSensorId(sensorId);
            
            bool registrationResult = iotSystem.RegisterSensor(mockSensor);
            
            Assert.IsTrue(registrationResult, "传感器注册应该成功");
            Assert.IsTrue(iotSystem.IsSensorRegistered(sensorId), "传感器应该已注册");
        }
        
        /// <summary>
        /// 测试重复注册传感器
        /// </summary>
        [Test]
        public void TestDuplicateSensorRegistration()
        {
            string sensorId = CreateMockSensorId("duplicate_test");
            mockSensor.SetSensorId(sensorId);
            
            // 第一次注册
            bool firstRegistration = iotSystem.RegisterSensor(mockSensor);
            Assert.IsTrue(firstRegistration, "第一次注册应该成功");
            
            // 第二次注册相同ID
            bool secondRegistration = iotSystem.RegisterSensor(mockSensor);
            Assert.IsFalse(secondRegistration, "重复注册应该失败");
        }
        
        /// <summary>
        /// 测试传感器注销功能
        /// </summary>
        [Test]
        public void TestSensorUnregistration()
        {
            string sensorId = CreateMockSensorId("unregister_test");
            mockSensor.SetSensorId(sensorId);
            
            // 注册传感器
            iotSystem.RegisterSensor(mockSensor);
            Assert.IsTrue(iotSystem.IsSensorRegistered(sensorId), "传感器应该已注册");
            
            // 注销传感器
            bool unregistrationResult = iotSystem.UnregisterSensor(sensorId);
            Assert.IsTrue(unregistrationResult, "传感器注销应该成功");
            Assert.IsFalse(iotSystem.IsSensorRegistered(sensorId), "传感器应该已注销");
        }
        
        /// <summary>
        /// 测试传感器数据收集
        /// </summary>
        [Test]
        public void TestSensorDataCollection()
        {
            string sensorId = CreateMockSensorId("data_test");
            var testData = new MockSensorData(sensorId, "device_001", "mock");
            
            // 收集传感器数据
            iotSystem.CollectSensorData(sensorId, testData);
            
            // 验证数据格式
            Assert.IsNotNull(testData.timestamp, "时间戳不应为空");
            Assert.AreEqual(sensorId, testData.sensorId, "传感器ID应该匹配");
            Assert.AreEqual("mock", testData.sensorType, "传感器类型应该匹配");
        }
        
        /// <summary>
        /// 测试IoT事件发送
        /// </summary>
        [Test]
        public void TestIoTEventSending()
        {
            string sourceId = CreateMockSensorId("event_test");
            string eventType = "test_event";
            string title = "测试事件";
            string description = "这是一个测试事件";
            var eventData = new { testValue = 123 };
            
            // 发送IoT事件
            iotSystem.SendIOTEvent(sourceId, eventType, title, description, eventData);
            
            // 这里主要测试方法不会抛出异常
            // 实际的事件处理验证需要在集成测试中进行
            Assert.Pass("IoT事件发送完成");
        }
        
        /// <summary>
        /// 测试获取注册传感器列表
        /// </summary>
        [Test]
        public void TestGetRegisteredSensors()
        {
            // 注册多个传感器
            var sensor1Id = CreateMockSensorId("list_test_1");
            var sensor2Id = CreateMockSensorId("list_test_2");
            
            var sensor1 = testGameObject.AddComponent<MockSensor>();
            var sensor2 = testGameObject.AddComponent<MockSensor>();
            
            sensor1.SetSensorId(sensor1Id);
            sensor2.SetSensorId(sensor2Id);
            
            iotSystem.RegisterSensor(sensor1);
            iotSystem.RegisterSensor(sensor2);
            
            // 获取注册传感器列表
            var registeredSensors = iotSystem.GetRegisteredSensors();
            
            Assert.IsNotNull(registeredSensors, "注册传感器列表不应为空");
            Assert.Contains(sensor1Id, registeredSensors, "应该包含传感器1");
            Assert.Contains(sensor2Id, registeredSensors, "应该包含传感器2");
        }
        
        /// <summary>
        /// 测试空传感器ID处理
        /// </summary>
        [Test]
        public void TestNullSensorIdHandling()
        {
            // 测试空传感器ID注册
            mockSensor.SetSensorId(null);
            bool registrationResult = iotSystem.RegisterSensor(mockSensor);
            Assert.IsFalse(registrationResult, "空传感器ID注册应该失败");
            
            // 测试空传感器ID注销
            bool unregistrationResult = iotSystem.UnregisterSensor(null);
            Assert.IsFalse(unregistrationResult, "空传感器ID注销应该失败");
        }
        
        /// <summary>
        /// 测试传感器数据JSON序列化
        /// </summary>
        [Test]
        public void TestSensorDataSerialization()
        {
            var testData = new MockSensorData(CreateMockSensorId("json_test"), "device_001", "mock");
            testData.testValue = 42;
            testData.testString = "测试字符串";
            
            // 测试JSON序列化
            AssertJsonSerialization(testData);
        }
    }
    
    /// <summary>
    /// 模拟传感器类，用于测试
    /// </summary>
    public class MockSensor : SensorBase
    {
        public override string SensorType => "mock";

        public void SetSensorId(string id)
        {
            sensorId = id;
        }

        protected override async UniTask<object> GenerateSensorDataAsync(CancellationToken cancellationToken)
        {
            // 模拟数据收集
            await UniTask.Yield(PlayerLoopTiming.Update, cancellationToken);
            var data = new MockSensorData(sensorId, deviceId, SensorType);
            return data;
        }
    }
    
    /// <summary>
    /// 模拟传感器数据类，用于测试
    /// </summary>
    [System.Serializable]
    public class MockSensorData : SensorDataBase
    {
        public int testValue;
        public string testString;
        
        public MockSensorData(string sensorId, string deviceId, string sensorType) 
            : base(sensorId, deviceId, sensorType)
        {
        }
    }
}
