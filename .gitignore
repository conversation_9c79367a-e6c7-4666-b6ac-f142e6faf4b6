# ================================
# Unity项目通用忽略规则
# ================================

# 构建和临时文件
Library/
library/
Temp/
temp/
Obj/
obj/
Build/
build/
Builds/
builds/
ExportedObj/
~UnityDirMonSyncFile~*

# 用户设置和日志
UserSettings/
usersettings/
MemoryCaptures/
memorycaptures/
Logs/
logs/
ServerData/

# 特定配置文件
/ignore.conf
.collabignore
crashlytics-build.properties
.svn
.trae

# 私有文件和元数据
*.private
*.private.meta
^*.private.[0-9]+$
^*.private.[0-9]+.meta$

# 插件和工具目录
**/Assets/AssetStoreTools
**/assets/assetstoretools
/Assets/Plugins/PlasticSCM*
/assets/plugins/PlasticSCM*

# IDE和编辑器相关
.vs/
.gradle/
.consulo/
.vscode/
.DS_Store*
Thumbs.db
Desktop.ini

# 项目文件和元数据
*.csproj
*.unityproj
*.sln
*.suo
*.tmp
*.user
*.userprefs
*.pidb
*.pidb.meta
*.booproj
*.svd
*.pdb
*.pdb.meta
*.mdb
*.mdb.meta
*.opendb
*.VC.db
sysinfo.txt