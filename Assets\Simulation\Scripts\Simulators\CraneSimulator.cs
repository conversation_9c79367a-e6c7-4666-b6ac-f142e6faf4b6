using System;
using System.Threading;
using Cysharp.Threading.Tasks;
using UnityEngine;
using Simulation.IoTSystem;
using Simulation.Data;

namespace Simulation.Simulators
{
    /// <summary>
    /// 塔吊模拟器
    /// 模拟塔吊的作业流程和安全事件
    /// </summary>
    public class CraneSimulator : SimulatorBase
    {
        [Header("塔吊基础信息")]
        [SerializeField] private string craneId = "crane_001";
        [SerializeField] private string craneName = "主塔吊";
        [SerializeField] private string operatorId = "operator_001";
        [SerializeField] private string operatorName = "王师傅";
        [SerializeField] private float maxHeight = 50f; // 最大高度（米）
        [SerializeField] private float maxReach = 30f; // 最大臂长（米）
        [SerializeField] private float maxLoad = 5000f; // 最大载重（kg）
        
        [Header("作业配置")]
        [SerializeField] private float liftingSpeed = 2f; // 起升速度（米/秒）
        [SerializeField] private float rotationSpeed = 30f; // 回转速度（度/秒）
        [SerializeField] private float trolleySpeed = 1f; // 小车速度（米/秒）
        [SerializeField] private int dailyOperations = 20; // 每日作业次数
        [SerializeField] private float operationInterval = 900f; // 作业间隔（秒）
        
        [Header("作业位置")]
        [SerializeField] private Transform[] liftingPoints; // 起吊点
        [SerializeField] private Transform[] placementPoints; // 放置点
        [SerializeField] private Transform restPosition; // 休息位置
        [SerializeField] private Transform maintenancePosition; // 维护位置
        
        [Header("安全事件配置")]
        [SerializeField] private bool enableSafetyEvents = true;
        [SerializeField] private float safetyEventProbability = 0.05f; // 安全事件概率
        [SerializeField] private float safetyCheckInterval = 120f; // 安全检查间隔（秒）
        [SerializeField] private string[] safetyEventTypes = {
            "超载警告",
            "风速过大",
            "碰撞检测",
            "限位触发",
            "钢丝绳异常"
        };
        
        [Header("环境影响")]
        [SerializeField] private float noiseLevel = 75f; // 噪声等级（dB）
        [SerializeField] private float dustGeneration = 0.3f; // 扬尘产生量
        [SerializeField] private float environmentalImpactRadius = 50f; // 环境影响半径
        
        [Header("调试配置")]
        [SerializeField] private bool enableDebugLog = true;
        [SerializeField] private bool showOperationGizmos = true;
        [SerializeField] private Color operationColor = Color.cyan;
        
        // 塔吊状态
        private CraneState currentState = CraneState.Idle;
        private CraneOperation currentOperation;
        private int completedOperations = 0;
        private float currentHeight = 0f;
        private float currentRotation = 0f;
        private float currentReach = 0f;
        private float currentLoad = 0f;
        private float stateStartTime;
        
        // 作业统计
        private float totalLiftingTime = 0f;
        private float totalOperationTime = 0f;
        private int totalSafetyEvents = 0;
        private float maxLoadLifted = 0f;
        
        // 公共属性
        public string CraneId => craneId;
        public CraneState CurrentState => currentState;
        public float CurrentHeight => currentHeight;
        public float CurrentLoad => currentLoad;
        public int CompletedOperations => completedOperations;
        
        protected override async UniTask OnStartSimulationAsync(CancellationToken cancellationToken)
        {
            LogDebug("塔吊模拟器启动");
            
            // 初始化位置
            if (liftingPoints == null || liftingPoints.Length == 0)
            {
                CreateDefaultLiftingPoints();
            }
            
            if (placementPoints == null || placementPoints.Length == 0)
            {
                CreateDefaultPlacementPoints();
            }
            
            if (restPosition == null)
            {
                restPosition = transform;
            }
            
            // 设置初始状态
            ChangeState(CraneState.Idle);
            currentHeight = 0f;
            currentRotation = 0f;
            currentReach = maxReach * 0.3f;
            currentLoad = 0f;
            
            // 发送塔吊启动事件
            SendCraneEvent("crane_start", "塔吊启动", $"{craneName} 由 {operatorName} 操作启动");
            
            // 启动安全检查
            if (enableSafetyEvents)
            {
                SafetyCheckLoop(cancellationToken).Forget();
            }
            
            // 启动作业循环
            OperationLoop(cancellationToken).Forget();
        }
        
        protected override void OnSimulationUpdate(float deltaTime)
        {
            totalOperationTime += deltaTime;
            
            switch (currentState)
            {
                case CraneState.MovingToLift:
                    UpdateMovingToLift(deltaTime);
                    break;
                case CraneState.Lifting:
                    UpdateLifting(deltaTime);
                    break;
                case CraneState.MovingToPlace:
                    UpdateMovingToPlace(deltaTime);
                    break;
                case CraneState.Placing:
                    UpdatePlacing(deltaTime);
                    break;
                case CraneState.Returning:
                    UpdateReturning(deltaTime);
                    break;
            }
            
            // 更新环境影响
            UpdateEnvironmentalImpact();
        }
        
        protected override UniTask OnStopSimulationAsync()
        {
            LogDebug("塔吊模拟器停止");

            // 发送塔吊停止事件
            SendCraneEvent("crane_stop", "塔吊停止",
                $"{craneName} 停止作业，共完成{completedOperations}次作业，发生{totalSafetyEvents}次安全事件");

            return UniTask.CompletedTask;
        }
        
        /// <summary>
        /// 作业循环
        /// </summary>
        private async UniTaskVoid OperationLoop(CancellationToken cancellationToken)
        {
            try
            {
                while (!cancellationToken.IsCancellationRequested && completedOperations < dailyOperations)
                {
                    // 等待作业间隔
                    await UniTask.Delay(TimeSpan.FromSeconds(operationInterval), cancellationToken: cancellationToken);
                    
                    // 开始新的作业
                    await StartNewOperation(cancellationToken);
                }
                
                LogDebug($"完成所有作业，共{completedOperations}次");
            }
            catch (OperationCanceledException)
            {
                LogDebug("作业循环被取消");
            }
        }
        
        /// <summary>
        /// 开始新作业
        /// </summary>
        private async UniTask StartNewOperation(CancellationToken cancellationToken)
        {
            if (currentState != CraneState.Idle) return;
            
            // 创建新作业
            currentOperation = new CraneOperation
            {
                operationId = Guid.NewGuid().ToString(),
                liftPoint = liftingPoints[UnityEngine.Random.Range(0, liftingPoints.Length)],
                placePoint = placementPoints[UnityEngine.Random.Range(0, placementPoints.Length)],
                loadWeight = UnityEngine.Random.Range(maxLoad * 0.3f, maxLoad * 0.8f),
                startTime = DateTime.UtcNow
            };
            
            LogDebug($"开始作业 {completedOperations + 1}: 载重 {currentOperation.loadWeight:F0}kg");
            
            // 发送作业开始事件
            SendOperationEvent("crane_operation_start", "塔吊作业开始", 
                $"开始第{completedOperations + 1}次作业，载重{currentOperation.loadWeight:F0}kg");
            
            ChangeState(CraneState.MovingToLift);
        }
        
        /// <summary>
        /// 更新移动到起吊位置状态
        /// </summary>
        private void UpdateMovingToLift(float deltaTime)
        {
            if (MoveCraneToPosition(currentOperation.liftPoint.position, deltaTime))
            {
                LogDebug("到达起吊位置");
                ChangeState(CraneState.Lifting);
            }
        }
        
        /// <summary>
        /// 更新起吊状态
        /// </summary>
        private void UpdateLifting(float deltaTime)
        {
            float liftTime = Time.time - stateStartTime;
            float targetHeight = currentOperation.liftPoint.position.y + 10f; // 起吊高度
            
            // 模拟起吊过程
            currentHeight = Mathf.MoveTowards(currentHeight, targetHeight, liftingSpeed * deltaTime);
            currentLoad = currentOperation.loadWeight;
            
            if (Mathf.Abs(currentHeight - targetHeight) < 0.1f)
            {
                LogDebug("起吊完成");
                totalLiftingTime += liftTime;
                maxLoadLifted = Mathf.Max(maxLoadLifted, currentLoad);
                
                // 发送起吊完成事件
                SendOperationEvent("lifting_complete", "起吊完成", 
                    $"成功起吊{currentLoad:F0}kg物料到{currentHeight:F1}m高度");
                
                ChangeState(CraneState.MovingToPlace);
            }
        }
        
        /// <summary>
        /// 更新移动到放置位置状态
        /// </summary>
        private void UpdateMovingToPlace(float deltaTime)
        {
            if (MoveCraneToPosition(currentOperation.placePoint.position, deltaTime))
            {
                LogDebug("到达放置位置");
                ChangeState(CraneState.Placing);
            }
        }
        
        /// <summary>
        /// 更新放置状态
        /// </summary>
        private void UpdatePlacing(float deltaTime)
        {
            float targetHeight = currentOperation.placePoint.position.y;
            
            // 模拟放置过程
            currentHeight = Mathf.MoveTowards(currentHeight, targetHeight, liftingSpeed * deltaTime);
            
            if (Mathf.Abs(currentHeight - targetHeight) < 0.1f)
            {
                LogDebug("放置完成");
                currentLoad = 0f;
                completedOperations++;
                
                // 发送放置完成事件
                SendOperationEvent("placing_complete", "放置完成", 
                    $"成功放置物料，完成第{completedOperations}次作业");
                
                // 发送作业完成事件
                SendOperationEvent("crane_operation_complete", "塔吊作业完成", 
                    $"完成第{completedOperations}次作业");
                
                ChangeState(CraneState.Returning);
            }
        }
        
        /// <summary>
        /// 更新返回状态
        /// </summary>
        private void UpdateReturning(float deltaTime)
        {
            if (MoveCraneToPosition(restPosition.position, deltaTime))
            {
                LogDebug("返回休息位置");
                ChangeState(CraneState.Idle);
            }
        }
        
        /// <summary>
        /// 移动塔吊到指定位置
        /// </summary>
        private bool MoveCraneToPosition(Vector3 targetPosition, float deltaTime)
        {
            // 计算目标旋转角度和臂长
            Vector3 localTarget = transform.InverseTransformPoint(targetPosition);
            float targetRotation = Mathf.Atan2(localTarget.x, localTarget.z) * Mathf.Rad2Deg;
            float targetReach = Mathf.Clamp(new Vector2(localTarget.x, localTarget.z).magnitude, 5f, maxReach);
            
            // 移动旋转
            float rotationDiff = Mathf.DeltaAngle(currentRotation, targetRotation);
            if (Mathf.Abs(rotationDiff) > 1f)
            {
                float rotationStep = Mathf.Sign(rotationDiff) * rotationSpeed * deltaTime;
                currentRotation += rotationStep;
                currentRotation = Mathf.Repeat(currentRotation, 360f);
            }
            
            // 移动小车
            currentReach = Mathf.MoveTowards(currentReach, targetReach, trolleySpeed * deltaTime);
            
            // 检查是否到达目标
            bool rotationReached = Mathf.Abs(rotationDiff) <= 1f;
            bool reachReached = Mathf.Abs(currentReach - targetReach) <= 0.1f;
            
            return rotationReached && reachReached;
        }
        
        /// <summary>
        /// 安全检查循环
        /// </summary>
        private async UniTaskVoid SafetyCheckLoop(CancellationToken cancellationToken)
        {
            try
            {
                while (!cancellationToken.IsCancellationRequested)
                {
                    await UniTask.Delay(TimeSpan.FromSeconds(safetyCheckInterval), cancellationToken: cancellationToken);
                    
                    // 只在作业状态下检查安全事件
                    if (currentState != CraneState.Idle)
                    {
                        CheckSafetyEvents();
                    }
                }
            }
            catch (OperationCanceledException)
            {
                LogDebug("安全检查循环被取消");
            }
        }
        
        /// <summary>
        /// 检查安全事件
        /// </summary>
        private void CheckSafetyEvents()
        {
            if (UnityEngine.Random.Range(0f, 1f) <= safetyEventProbability)
            {
                string eventType = safetyEventTypes[UnityEngine.Random.Range(0, safetyEventTypes.Length)];
                totalSafetyEvents++;
                
                LogDebug($"检测到安全事件: {eventType}");
                
                // 发送安全事件
                SendSafetyEvent(eventType);
                
                // 根据事件类型处理
                HandleSafetyEvent(eventType);
            }
        }
        
        /// <summary>
        /// 处理安全事件
        /// </summary>
        private void HandleSafetyEvent(string eventType)
        {
            switch (eventType)
            {
                case "超载警告":
                    // 减少载重
                    currentLoad = Mathf.Min(currentLoad, maxLoad * 0.9f);
                    break;
                case "风速过大":
                    // 暂停作业
                    if (currentState != CraneState.Idle)
                    {
                        ChangeState(CraneState.Emergency);
                    }
                    break;
                case "限位触发":
                    // 限制移动范围
                    currentReach = Mathf.Min(currentReach, maxReach * 0.8f);
                    currentHeight = Mathf.Min(currentHeight, maxHeight * 0.8f);
                    break;
            }
        }
        
        /// <summary>
        /// 更新环境影响
        /// </summary>
        private void UpdateEnvironmentalImpact()
        {
            if (currentState != CraneState.Idle)
            {
                // 发送环境影响数据（噪声和扬尘）
                SendEnvironmentalImpact();
            }
        }
        
        /// <summary>
        /// 创建默认起吊点
        /// </summary>
        private void CreateDefaultLiftingPoints()
        {
            liftingPoints = new Transform[4];
            Vector3 basePos = transform.position;
            
            for (int i = 0; i < 4; i++)
            {
                GameObject point = new GameObject($"LiftPoint_{i}");
                point.transform.position = basePos + new Vector3(
                    UnityEngine.Random.Range(-15f, 15f),
                    0f,
                    UnityEngine.Random.Range(-15f, 15f)
                );
                liftingPoints[i] = point.transform;
            }
        }
        
        /// <summary>
        /// 创建默认放置点
        /// </summary>
        private void CreateDefaultPlacementPoints()
        {
            placementPoints = new Transform[4];
            Vector3 basePos = transform.position;
            
            for (int i = 0; i < 4; i++)
            {
                GameObject point = new GameObject($"PlacePoint_{i}");
                point.transform.position = basePos + new Vector3(
                    UnityEngine.Random.Range(-20f, 20f),
                    UnityEngine.Random.Range(5f, 15f),
                    UnityEngine.Random.Range(-20f, 20f)
                );
                placementPoints[i] = point.transform;
            }
        }
        
        /// <summary>
        /// 改变塔吊状态
        /// </summary>
        private void ChangeState(CraneState newState)
        {
            if (currentState != newState)
            {
                LogDebug($"塔吊状态变化: {currentState} -> {newState}");
                currentState = newState;
                stateStartTime = Time.time;
                
                // 发送状态变化事件
                SendStatusEvent(currentState.ToString(), newState.ToString());
            }
        }
        
        /// <summary>
        /// 发送塔吊事件
        /// </summary>
        private void SendCraneEvent(string eventType, string title, string description)
        {
            var eventData = new OperationEventData
            {
                operationType = eventType,
                operatorId = operatorId,
                operatorName = operatorName,
                targetId = craneId,
                targetType = "crane",
                title = title,
                description = description,
                sourceId = craneId
            };
            
            eventData.location.SetPosition(transform.position);
            
            IoTSystem.IoTSystem.Instance?.SendIOTEvent(craneId, eventType, title, description, eventData);
        }
        
        /// <summary>
        /// 发送状态变化事件
        /// </summary>
        private void SendStatusEvent(string previousStatus, string currentStatus)
        {
            var statusEvent = new StatusEventData
            {
                previousStatus = previousStatus,
                currentStatus = currentStatus,
                equipmentId = craneId,
                equipmentType = "crane",
                sourceId = craneId,
                title = "塔吊状态变化",
                description = $"{craneName} 状态从{previousStatus}变为{currentStatus}"
            };
            
            IoTSystem.IoTSystem.Instance?.SendIOTEvent(craneId, "status_change", "塔吊状态变化", statusEvent.description, statusEvent);
        }
        
        /// <summary>
        /// 发送作业事件
        /// </summary>
        private void SendOperationEvent(string operationType, string title, string description)
        {
            var operationEvent = new OperationEventData
            {
                operationType = operationType,
                operatorId = operatorId,
                operatorName = operatorName,
                targetId = craneId,
                targetType = "crane",
                title = title,
                description = description,
                sourceId = craneId
            };
            
            operationEvent.location.SetPosition(transform.position);
            
            IoTSystem.IoTSystem.Instance?.SendIOTEvent(craneId, operationType, title, description, operationEvent);
        }
        
        /// <summary>
        /// 发送安全事件
        /// </summary>
        private void SendSafetyEvent(string violationType)
        {
            var safetyEvent = new SafetyEventData
            {
                violationType = violationType,
                workerId = operatorId,
                workerName = operatorName,
                sourceId = craneId,
                title = "塔吊安全事件",
                description = $"{craneName} 发生安全事件: {violationType}",
                severity = "warning"
            };
            
            safetyEvent.location.SetPosition(transform.position);
            
            IoTSystem.IoTSystem.Instance?.SendIOTEvent(craneId, "safety_violation", "塔吊安全事件", safetyEvent.description, safetyEvent);
        }
        
        /// <summary>
        /// 发送环境影响
        /// </summary>
        private void SendEnvironmentalImpact()
        {
            // 这里可以触发环境模拟器更新噪声和扬尘数据
            // 简化实现：直接发送环境事件
            var environmentEvent = new IoTEventData
            {
                eventType = "environmental_impact",
                sourceId = craneId,
                title = "塔吊环境影响",
                description = $"{craneName} 作业产生噪声{noiseLevel}dB，扬尘{dustGeneration}μg/m³",
                data = new { noiseLevel, dustGeneration, radius = environmentalImpactRadius }
            };
            
            IoTSystem.IoTSystem.Instance?.SendIOTEvent(craneId, "environmental_impact", "塔吊环境影响", environmentEvent.description, environmentEvent);
        }
        
        /// <summary>
        /// 调试日志
        /// </summary>
        private void LogDebug(string message)
        {
            if (enableDebugLog)
            {
                Debug.Log($"[CraneSimulator:{craneId}] {message}");
            }
        }
        
        /// <summary>
        /// 绘制调试信息
        /// </summary>
        private void OnDrawGizmos()
        {
            if (!showOperationGizmos) return;
            
            // 绘制塔吊基座
            Gizmos.color = operationColor;
            Gizmos.DrawWireCube(transform.position, Vector3.one * 2f);
            
            // 绘制作业范围
            Gizmos.color = Color.yellow;
            Gizmos.DrawWireSphere(transform.position, maxReach);
            
            // 绘制当前臂长和高度
            Vector3 armEnd = transform.position + 
                Quaternion.Euler(0, currentRotation, 0) * Vector3.forward * currentReach +
                Vector3.up * currentHeight;
            
            Gizmos.color = Color.red;
            Gizmos.DrawLine(transform.position + Vector3.up * currentHeight, armEnd);
            Gizmos.DrawWireSphere(armEnd, 0.5f);
            
            // 绘制起吊点和放置点
            if (liftingPoints != null)
            {
                Gizmos.color = Color.green;
                foreach (var point in liftingPoints)
                {
                    if (point != null)
                    {
                        Gizmos.DrawWireSphere(point.position, 1f);
                    }
                }
            }
            
            if (placementPoints != null)
            {
                Gizmos.color = Color.blue;
                foreach (var point in placementPoints)
                {
                    if (point != null)
                    {
                        Gizmos.DrawWireSphere(point.position, 1f);
                    }
                }
            }
            
            // 绘制环境影响范围
            Gizmos.color = new Color(1f, 0.5f, 0f, 0.3f);
            Gizmos.DrawSphere(transform.position, environmentalImpactRadius);
        }
    }
    
    /// <summary>
    /// 塔吊状态枚举
    /// </summary>
    public enum CraneState
    {
        Idle,           // 空闲
        MovingToLift,   // 移动到起吊位置
        Lifting,        // 起吊中
        MovingToPlace,  // 移动到放置位置
        Placing,        // 放置中
        Returning,      // 返回休息位置
        Emergency,      // 紧急状态
        Maintenance     // 维护状态
    }
    
    /// <summary>
    /// 塔吊作业数据
    /// </summary>
    [Serializable]
    public class CraneOperation
    {
        public string operationId;
        public Transform liftPoint;
        public Transform placePoint;
        public float loadWeight;
        public DateTime startTime;
    }
}
