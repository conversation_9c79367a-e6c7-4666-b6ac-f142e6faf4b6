using UnityEngine;
using Simulation.Simulators;

namespace Simulation.Tests
{
    /// <summary>
    /// 监控摄像头模拟器测试脚本
    /// 用于测试摄像头模拟器的基本功能
    /// </summary>
    public class CameraMonitorTest : MonoBehaviour
    {
        [Header("测试配置")]
        [SerializeField] private CameraMonitorSimulator cameraSimulator;
        [SerializeField] private bool enableAutoTest = false;
        [SerializeField] private float testInterval = 10f;
        
        [Header("云台控制测试")]
        [SerializeField] private bool testPTZControl = false;
        [SerializeField] private float testHorizontalAngle = 45f;
        [SerializeField] private float testVerticalAngle = 30f;
        [SerializeField] private float testZoomLevel = 2f;
        
        [Header("测试对象")]
        [SerializeField] private GameObject testWorker;
        [SerializeField] private GameObject testVehicle;
        [SerializeField] private float moveSpeed = 2f;
        [SerializeField] private float moveRadius = 10f;
        
        private float lastTestTime;
        private bool isMovingTestObjects;
        
        private void Start()
        {
            // 自动查找摄像头模拟器
            if (cameraSimulator == null)
            {
                cameraSimulator = FindObjectOfType<CameraMonitorSimulator>();
            }
            
            if (cameraSimulator == null)
            {
                Debug.LogError("[CameraMonitorTest] 未找到CameraMonitorSimulator组件");
                return;
            }
            
            Debug.Log("[CameraMonitorTest] 监控摄像头测试脚本已启动");
            
            // 创建测试对象
            CreateTestObjects();
        }
        
        private void Update()
        {
            if (!enableAutoTest || cameraSimulator == null) return;
            
            // 定期执行测试
            if (Time.time - lastTestTime >= testInterval)
            {
                PerformAutoTest();
                lastTestTime = Time.time;
            }
            
            // 移动测试对象
            if (isMovingTestObjects)
            {
                MoveTestObjects();
            }
        }
        
        /// <summary>
        /// 创建测试对象
        /// </summary>
        private void CreateTestObjects()
        {
            // 创建测试工人
            if (testWorker == null)
            {
                testWorker = GameObject.CreatePrimitive(PrimitiveType.Capsule);
                testWorker.name = "TestWorker";
                testWorker.tag = "Worker";
                testWorker.transform.position = transform.position + Vector3.right * 5f;
                testWorker.GetComponent<Renderer>().material.color = Color.blue;
                
                // 添加Rigidbody以便物理检测
                var rb = testWorker.AddComponent<Rigidbody>();
                rb.isKinematic = true;
            }
            
            // 创建测试车辆
            if (testVehicle == null)
            {
                testVehicle = GameObject.CreatePrimitive(PrimitiveType.Cube);
                testVehicle.name = "TestVehicle";
                testVehicle.tag = "Vehicle";
                testVehicle.transform.position = transform.position + Vector3.left * 5f;
                testVehicle.transform.localScale = new Vector3(2f, 1f, 4f);
                testVehicle.GetComponent<Renderer>().material.color = Color.red;
                
                // 添加Rigidbody以便物理检测
                var rb = testVehicle.AddComponent<Rigidbody>();
                rb.isKinematic = true;
            }
            
            Debug.Log("[CameraMonitorTest] 测试对象已创建");
        }
        
        /// <summary>
        /// 执行自动测试
        /// </summary>
        private void PerformAutoTest()
        {
            Debug.Log("[CameraMonitorTest] 执行自动测试...");
            
            // 测试云台控制
            if (testPTZControl)
            {
                TestPTZControl();
            }
            
            // 切换测试对象移动状态
            isMovingTestObjects = !isMovingTestObjects;
            Debug.Log($"[CameraMonitorTest] 测试对象移动状态: {(isMovingTestObjects ? "开始移动" : "停止移动")}");
            
            // 显示监控统计
            if (cameraSimulator != null)
            {
                string stats = cameraSimulator.GetMonitoringStatistics();
                Debug.Log($"[CameraMonitorTest] {stats}");
            }
        }
        
        /// <summary>
        /// 测试云台控制
        /// </summary>
        private void TestPTZControl()
        {
            if (cameraSimulator == null) return;
            
            // 随机生成云台控制参数
            float randomH = Random.Range(-90f, 90f);
            float randomV = Random.Range(-30f, 60f);
            float randomZ = Random.Range(1f, 5f);
            
            cameraSimulator.ControlPTZ(randomH, randomV, randomZ);
            Debug.Log($"[CameraMonitorTest] 测试云台控制: H={randomH:F1}°, V={randomV:F1}°, Z={randomZ:F1}x");
        }
        
        /// <summary>
        /// 移动测试对象
        /// </summary>
        private void MoveTestObjects()
        {
            float time = Time.time * moveSpeed;
            Vector3 cameraPos = cameraSimulator.transform.position;
            
            // 工人做圆周运动
            if (testWorker != null)
            {
                Vector3 workerPos = cameraPos + new Vector3(
                    Mathf.Cos(time) * moveRadius,
                    0,
                    Mathf.Sin(time) * moveRadius
                );
                testWorker.transform.position = workerPos;
            }
            
            // 车辆做直线往返运动
            if (testVehicle != null)
            {
                Vector3 vehiclePos = cameraPos + new Vector3(
                    Mathf.PingPong(time, moveRadius * 2) - moveRadius,
                    0,
                    moveRadius * 0.5f
                );
                testVehicle.transform.position = vehiclePos;
            }
        }
        
        /// <summary>
        /// 手动测试按钮 - 开始模拟
        /// </summary>
        [ContextMenu("开始摄像头模拟")]
        public void StartCameraSimulation()
        {
            if (cameraSimulator != null)
            {
                cameraSimulator.StartSimulationAsync();
                Debug.Log("[CameraMonitorTest] 手动启动摄像头模拟");
            }
        }
        
        /// <summary>
        /// 手动测试按钮 - 停止模拟
        /// </summary>
        [ContextMenu("停止摄像头模拟")]
        public void StopCameraSimulation()
        {
            if (cameraSimulator != null)
            {
                cameraSimulator.StopSimulation();
                Debug.Log("[CameraMonitorTest] 手动停止摄像头模拟");
            }
        }
        
        /// <summary>
        /// 手动测试按钮 - 测试云台控制
        /// </summary>
        [ContextMenu("测试云台控制")]
        public void TestPTZControlManual()
        {
            if (cameraSimulator != null)
            {
                cameraSimulator.ControlPTZ(testHorizontalAngle, testVerticalAngle, testZoomLevel);
                Debug.Log($"[CameraMonitorTest] 手动测试云台控制: H={testHorizontalAngle}°, V={testVerticalAngle}°, Z={testZoomLevel}x");
            }
        }
        
        /// <summary>
        /// 手动测试按钮 - 切换对象移动
        /// </summary>
        [ContextMenu("切换测试对象移动")]
        public void ToggleTestObjectMovement()
        {
            isMovingTestObjects = !isMovingTestObjects;
            Debug.Log($"[CameraMonitorTest] 测试对象移动状态: {(isMovingTestObjects ? "开始移动" : "停止移动")}");
        }
        
        /// <summary>
        /// 手动测试按钮 - 显示监控统计
        /// </summary>
        [ContextMenu("显示监控统计")]
        public void ShowMonitoringStatistics()
        {
            if (cameraSimulator != null)
            {
                string stats = cameraSimulator.GetMonitoringStatistics();
                Debug.Log($"[CameraMonitorTest] {stats}");
            }
        }
        
        private void OnDestroy()
        {
            // 清理测试对象
            if (testWorker != null && testWorker.name == "TestWorker")
            {
                DestroyImmediate(testWorker);
            }
            
            if (testVehicle != null && testVehicle.name == "TestVehicle")
            {
                DestroyImmediate(testVehicle);
            }
        }
    }
}
