using UnityEngine;
using Cysharp.Threading.Tasks;
using System.Threading;
using System;
using System.Text;
using Simulation.IoTSystem.Core;

namespace Simulation.IoTSystem.Data
{
    /// <summary>
    /// 数据处理器
    /// 负责传感器数据的格式化、验证和转换
    /// </summary>
    public class DataProcessor : MonoBehaviour, IDataProcessor
    {
        [Header("数据处理配置")]
        [SerializeField] private bool enableDataValidation = true;
        [SerializeField] private bool enableDataCompression = false;
        [SerializeField] private bool enableDebugLog = false;
        
        /// <summary>
        /// 处理传感器数据
        /// </summary>
        public async UniTask<byte[]> ProcessSensorDataAsync(string sensorId, object data, CancellationToken cancellationToken = default)
        {
            try
            {
                // 验证数据
                if (enableDataValidation && !ValidateData(data))
                {
                    throw new ArgumentException($"数据验证失败: {data}");
                }
                
                // 创建标准化的数据包
                var dataPacket = CreateDataPacket(sensorId, data);
                
                // 序列化为JSON
                var jsonData = JsonUtility.ToJson(dataPacket, true);
                
                LogDebug($"处理传感器数据 - 传感器: {sensorId}, 数据: {jsonData}");
                
                // 转换为字节数组
                var bytes = Encoding.UTF8.GetBytes(jsonData);
                
                // 可选的数据压缩
                if (enableDataCompression)
                {
                    bytes = await CompressDataAsync(bytes, cancellationToken);
                }
                
                return bytes;
            }
            catch (Exception ex)
            {
                LogError($"处理传感器数据异常: {ex.Message}");
                throw;
            }
        }
        
        /// <summary>
        /// 解析接收到的数据
        /// </summary>
        public async UniTask<object> ParseReceivedDataAsync(string topic, byte[] data, CancellationToken cancellationToken = default)
        {
            try
            {
                // 可选的数据解压缩
                var processedData = data;
                if (enableDataCompression)
                {
                    processedData = await DecompressDataAsync(data, cancellationToken);
                }
                
                // 转换为字符串
                var jsonData = Encoding.UTF8.GetString(processedData);
                
                LogDebug($"解析接收数据 - 主题: {topic}, 数据: {jsonData}");
                
                // 尝试解析为标准数据包
                try
                {
                    var dataPacket = JsonUtility.FromJson<SensorDataPacket>(jsonData);
                    return dataPacket;
                }
                catch
                {
                    // 如果不是标准格式，返回原始JSON字符串
                    return jsonData;
                }
            }
            catch (Exception ex)
            {
                LogError($"解析接收数据异常: {ex.Message}");
                throw;
            }
        }
        
        /// <summary>
        /// 验证数据格式
        /// </summary>
        public bool ValidateData(object data)
        {
            if (data == null)
            {
                LogWarning("数据为空");
                return false;
            }
            
            // 基本类型验证
            if (data is string str && string.IsNullOrEmpty(str))
            {
                LogWarning("字符串数据为空");
                return false;
            }
            
            // 数值类型验证
            if (data is float f && (float.IsNaN(f) || float.IsInfinity(f)))
            {
                LogWarning("浮点数数据无效");
                return false;
            }
            
            if (data is double d && (double.IsNaN(d) || double.IsInfinity(d)))
            {
                LogWarning("双精度数据无效");
                return false;
            }
            
            return true;
        }
        
        /// <summary>
        /// 创建标准化数据包
        /// </summary>
        private SensorDataPacket CreateDataPacket(string sensorId, object data)
        {
            return new SensorDataPacket
            {
                sensorId = sensorId,
                timestamp = DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"),
                dataType = data.GetType().Name,
                data = data,
                version = "1.0"
            };
        }
        
        /// <summary>
        /// 压缩数据（简单实现，实际项目中可使用更高效的压缩算法）
        /// </summary>
        private async UniTask<byte[]> CompressDataAsync(byte[] data, CancellationToken cancellationToken)
        {
            // 这里可以实现实际的压缩算法
            // 为了简化，暂时返回原始数据
            await UniTask.Yield(cancellationToken);
            return data;
        }
        
        /// <summary>
        /// 解压缩数据
        /// </summary>
        private async UniTask<byte[]> DecompressDataAsync(byte[] data, CancellationToken cancellationToken)
        {
            // 这里可以实现实际的解压缩算法
            // 为了简化，暂时返回原始数据
            await UniTask.Yield(cancellationToken);
            return data;
        }
        
        /// <summary>
        /// 调试日志
        /// </summary>
        private void LogDebug(string message)
        {
            if (enableDebugLog)
            {
                Debug.Log($"[DataProcessor] {message}");
            }
        }
        
        /// <summary>
        /// 错误日志
        /// </summary>
        private void LogError(string message)
        {
            Debug.LogError($"[DataProcessor] {message}");
        }
        
        /// <summary>
        /// 警告日志
        /// </summary>
        private void LogWarning(string message)
        {
            Debug.LogWarning($"[DataProcessor] {message}");
        }
    }
    
    /// <summary>
    /// 标准化传感器数据包
    /// </summary>
    [Serializable]
    public class SensorDataPacket
    {
        public string sensorId;
        public string timestamp;
        public string dataType;
        public object data;
        public string version;
    }
    
    /// <summary>
    /// 位置数据结构
    /// </summary>
    [Serializable]
    public class PositionData
    {
        public float x;
        public float y;
        public float z;
        public float rotationX;
        public float rotationY;
        public float rotationZ;
    }
    
    /// <summary>
    /// 身份数据结构
    /// </summary>
    [Serializable]
    public class IdentityData
    {
        public string userId;
        public string userName;
        public string userType;
        public string department;
        public string[] permissions;
    }
    
    /// <summary>
    /// 运动状态数据结构
    /// </summary>
    [Serializable]
    public class MotionData
    {
        public float speed;
        public string direction;
        public string workState;
        public bool isMoving;
    }
    
    /// <summary>
    /// 环境数据结构
    /// </summary>
    [Serializable]
    public class EnvironmentData
    {
        public float value;
        public string unit;
        public string status;
        public float threshold;
    }
}
