using System;
using System.Collections.Generic;
using UnityEngine;
using Cysharp.Threading.Tasks;
using System.Threading;
using Simulation.Data;

namespace Simulation.IoTSystem
{
    /// <summary>
    /// MQTT集成验证器
    /// 验证MQTTManager与IoT系统的完整集成
    /// </summary>
    public class MQTTIntegrationValidator : MonoBehaviour
    {
        [Header("验证配置")]
        [SerializeField] private bool autoStartValidation = false;
        [SerializeField] private float validationTimeout = 60f;
        [SerializeField] private int testDataCount = 10;
        
        [Header("验证结果")]
        [SerializeField] private ValidationResult lastValidationResult;
        
        private IoTSystem iotSystem;
        private CancellationTokenSource validationCancellationTokenSource;
        private bool isValidationRunning = false;
        
        // 验证事件
        public event Action<ValidationResult> OnValidationCompleted;
        public event Action<string> OnValidationProgress;
        
        private void Start()
        {
            // 获取IoT系统
            iotSystem = IoTSystem.Instance;
            
            if (autoStartValidation)
            {
                StartValidation();
            }
        }
        
        private void OnDestroy()
        {
            validationCancellationTokenSource?.Cancel();
        }
        
        /// <summary>
        /// 开始验证
        /// </summary>
        [ContextMenu("开始MQTT集成验证")]
        public void StartValidation()
        {
            if (isValidationRunning)
            {
                Debug.LogWarning("[MQTTValidator] 验证已在运行中");
                return;
            }
            
            RunValidationAsync().Forget();
        }
        
        /// <summary>
        /// 停止验证
        /// </summary>
        [ContextMenu("停止验证")]
        public void StopValidation()
        {
            validationCancellationTokenSource?.Cancel();
            isValidationRunning = false;
            OnValidationProgress?.Invoke("验证已停止");
        }
        
        /// <summary>
        /// 运行验证的异步方法
        /// </summary>
        private async UniTaskVoid RunValidationAsync()
        {
            isValidationRunning = true;
            validationCancellationTokenSource = new CancellationTokenSource();
            
            var result = new ValidationResult
            {
                StartTime = DateTime.Now,
                TestDataCount = testDataCount
            };
            
            try
            {
                OnValidationProgress?.Invoke("开始MQTT集成验证");
                
                // 1. 验证IoT系统初始化
                OnValidationProgress?.Invoke("验证IoT系统初始化...");
                result.IoTSystemInitialized = await ValidateIoTSystemInitialization(validationCancellationTokenSource.Token);
                
                if (!result.IoTSystemInitialized)
                {
                    result.ErrorMessage = "IoT系统初始化失败";
                    await CompleteValidation(result);
                    return;
                }
                
                // 2. 验证MQTT连接
                OnValidationProgress?.Invoke("验证MQTT连接...");
                result.MQTTConnected = await ValidateMQTTConnection(validationCancellationTokenSource.Token);
                
                if (!result.MQTTConnected)
                {
                    result.ErrorMessage = "MQTT连接失败";
                    await CompleteValidation(result);
                    return;
                }
                
                // 3. 验证传感器数据发布
                OnValidationProgress?.Invoke("验证传感器数据发布...");
                result.SensorDataPublished = await ValidateSensorDataPublishing(result, validationCancellationTokenSource.Token);
                
                // 4. 验证事件发布
                OnValidationProgress?.Invoke("验证事件发布...");
                result.EventsPublished = await ValidateEventPublishing(result, validationCancellationTokenSource.Token);
                
                // 5. 验证数据格式
                OnValidationProgress?.Invoke("验证数据格式...");
                result.DataFormatValid = ValidateDataFormats();
                
                // 6. 验证主题命名
                OnValidationProgress?.Invoke("验证主题命名...");
                result.TopicNamingValid = ValidateTopicNaming();
                
                result.Success = result.IoTSystemInitialized && 
                               result.MQTTConnected && 
                               result.SensorDataPublished && 
                               result.EventsPublished && 
                               result.DataFormatValid && 
                               result.TopicNamingValid;
                
                OnValidationProgress?.Invoke(result.Success ? "验证成功完成" : "验证完成，发现问题");
            }
            catch (OperationCanceledException)
            {
                result.ErrorMessage = "验证被取消";
                OnValidationProgress?.Invoke("验证被取消");
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.ErrorMessage = ex.Message;
                Debug.LogError($"[MQTTValidator] 验证异常: {ex.Message}");
                OnValidationProgress?.Invoke($"验证异常: {ex.Message}");
            }
            finally
            {
                await CompleteValidation(result);
            }
        }
        
        /// <summary>
        /// 验证IoT系统初始化
        /// </summary>
        private async UniTask<bool> ValidateIoTSystemInitialization(CancellationToken cancellationToken)
        {
            try
            {
                if (iotSystem == null)
                {
                    Debug.LogError("[MQTTValidator] IoT系统实例为空");
                    return false;
                }
                
                // 等待IoT系统初始化
                int waitCount = 0;
                while (!iotSystem.IsRunning && waitCount < 30)
                {
                    await UniTask.Delay(1000, cancellationToken: cancellationToken);
                    waitCount++;
                }
                
                return iotSystem.IsRunning;
            }
            catch (Exception ex)
            {
                Debug.LogError($"[MQTTValidator] IoT系统初始化验证失败: {ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// 验证MQTT连接
        /// </summary>
        private async UniTask<bool> ValidateMQTTConnection(CancellationToken cancellationToken)
        {
            try
            {
                // 等待MQTT连接
                int waitCount = 0;
                while (!iotSystem.IsConnected && waitCount < 30)
                {
                    await UniTask.Delay(1000, cancellationToken: cancellationToken);
                    waitCount++;
                }
                
                return iotSystem.IsConnected;
            }
            catch (Exception ex)
            {
                Debug.LogError($"[MQTTValidator] MQTT连接验证失败: {ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// 验证传感器数据发布
        /// </summary>
        private async UniTask<bool> ValidateSensorDataPublishing(ValidationResult result, CancellationToken cancellationToken)
        {
            try
            {
                for (int i = 0; i < testDataCount; i++)
                {
                    if (cancellationToken.IsCancellationRequested)
                        break;
                    
                    // 创建测试传感器数据
                    var testData = CreateTestSensorData(i);
                    
                    // 发布数据
                    iotSystem.PublishSensorData(testData);
                    result.PublishedSensorData++;
                    
                    await UniTask.Delay(100, cancellationToken: cancellationToken);
                }
                
                return result.PublishedSensorData > 0;
            }
            catch (Exception ex)
            {
                Debug.LogError($"[MQTTValidator] 传感器数据发布验证失败: {ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// 验证事件发布
        /// </summary>
        private async UniTask<bool> ValidateEventPublishing(ValidationResult result, CancellationToken cancellationToken)
        {
            try
            {
                for (int i = 0; i < 5; i++)
                {
                    if (cancellationToken.IsCancellationRequested)
                        break;
                    
                    // 创建测试事件
                    var testEvent = CreateTestEvent(i);
                    
                    // 发布事件
                    iotSystem.PublishEvent(testEvent);
                    result.PublishedEvents++;
                    
                    await UniTask.Delay(200, cancellationToken: cancellationToken);
                }
                
                return result.PublishedEvents > 0;
            }
            catch (Exception ex)
            {
                Debug.LogError($"[MQTTValidator] 事件发布验证失败: {ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// 验证数据格式
        /// </summary>
        private bool ValidateDataFormats()
        {
            try
            {
                // 验证传感器数据格式
                var sensorData = CreateTestSensorData(1);
                string json = JsonUtility.ToJson(sensorData);
                var parsed = JsonUtility.FromJson<SensorDataPacket>(json);
                
                if (parsed == null || parsed.SensorId != sensorData.SensorId)
                {
                    Debug.LogError("[MQTTValidator] 传感器数据格式验证失败");
                    return false;
                }
                
                // 验证事件数据格式
                var eventData = CreateTestEvent(1);
                string eventJson = JsonUtility.ToJson(eventData);
                var parsedEvent = JsonUtility.FromJson<EventPacket>(eventJson);
                
                if (parsedEvent == null || parsedEvent.EventType != eventData.EventType)
                {
                    Debug.LogError("[MQTTValidator] 事件数据格式验证失败");
                    return false;
                }
                
                return true;
            }
            catch (Exception ex)
            {
                Debug.LogError($"[MQTTValidator] 数据格式验证失败: {ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// 验证主题命名
        /// </summary>
        private bool ValidateTopicNaming()
        {
            try
            {
                // 验证传感器主题格式: sensors/{sensorId}/data
                string sensorTopic = "sensors/test_sensor_001/data";
                if (!sensorTopic.StartsWith("sensors/") || !sensorTopic.EndsWith("/data"))
                {
                    Debug.LogError("[MQTTValidator] 传感器主题命名格式错误");
                    return false;
                }
                
                // 验证事件主题格式: events/{eventType}
                string eventTopic = "events/safety_violation";
                if (!eventTopic.StartsWith("events/"))
                {
                    Debug.LogError("[MQTTValidator] 事件主题命名格式错误");
                    return false;
                }
                
                return true;
            }
            catch (Exception ex)
            {
                Debug.LogError($"[MQTTValidator] 主题命名验证失败: {ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// 创建测试传感器数据
        /// </summary>
        private SensorDataPacket CreateTestSensorData(int index)
        {
            return new SensorDataPacket
            {
                SensorId = $"test_sensor_{index:D3}",
                SensorType = "position",
                Timestamp = DateTime.UtcNow,
                Data = new PositionData
                {
                    X = UnityEngine.Random.Range(-10f, 10f),
                    Y = UnityEngine.Random.Range(0f, 5f),
                    Z = UnityEngine.Random.Range(-10f, 10f),
                    RotationY = UnityEngine.Random.Range(0f, 360f)
                }
            };
        }
        
        /// <summary>
        /// 创建测试事件
        /// </summary>
        private EventPacket CreateTestEvent(int index)
        {
            return new EventPacket
            {
                EventId = Guid.NewGuid().ToString(),
                EventType = "test_event",
                Title = $"测试事件 #{index}",
                Description = $"这是第{index}个测试事件",
                Severity = "info",
                Timestamp = DateTime.UtcNow,
                Source = "MQTTIntegrationValidator"
            };
        }
        
        /// <summary>
        /// 完成验证
        /// </summary>
        private async UniTask CompleteValidation(ValidationResult result)
        {
            result.EndTime = DateTime.Now;
            lastValidationResult = result;
            
            isValidationRunning = false;
            validationCancellationTokenSource?.Dispose();
            validationCancellationTokenSource = null;
            
            // 触发完成事件
            OnValidationCompleted?.Invoke(result);
            
            // 记录结果
            LogValidationResult(result);
        }
        
        /// <summary>
        /// 记录验证结果
        /// </summary>
        private void LogValidationResult(ValidationResult result)
        {
            Debug.Log($"[MQTTValidator] 验证完成 - 成功: {result.Success}");
            Debug.Log($"[MQTTValidator] IoT系统初始化: {result.IoTSystemInitialized}");
            Debug.Log($"[MQTTValidator] MQTT连接: {result.MQTTConnected}");
            Debug.Log($"[MQTTValidator] 传感器数据发布: {result.SensorDataPublished} ({result.PublishedSensorData}条)");
            Debug.Log($"[MQTTValidator] 事件发布: {result.EventsPublished} ({result.PublishedEvents}条)");
            Debug.Log($"[MQTTValidator] 数据格式验证: {result.DataFormatValid}");
            Debug.Log($"[MQTTValidator] 主题命名验证: {result.TopicNamingValid}");
            Debug.Log($"[MQTTValidator] 总耗时: {result.TotalTime:F2}秒");
            
            if (!result.Success && !string.IsNullOrEmpty(result.ErrorMessage))
            {
                Debug.LogError($"[MQTTValidator] 错误信息: {result.ErrorMessage}");
            }
        }
    }
    
    /// <summary>
    /// 验证结果数据结构
    /// </summary>
    [Serializable]
    public class ValidationResult
    {
        public DateTime StartTime;
        public DateTime EndTime;
        public bool Success;
        public string ErrorMessage;
        
        // 验证项目
        public bool IoTSystemInitialized;
        public bool MQTTConnected;
        public bool SensorDataPublished;
        public bool EventsPublished;
        public bool DataFormatValid;
        public bool TopicNamingValid;
        
        // 统计数据
        public int TestDataCount;
        public int PublishedSensorData;
        public int PublishedEvents;
        
        public double TotalTime => (EndTime - StartTime).TotalSeconds;
    }
}
