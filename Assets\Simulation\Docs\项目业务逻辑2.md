# 建筑工地数字孪生模拟系统业务逻辑文档

## 1. 系统整体业务架构

### 1.1 核心业务流程概览

```mermaid
graph TB
    A[工地模拟启动] --> B[初始化各模拟器]
    B --> C[启动异步业务流程]
    C --> D[工人日常作业流程]
    C --> E[车辆运输流程]
    C --> F[设备作业流程]
    C --> G[门禁管理流程]
    C --> H[物料管理流程]

    D --> I[环境模拟器监听]
    E --> I
    F --> I

    I --> J[环境数据生成]

    D --> K[传感器数据采集]
    E --> K
    F --> K
    G --> K
    H --> K
    J --> K

    K --> L[IoT系统数据处理]
    L --> M[MQTT数据发布]
    M --> N[数字孪生系统接收]
```

### 1.2 数据流向架构

```mermaid
graph LR
    subgraph "模拟层"
        A1[工人模拟器]
        A2[车辆模拟器]
        A3[设备模拟器]
        A4[门禁模拟器]
        A5[物料模拟器]
    end
    
    subgraph "传感器层"
        B1[位置传感器]
        B2[身份传感器]
        B3[运动传感器]
        B4[噪声传感器]
        B5[扬尘传感器]
    end
    
    subgraph "环境层"
        C1[环境模拟器]
    end
    
    subgraph "数据层"
        D1[IoT系统]
        D2[MQTT发布]
    end
    
    A1 --> B1
    A1 --> B2
    A2 --> B1
    A2 --> B3
    A3 --> B3
    
    A1 --> C1
    A2 --> C1
    A3 --> C1
    
    C1 --> B4
    C1 --> B5
    
    B1 --> D1
    B2 --> D1
    B3 --> D1
    B4 --> D1
    B5 --> D1
    
    D1 --> D2
```

## 2. 核心业务模块流程

### 2.1 工人管理业务流程

#### 2.1.1 工人日常作业流程

```mermaid
sequenceDiagram
    participant W as 工人模拟器
    participant G as 门禁系统
    participant P as 位置传感器
    participant I as 身份传感器
    participant IoT as IoT系统
    participant S as 安全检查
    
    W->>G: 刷卡进入工地
    G->>I: 识别并记录身份信息
    I->>IoT: 上报身份数据
    
    loop 工作日循环
        W->>W: 移动到工作位置
        W->>P: 更新位置信息
        P->>IoT: 上报位置数据
        
        W->>W: 执行工作任务
        W->>S: 随机安全检查
        
        alt 发现安全违规
            S->>IoT: 发送安全事件
        end
        
        W->>W: 等待下一个任务
    end
    
    W->>G: 刷卡离开工地
    G->>I: 识别并记录离开信息
    I->>IoT: 上报离开数据
```

#### 2.1.2 安全违规检测流程

```mermaid
flowchart TD
    A[工人执行任务] --> B{随机安全事件触发}
    B -->|触发概率| C[随机选择违规类型]
    B -->|继续工作| A

    C --> D[生成安全违规事件]
    D --> E[记录违规详情]
    E --> F[发送IoT事件]
    F --> A

    note1[违规类型包括:<br/>- 未佩戴安全帽<br/>- 未穿着防护服<br/>- 未佩戴安全带<br/>- 工作时使用手机<br/>- 违规操作设备]
    C -.-> note1
```

### 2.2 车辆管理业务流程

#### 2.2.1 车辆运输任务流程

```mermaid
sequenceDiagram
    participant V as 车辆模拟器
    participant G as 门禁系统
    participant P as 位置传感器
    participant M as 运动传感器
    participant Mat as 物料系统
    participant IoT as IoT系统
    
    V->>G: 车辆到达工地门口
    G->>G: 车牌识别记录
    G->>IoT: 记录进入信息
    
    V->>V: 移动到装载区
    V->>P: 更新位置信息
    P->>IoT: 上报位置数据
    
    V->>Mat: 开始装载物料
    Mat->>Mat: 更新库存状态
    Mat->>IoT: 上报物料变化
    
    V->>V: 运输到目标位置
    V->>M: 更新速度状态
    M->>IoT: 上报运动数据
    
    V->>Mat: 卸载物料
    Mat->>Mat: 更新目标库存
    Mat->>IoT: 上报物料变化
    
    V->>G: 车辆离开工地
    G->>IoT: 记录离开信息
```

#### 2.2.2 车辆状态监控流程

```mermaid
stateDiagram-v2
    [*] --> 等待任务
    等待任务 --> 前往工地 : 接收运输任务
    前往工地 --> 门禁识别 : 到达工地
    门禁识别 --> 进入工地 : 识别记录
    
    进入工地 --> 移动到装载区
    移动到装载区 --> 装载物料
    装载物料 --> 运输中 : 装载完成
    运输中 --> 卸载物料 : 到达目标
    卸载物料 --> 离开工地 : 卸载完成
    离开工地 --> 等待任务 : 任务完成
    
    note right of 运输中 : 持续监控位置和速度
    note right of 装载物料 : 更新物料库存
    note right of 卸载物料 : 更新目标库存
```

### 2.3 设备管理业务流程

#### 2.3.1 塔吊作业流程

```mermaid
flowchart TD
    A[塔吊启动] --> B[进入待机状态]

    B --> C{接收作业任务}
    C -->|有任务| D[移动到起吊位置]
    C -->|无任务| B

    D --> E[起吊物料]
    E --> F[旋转到目标位置]
    F --> G[下降放置物料]
    G --> H[返回待机位置]
    H --> B

    B --> I{随机事件触发}
    I -->|故障事件| J[设备故障]
    I -->|正常运行| B

    J --> K[发送故障事件]
    K --> L[等待维修完成]
    L --> B

    subgraph "数据采集"
        M[运动传感器]
        N[位置传感器]
        O[工作状态监控]
    end

    D --> M
    E --> M
    F --> M
    G --> M

    D --> N
    F --> N
    H --> N

    B --> O
    D --> O
    J --> O
```

#### 2.3.2 升降机作业流程

```mermaid
flowchart TD
    A[升降机启动] --> B[进入待机状态]

    B --> C{接收运输任务}
    C -->|有任务| D[移动到起始楼层]
    C -->|无任务| B

    D --> E[装载人员/物料]
    E --> F[运行到目标楼层]
    F --> G[卸载人员/物料]
    G --> H[返回待机位置]
    H --> B

    B --> I{随机事件触发}
    I -->|故障事件| J[设备故障]
    I -->|维护事件| K[设备维护]
    I -->|正常运行| B

    J --> L[发送故障事件]
    K --> M[发送维护事件]
    L --> N[等待维修完成]
    M --> O[等待维护完成]
    N --> B
    O --> B

    subgraph "数据采集"
        P[运动传感器]
        Q[位置传感器]
        R[工作状态监控]
    end

    D --> P
    E --> P
    F --> P
    G --> P

    D --> Q
    F --> Q
    H --> Q

    B --> R
    D --> R
    J --> R
    K --> R
```

### 2.4 环境监测业务流程

#### 2.4.1 环境影响传播模型

```mermaid
graph TD
    subgraph "噪声源"
        A1[工人作业]
        A2[车辆行驶]
        A3[设备运行]
    end
    
    subgraph "扬尘源"
        B1[车辆行驶]
        B2[物料装卸]
        B3[施工作业]
    end
    
    subgraph "环境计算"
        C1[噪声传播算法]
        C2[扬尘扩散算法]
    end
    
    subgraph "传感器采集"
        D1[噪声传感器]
        D2[扬尘传感器]
    end
    
    A1 --> C1
    A2 --> C1
    A3 --> C1
    
    B1 --> C2
    B2 --> C2
    B3 --> C2
    
    C1 --> D1
    C2 --> D2
    
    D1 --> E[IoT系统]
    D2 --> E
```

### 2.5 门禁管理业务流程

#### 2.5.1 人员出入管理流程

```mermaid
sequenceDiagram
    participant P as 人员
    participant G as 门禁设备
    participant I as 身份验证
    participant D as 数据库
    participant IoT as IoT系统
    participant S as 统计系统
    
    P->>G: 刷卡/扫码
    G->>I: 读取身份信息
    I->>G: 识别身份
    G->>P: 允许通过
    G->>IoT: 记录通过信息
    IoT->>S: 更新统计数据
```

### 2.6 物料管理业务流程

#### 2.6.1 物料出入库流程

```mermaid
flowchart TD
    A[物料到达] --> B{入库还是出库}
    
    B -->|入库| C[扫描物料信息]
    B -->|出库| M[接收出库申请]
    
    C --> D[质量检验]
    D --> E{检验结果}
    E -->|合格| F[分配存储位置]
    E -->|不合格| G[拒绝入库]
    
    F --> H[更新库存数据]
    H --> I[生成入库单]
    I --> J[发送IoT事件]
    
    M --> N[确认库存数量]
    N --> O{库存充足}
    O -->|充足| P[执行出库]
    O -->|不足| Q[部分出库/等待]

    P --> R[更新库存数据]
    R --> S[生成出库单]
    S --> T[发送IoT事件]

    G --> U[记录拒绝原因]
    Q --> V[记录等待状态]

    J --> End[流程结束]
    T --> End
    U --> End
    V --> End
```

## 3. 异步业务流程协调

### 3.1 多模拟器协调机制

```mermaid
gantt
    title 工地日常作业时间线
    dateFormat HH:mm
    axisFormat %H:%M
    
    section 工人作业
    进入工地    :active, worker1, 08:00, 08:30
    施工作业    :worker2, after worker1, 16:00
    离开工地    :worker3, after worker2, 17:00
    
    section 车辆运输
    物料运输1   :vehicle1, 09:00, 11:00
    物料运输2   :vehicle2, 13:00, 15:00
    
    section 设备作业
    塔吊作业    :crane1, 08:30, 17:00
    升降机运行  :elevator1, 08:00, 17:30
    
    section 环境监测
    噪声监测    :noise1, 08:00, 18:00
    扬尘监测    :dust1, 08:00, 18:00
```

### 3.2 事件驱动业务流程

```mermaid
sequenceDiagram
    participant W as 工人模拟器
    participant V as 车辆模拟器
    participant E as 环境模拟器
    participant IoT as IoT系统
    participant Ext as 外部系统
    
    Note over W,Ext: 工地作业开始
    
    W->>W: 开始施工作业
    W->>E: 触发噪声事件
    E->>E: 计算噪声传播
    E->>IoT: 发送噪声数据
    
    V->>V: 车辆进入工地
    V->>E: 触发扬尘事件
    E->>E: 计算扬尘扩散
    E->>IoT: 发送扬尘数据
    
    par 并行数据处理
        IoT->>IoT: 处理噪声数据
    and
        IoT->>IoT: 处理扬尘数据
    and
        IoT->>IoT: 处理位置数据
    end
    
    IoT->>Ext: MQTT批量发布
    Ext->>Ext: 数据分析处理
```


