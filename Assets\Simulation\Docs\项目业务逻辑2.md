# 建筑工地数字孪生模拟系统业务逻辑文档

## 1. 系统整体业务架构

### 1.1 核心业务流程概览

```mermaid
graph TB
    A[工地模拟启动] --> B[初始化各模拟器]
    B --> C[启动异步业务流程]
    C --> D[工人日常作业流程]
    C --> E[车辆运输流程]
    C --> F[设备作业流程]
    C --> G[门禁管理流程]
    C --> H[物料管理流程]
    
    D --> I[传感器数据采集]
    E --> I
    F --> I
    G --> I
    H --> I
    
    I --> J[环境影响计算]
    I --> K[IoT系统数据处理]
    J --> K
    K --> L[MQTT数据发布]
    L --> M[数字孪生系统接收]
```

### 1.2 数据流向架构

```mermaid
graph LR
    subgraph "模拟层"
        A1[工人模拟器]
        A2[车辆模拟器]
        A3[设备模拟器]
        A4[门禁模拟器]
        A5[物料模拟器]
    end
    
    subgraph "传感器层"
        B1[位置传感器]
        B2[身份传感器]
        B3[运动传感器]
        B4[噪声传感器]
        B5[扬尘传感器]
    end
    
    subgraph "环境层"
        C1[环境模拟器]
    end
    
    subgraph "数据层"
        D1[IoT系统]
        D2[MQTT发布]
    end
    
    A1 --> B1
    A1 --> B2
    A2 --> B1
    A2 --> B3
    A3 --> B3
    
    A1 --> C1
    A2 --> C1
    A3 --> C1
    
    C1 --> B4
    C1 --> B5
    
    B1 --> D1
    B2 --> D1
    B3 --> D1
    B4 --> D1
    B5 --> D1
    
    D1 --> D2
```

## 2. 核心业务模块流程

### 2.1 工人管理业务流程

#### 2.1.1 工人日常作业流程

```mermaid
sequenceDiagram
    participant W as 工人模拟器
    participant G as 门禁系统
    participant P as 位置传感器
    participant I as 身份传感器
    participant IoT as IoT系统
    participant S as 安全检查
    
    W->>G: 刷卡进入工地
    G->>I: 记录身份信息
    I->>IoT: 上报身份数据
    
    loop 工作日循环
        W->>W: 移动到工作位置
        W->>P: 更新位置信息
        P->>IoT: 上报位置数据
        
        W->>W: 执行工作任务
        W->>S: 随机安全检查
        
        alt 发现安全违规
            S->>IoT: 发送安全事件
        end
        
        W->>W: 等待下一个任务
    end
    
    W->>G: 刷卡离开工地
    G->>I: 记录离开信息
    I->>IoT: 上报离开数据
```

#### 2.1.2 安全违规检测流程

```mermaid
flowchart TD
    A[工人执行任务] --> B{安全检查触发}
    B -->|随机触发| C[检查安全装备]
    B -->|继续工作| A
    
    C --> D{安全帽检查}
    D -->|未佩戴| E[生成违规事件]
    D -->|已佩戴| F{防护服检查}
    
    F -->|未穿着| E
    F -->|已穿着| G{安全带检查}
    
    G -->|未佩戴| E
    G -->|已佩戴| H{行为检查}
    
    H -->|使用手机| E
    H -->|违规操作| E
    H -->|正常行为| I[继续工作]
    
    E --> J[记录违规详情]
    J --> K[发送IoT事件]
    K --> L[通知安全管理]
    
    I --> A
    L --> A
```

### 2.2 车辆管理业务流程

#### 2.2.1 车辆运输任务流程

```mermaid
sequenceDiagram
    participant V as 车辆模拟器
    participant G as 门禁系统
    participant P as 位置传感器
    participant M as 运动传感器
    participant Mat as 物料系统
    participant IoT as IoT系统
    
    V->>G: 车辆到达工地门口
    G->>G: 车牌识别验证
    G->>IoT: 记录进入信息
    
    V->>V: 移动到装载区
    V->>P: 更新位置信息
    P->>IoT: 上报位置数据
    
    V->>Mat: 开始装载物料
    Mat->>Mat: 更新库存状态
    Mat->>IoT: 上报物料变化
    
    V->>V: 运输到目标位置
    V->>M: 更新速度状态
    M->>IoT: 上报运动数据
    
    V->>Mat: 卸载物料
    Mat->>Mat: 更新目标库存
    Mat->>IoT: 上报物料变化
    
    V->>G: 车辆离开工地
    G->>IoT: 记录离开信息
```

#### 2.2.2 车辆状态监控流程

```mermaid
stateDiagram-v2
    [*] --> 等待任务
    等待任务 --> 前往工地 : 接收运输任务
    前往工地 --> 门禁验证 : 到达工地
    门禁验证 --> 进入工地 : 验证通过
    门禁验证 --> 等待任务 : 验证失败
    
    进入工地 --> 移动到装载区
    移动到装载区 --> 装载物料
    装载物料 --> 运输中 : 装载完成
    运输中 --> 卸载物料 : 到达目标
    卸载物料 --> 离开工地 : 卸载完成
    离开工地 --> 等待任务 : 任务完成
    
    note right of 运输中 : 持续监控位置和速度
    note right of 装载物料 : 更新物料库存
    note right of 卸载物料 : 更新目标库存
```

### 2.3 设备管理业务流程

#### 2.3.1 塔吊作业流程

```mermaid
flowchart TD
    A[塔吊启动] --> B[设备自检]
    B --> C{自检结果}
    C -->|通过| D[进入待机状态]
    C -->|失败| E[设备故障]
    
    D --> F{接收作业任务}
    F -->|有任务| G[移动到起吊位置]
    F -->|无任务| D
    
    G --> H[起吊物料]
    H --> I[旋转到目标位置]
    I --> J[下降放置物料]
    J --> K[返回待机位置]
    K --> D
    
    E --> L[发送故障事件]
    L --> M[等待维修]
    M --> B
    
    subgraph "数据采集"
        N[运动传感器]
        O[位置传感器]
        P[工作状态监控]
    end
    
    G --> N
    H --> N
    I --> N
    J --> N
    
    G --> O
    I --> O
    K --> O
    
    D --> P
    G --> P
    E --> P
```

### 2.4 环境监测业务流程

#### 2.4.1 环境影响传播模型

```mermaid
graph TD
    subgraph "噪声源"
        A1[工人作业]
        A2[车辆行驶]
        A3[设备运行]
    end
    
    subgraph "扬尘源"
        B1[车辆行驶]
        B2[物料装卸]
        B3[施工作业]
    end
    
    subgraph "环境计算"
        C1[噪声传播算法]
        C2[扬尘扩散算法]
    end
    
    subgraph "传感器采集"
        D1[噪声传感器]
        D2[扬尘传感器]
    end
    
    A1 --> C1
    A2 --> C1
    A3 --> C1
    
    B1 --> C2
    B2 --> C2
    B3 --> C2
    
    C1 --> D1
    C2 --> D2
    
    D1 --> E[IoT系统]
    D2 --> E
```

### 2.5 门禁管理业务流程

#### 2.5.1 人员出入管理流程

```mermaid
sequenceDiagram
    participant P as 人员
    participant G as 门禁设备
    participant I as 身份验证
    participant D as 数据库
    participant IoT as IoT系统
    participant S as 统计系统
    
    P->>G: 刷卡/扫码
    G->>I: 读取身份信息
    I->>D: 查询权限
    
    alt 权限验证通过
        D->>I: 返回验证成功
        I->>G: 开启门禁
        G->>P: 允许通过
        G->>IoT: 记录通过信息
        IoT->>S: 更新统计数据
    else 权限验证失败
        D->>I: 返回验证失败
        I->>G: 拒绝开启
        G->>P: 拒绝通过
        G->>IoT: 记录拒绝信息
    end
```

### 2.6 物料管理业务流程

#### 2.6.1 物料出入库流程

```mermaid
flowchart TD
    A[物料到达] --> B{入库还是出库}
    
    B -->|入库| C[扫描物料信息]
    B -->|出库| M[接收出库申请]
    
    C --> D[质量检验]
    D --> E{检验结果}
    E -->|合格| F[分配存储位置]
    E -->|不合格| G[拒绝入库]
    
    F --> H[更新库存数据]
    H --> I[生成入库单]
    I --> J[发送IoT事件]
    
    M --> N[验证申请权限]
    N --> O{权限验证}
    O -->|通过| P[确认库存数量]
    O -->|拒绝| Q[拒绝出库]
    
    P --> R{库存充足}
    R -->|充足| S[执行出库]
    R -->|不足| T[部分出库/等待]
    
    S --> U[更新库存数据]
    U --> V[生成出库单]
    V --> W[发送IoT事件]
    
    G --> X[记录拒绝原因]
    Q --> Y[记录拒绝原因]
    T --> Z[记录等待状态]
    
    J --> End[流程结束]
    W --> End
    X --> End
    Y --> End
    Z --> End
```

## 3. 异步业务流程协调

### 3.1 多模拟器协调机制

```mermaid
gantt
    title 工地日常作业时间线
    dateFormat HH:mm
    axisFormat %H:%M
    
    section 工人作业
    进入工地    :active, worker1, 08:00, 08:30
    施工作业    :worker2, after worker1, 16:00
    离开工地    :worker3, after worker2, 17:00
    
    section 车辆运输
    物料运输1   :vehicle1, 09:00, 11:00
    物料运输2   :vehicle2, 13:00, 15:00
    
    section 设备作业
    塔吊作业    :crane1, 08:30, 17:00
    升降机运行  :elevator1, 08:00, 17:30
    
    section 环境监测
    噪声监测    :noise1, 08:00, 18:00
    扬尘监测    :dust1, 08:00, 18:00
```

### 3.2 事件驱动业务流程

```mermaid
sequenceDiagram
    participant W as 工人模拟器
    participant V as 车辆模拟器
    participant E as 环境模拟器
    participant IoT as IoT系统
    participant Ext as 外部系统
    
    Note over W,Ext: 工地作业开始
    
    W->>W: 开始施工作业
    W->>E: 触发噪声事件
    E->>E: 计算噪声传播
    E->>IoT: 发送噪声数据
    
    V->>V: 车辆进入工地
    V->>E: 触发扬尘事件
    E->>E: 计算扬尘扩散
    E->>IoT: 发送扬尘数据
    
    par 并行数据处理
        IoT->>IoT: 处理噪声数据
    and
        IoT->>IoT: 处理扬尘数据
    and
        IoT->>IoT: 处理位置数据
    end
    
    IoT->>Ext: MQTT批量发布
    Ext->>Ext: 数据分析处理
```

## 4. 业务规则与约束

### 4.1 时间约束
- **工作时间**：08:00-18:00为正常工作时间
- **车辆限制**：夜间22:00-06:00禁止大型车辆进入
- **设备维护**：每日18:00-20:00为设备维护时间

### 4.2 安全约束
- **人员安全**：必须佩戴安全帽、防护服、安全带
- **车辆安全**：工地内限速15km/h
- **设备安全**：恶劣天气下停止高空作业

### 4.3 环境约束
- **噪声控制**：施工噪声不得超过85dB
- **扬尘控制**：PM2.5浓度不得超过75μg/m³
- **作业区域**：危险区域禁止无关人员进入

### 4.4 数据约束
- **数据频率**：传感器数据发送间隔1-5秒可配置
- **数据完整性**：关键事件必须确保数据传输成功
- **数据格式**：所有数据必须符合JSON格式规范

## 5. 扩展性设计

### 5.1 模块化扩展
- 各模拟器独立开发，支持热插拔
- 传感器组件可自由添加和配置
- 业务规则可通过配置文件调整

### 5.2 数据扩展
- 支持新增传感器类型
- 支持自定义数据格式
- 支持多种数据传输协议

### 5.3 功能扩展
- 支持新增工种和设备类型
- 支持复杂的多人协作场景
- 支持更精细的环境模拟算法
