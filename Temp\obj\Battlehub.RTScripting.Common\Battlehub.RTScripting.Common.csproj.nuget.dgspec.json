{"format": 1, "restore": {"f:\\LongProjects\\General Editor\\Battlehub.RTScripting.Common.csproj": {}}, "projects": {"f:\\LongProjects\\General Editor\\Battlehub.LoadImageAsync.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "f:\\LongProjects\\General Editor\\Battlehub.LoadImageAsync.csproj", "projectName": "Battlehub.LoadImageAsync", "projectPath": "f:\\LongProjects\\General Editor\\Battlehub.LoadImageAsync.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "f:\\LongProjects\\General Editor\\Temp\\obj\\Battlehub.LoadImageAsync\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\VS2022Tools\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "f:\\LongProjects\\General Editor\\Battlehub.RTEditor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "f:\\LongProjects\\General Editor\\Battlehub.RTEditor.csproj", "projectName": "Battlehub.RTEditor", "projectPath": "f:\\LongProjects\\General Editor\\Battlehub.RTEditor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "f:\\LongProjects\\General Editor\\Temp\\obj\\Battlehub.RTEditor\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\VS2022Tools\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"f:\\LongProjects\\General Editor\\Battlehub.LoadImageAsync.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.LoadImageAsync.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.Addressables.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.Addressables.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.Core.Runtime.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.Core.Runtime.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.Runtime.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.Runtime.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.ShaderUtil.Runtime.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.ShaderUtil.Runtime.csproj"}, "f:\\LongProjects\\General Editor\\HSVPicker.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\HSVPicker.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "f:\\LongProjects\\General Editor\\Battlehub.RTScripting.Common.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "f:\\LongProjects\\General Editor\\Battlehub.RTScripting.Common.csproj", "projectName": "Battlehub.RTScripting.Common", "projectPath": "f:\\LongProjects\\General Editor\\Battlehub.RTScripting.Common.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "f:\\LongProjects\\General Editor\\Temp\\obj\\Battlehub.RTScripting.Common\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\VS2022Tools\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"f:\\LongProjects\\General Editor\\Battlehub.RTEditor.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.RTEditor.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.Addressables.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "f:\\LongProjects\\General Editor\\Battlehub.Storage.Addressables.csproj", "projectName": "Battlehub.Storage.Addressables", "projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.Addressables.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "f:\\LongProjects\\General Editor\\Temp\\obj\\Battlehub.Storage.Addressables\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\VS2022Tools\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"f:\\LongProjects\\General Editor\\Battlehub.Storage.Core.Runtime.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.Core.Runtime.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.Core.Runtime.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "f:\\LongProjects\\General Editor\\Battlehub.Storage.Core.Runtime.csproj", "projectName": "Battlehub.Storage.Core.Runtime", "projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.Core.Runtime.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "f:\\LongProjects\\General Editor\\Temp\\obj\\Battlehub.Storage.Core.Runtime\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\VS2022Tools\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"f:\\LongProjects\\General Editor\\Battlehub.Storage.ShaderUtil.Runtime.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.ShaderUtil.Runtime.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.Runtime.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "f:\\LongProjects\\General Editor\\Battlehub.Storage.Runtime.csproj", "projectName": "Battlehub.Storage.Runtime", "projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.Runtime.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "f:\\LongProjects\\General Editor\\Temp\\obj\\Battlehub.Storage.Runtime\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\VS2022Tools\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"f:\\LongProjects\\General Editor\\Battlehub.Storage.Core.Runtime.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.Core.Runtime.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.ShaderUtil.Runtime.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.ShaderUtil.Runtime.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.ShaderUtil.Runtime.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "f:\\LongProjects\\General Editor\\Battlehub.Storage.ShaderUtil.Runtime.csproj", "projectName": "Battlehub.Storage.ShaderUtil.Runtime", "projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.ShaderUtil.Runtime.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "f:\\LongProjects\\General Editor\\Temp\\obj\\Battlehub.Storage.ShaderUtil.Runtime\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\VS2022Tools\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "f:\\LongProjects\\General Editor\\HSVPicker.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "f:\\LongProjects\\General Editor\\HSVPicker.csproj", "projectName": "HSVPicker", "projectPath": "f:\\LongProjects\\General Editor\\HSVPicker.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "f:\\LongProjects\\General Editor\\Temp\\obj\\HSVPicker\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\VS2022Tools\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}}}