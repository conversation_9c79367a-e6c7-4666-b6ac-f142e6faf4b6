using Cysharp.Threading.Tasks;
using System.Threading;
using System;

namespace Simulation.IoTSystem.Core
{
    /// <summary>
    /// IoT系统核心接口
    /// 定义了IoT系统的基本功能
    /// </summary>
    public interface IIoTSystem
    {
        /// <summary>
        /// 系统是否已初始化
        /// </summary>
        bool IsInitialized { get; }
        
        /// <summary>
        /// 系统是否正在运行
        /// </summary>
        bool IsRunning { get; }
        
        /// <summary>
        /// 初始化IoT系统
        /// </summary>
        UniTask<bool> InitializeAsync(CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 启动IoT系统
        /// </summary>
        UniTask<bool> StartAsync(CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 停止IoT系统
        /// </summary>
        UniTask StopAsync();
        
        /// <summary>
        /// 注册传感器
        /// </summary>
        bool RegisterSensor(string sensorId, ISensorComponent sensor);
        
        /// <summary>
        /// 注销传感器
        /// </summary>
        bool UnregisterSensor(string sensorId);
        
        /// <summary>
        /// 发送数据
        /// </summary>
        UniTask<bool> SendDataAsync(string topic, object data, CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 订阅数据
        /// </summary>
        bool Subscribe(string topic, Action<string, object> onDataReceived);
        
        /// <summary>
        /// 取消订阅
        /// </summary>
        bool Unsubscribe(string topic);
    }
    
    /// <summary>
    /// 传感器组件接口
    /// </summary>
    public interface ISensorComponent
    {
        /// <summary>
        /// 传感器ID
        /// </summary>
        string SensorId { get; }
        
        /// <summary>
        /// 设备ID
        /// </summary>
        string DeviceId { get; }
        
        /// <summary>
        /// 传感器类型
        /// </summary>
        string SensorType { get; }
        
        /// <summary>
        /// 是否正在运行
        /// </summary>
        bool IsRunning { get; }
        
        /// <summary>
        /// 启动传感器
        /// </summary>
        void StartSensor();
        
        /// <summary>
        /// 停止传感器
        /// </summary>
        void StopSensor();
        
        /// <summary>
        /// 数据生成事件
        /// </summary>
        event Action<string, object> OnDataGenerated;
        
        /// <summary>
        /// 传感器错误事件
        /// </summary>
        event Action<string> OnSensorError;
    }
    
    /// <summary>
    /// 数据传输接口
    /// </summary>
    public interface IDataTransmitter
    {
        /// <summary>
        /// 是否已连接
        /// </summary>
        bool IsConnected { get; }
        
        /// <summary>
        /// 连接
        /// </summary>
        UniTask<bool> ConnectAsync(CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 断开连接
        /// </summary>
        UniTask DisconnectAsync();
        
        /// <summary>
        /// 发送数据
        /// </summary>
        UniTask<bool> SendAsync(string topic, byte[] data, CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 订阅主题
        /// </summary>
        bool Subscribe(string topic, Action<string, byte[]> onMessageReceived);
        
        /// <summary>
        /// 取消订阅
        /// </summary>
        bool Unsubscribe(string topic);
        
        /// <summary>
        /// 连接状态变化事件
        /// </summary>
        event Action<bool> OnConnectionChanged;
        
        /// <summary>
        /// 错误事件
        /// </summary>
        event Action<string> OnError;
    }
    
    /// <summary>
    /// 数据处理器接口
    /// </summary>
    public interface IDataProcessor
    {
        /// <summary>
        /// 处理传感器数据
        /// </summary>
        UniTask<byte[]> ProcessSensorDataAsync(string sensorId, object data, CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 解析接收到的数据
        /// </summary>
        UniTask<object> ParseReceivedDataAsync(string topic, byte[] data, CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 验证数据格式
        /// </summary>
        bool ValidateData(object data);
    }
    
    /// <summary>
    /// 配置管理器接口
    /// </summary>
    public interface IConfigurationManager
    {
        /// <summary>
        /// 获取配置值
        /// </summary>
        T GetConfig<T>(string key, T defaultValue = default);
        
        /// <summary>
        /// 设置配置值
        /// </summary>
        void SetConfig<T>(string key, T value);
        
        /// <summary>
        /// 保存配置
        /// </summary>
        UniTask SaveConfigAsync();
        
        /// <summary>
        /// 加载配置
        /// </summary>
        UniTask LoadConfigAsync();
        
        /// <summary>
        /// 配置变化事件
        /// </summary>
        event Action<string, object> OnConfigChanged;
    }
}
