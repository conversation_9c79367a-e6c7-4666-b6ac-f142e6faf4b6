using UnityEngine;
using Simulation.IoTSystem;
using Simulation.Simulators;
using Simulation.Sensors;
using Simulation.Systems;

namespace Simulation.Testing
{
    /// <summary>
    /// 测试场景配置器
    /// 自动配置测试场景，创建必要的组件和连接
    /// </summary>
    public class TestSceneSetup : MonoBehaviour
    {
        [Header("自动配置")]
        [SerializeField] private bool autoSetupOnStart = true;
        [SerializeField] private bool createTestObjects = true;
        [SerializeField] private bool setupIoTSystem = true;
        [SerializeField] private bool setupDataFlowTest = true;
        
        [Header("测试对象配置")]
        [SerializeField] private int workerCount = 3;
        [SerializeField] private int vehicleCount = 2;
        [SerializeField] private int craneCount = 1;
        [SerializeField] private int elevatorCount = 1;
        
        [Header("传感器配置")]
        [SerializeField] private bool addPositionSensors = true;
        [SerializeField] private bool addIdentitySensors = true;
        [SerializeField] private bool addMotionSensors = true;
        [SerializeField] private bool addNoiseSensors = true;
        [SerializeField] private bool addDustSensors = true;
        
        [Header("调试配置")]
        [SerializeField] private bool enableDebugLog = true;
        
        private void Start()
        {
            if (autoSetupOnStart)
            {
                SetupTestScene();
            }
        }
        
        /// <summary>
        /// 配置测试场景
        /// </summary>
        [ContextMenu("Setup Test Scene")]
        public void SetupTestScene()
        {
            LogDebug("开始配置测试场景...");
            
            // 配置IoT系统
            if (setupIoTSystem)
            {
                SetupIoTSystem();
            }
            
            // 创建测试对象
            if (createTestObjects)
            {
                CreateTestObjects();
            }
            
            // 配置数据流测试
            if (setupDataFlowTest)
            {
                SetupDataFlowTest();
            }
            
            LogDebug("测试场景配置完成");
        }
        
        /// <summary>
        /// 配置IoT系统
        /// </summary>
        private void SetupIoTSystem()
        {
            // 查找或创建IoT系统
            var iotSystem = FindObjectOfType<IoTSystem.IoTSystem>();
            if (iotSystem == null)
            {
                var iotGameObject = new GameObject("IoT System");
                iotSystem = iotGameObject.AddComponent<IoTSystem.IoTSystem>();
                LogDebug("创建IoT系统");
            }
            
            // 查找或创建MQTT管理器
            var mqttManager = iotSystem.GetComponent<MQTTManager>();
            if (mqttManager == null)
            {
                mqttManager = iotSystem.gameObject.AddComponent<MQTTManager>();
                LogDebug("创建MQTT管理器");
            }
            
            // 查找或创建事件管理器
            var eventManager = iotSystem.GetComponent<EventManager>();
            if (eventManager == null)
            {
                eventManager = iotSystem.gameObject.AddComponent<EventManager>();
                LogDebug("创建事件管理器");
            }
        }
        
        /// <summary>
        /// 创建测试对象
        /// </summary>
        private void CreateTestObjects()
        {
            // 创建工人模拟器
            for (int i = 0; i < workerCount; i++)
            {
                CreateWorkerSimulator($"Worker_{i + 1}");
            }
            
            // 创建车辆模拟器
            for (int i = 0; i < vehicleCount; i++)
            {
                CreateVehicleSimulator($"Vehicle_{i + 1}");
            }
            
            // 创建塔吊模拟器
            for (int i = 0; i < craneCount; i++)
            {
                CreateCraneSimulator($"Crane_{i + 1}");
            }
            
            // 创建升降机模拟器
            for (int i = 0; i < elevatorCount; i++)
            {
                CreateElevatorSimulator($"Elevator_{i + 1}");
            }
            
            // 创建环境监测器
            CreateEnvironmentalMonitor();
            
            // 创建气象模拟器
            CreateWeatherSimulator();
            
            // 创建物料管理模拟器
            CreateMaterialManagementSimulator();
            
            // 创建门禁系统
            CreateAccessControlSystem();
        }
        
        /// <summary>
        /// 创建工人模拟器
        /// </summary>
        private void CreateWorkerSimulator(string workerId)
        {
            var workerObject = new GameObject($"Worker Simulator - {workerId}");
            workerObject.transform.position = GetRandomPosition();
            
            var workerSimulator = workerObject.AddComponent<WorkerSimulator>();
            
            // 添加传感器
            if (addPositionSensors)
            {
                var positionSensor = workerObject.AddComponent<PositionSensor>();
            }
            
            if (addIdentitySensors)
            {
                var identitySensor = workerObject.AddComponent<IdentitySensor>();
            }
            
            LogDebug($"创建工人模拟器: {workerId}");
        }
        
        /// <summary>
        /// 创建车辆模拟器
        /// </summary>
        private void CreateVehicleSimulator(string vehicleId)
        {
            var vehicleObject = new GameObject($"Vehicle Simulator - {vehicleId}");
            vehicleObject.transform.position = GetRandomPosition();
            
            var vehicleSimulator = vehicleObject.AddComponent<VehicleSimulator>();
            
            // 添加传感器
            if (addPositionSensors)
            {
                var positionSensor = vehicleObject.AddComponent<PositionSensor>();
            }
            
            if (addMotionSensors)
            {
                var motionSensor = vehicleObject.AddComponent<MotionSensor>();
            }
            
            if (addIdentitySensors)
            {
                var identitySensor = vehicleObject.AddComponent<IdentitySensor>();
            }
            
            LogDebug($"创建车辆模拟器: {vehicleId}");
        }
        
        /// <summary>
        /// 创建塔吊模拟器
        /// </summary>
        private void CreateCraneSimulator(string craneId)
        {
            var craneObject = new GameObject($"Crane Simulator - {craneId}");
            craneObject.transform.position = GetRandomPosition();
            
            var craneSimulator = craneObject.AddComponent<CraneSimulator>();
            
            // 添加传感器
            if (addPositionSensors)
            {
                var positionSensor = craneObject.AddComponent<PositionSensor>();
            }
            
            if (addNoiseSensors)
            {
                var noiseSensor = craneObject.AddComponent<NoiseSensor>();
            }
            
            LogDebug($"创建塔吊模拟器: {craneId}");
        }
        
        /// <summary>
        /// 创建升降机模拟器
        /// </summary>
        private void CreateElevatorSimulator(string elevatorId)
        {
            var elevatorObject = new GameObject($"Elevator Simulator - {elevatorId}");
            elevatorObject.transform.position = GetRandomPosition();
            
            var elevatorSimulator = elevatorObject.AddComponent<ElevatorSimulator>();
            
            // 添加传感器
            if (addPositionSensors)
            {
                var positionSensor = elevatorObject.AddComponent<PositionSensor>();
            }
            
            if (addNoiseSensors)
            {
                var noiseSensor = elevatorObject.AddComponent<NoiseSensor>();
            }
            
            LogDebug($"创建升降机模拟器: {elevatorId}");
        }
        
        /// <summary>
        /// 创建环境监测器
        /// </summary>
        private void CreateEnvironmentalMonitor()
        {
            var envObject = new GameObject("Environmental Monitor");
            envObject.transform.position = Vector3.zero;
            
            var envMonitor = envObject.AddComponent<EnvironmentalMonitorSimulator>();
            
            LogDebug("创建环境监测器");
        }
        
        /// <summary>
        /// 创建气象模拟器
        /// </summary>
        private void CreateWeatherSimulator()
        {
            var weatherObject = new GameObject("Weather Simulator");
            weatherObject.transform.position = Vector3.zero;
            
            var weatherSimulator = weatherObject.AddComponent<WeatherSimulator>();
            
            LogDebug("创建气象模拟器");
        }
        
        /// <summary>
        /// 创建物料管理模拟器
        /// </summary>
        private void CreateMaterialManagementSimulator()
        {
            var materialObject = new GameObject("Material Management Simulator");
            materialObject.transform.position = GetRandomPosition();
            
            var materialSimulator = materialObject.AddComponent<MaterialManagementSimulator>();
            
            LogDebug("创建物料管理模拟器");
        }
        
        /// <summary>
        /// 创建门禁系统
        /// </summary>
        private void CreateAccessControlSystem()
        {
            var accessObject = new GameObject("Access Control System");
            accessObject.transform.position = GetRandomPosition();
            
            var accessControl = accessObject.AddComponent<AccessControlSystem>();
            
            LogDebug("创建门禁系统");
        }
        
        /// <summary>
        /// 配置数据流测试
        /// </summary>
        private void SetupDataFlowTest()
        {
            var testObject = new GameObject("Data Flow Test Manager");
            var testManager = testObject.AddComponent<DataFlowTestManager>();
            
            LogDebug("创建数据流测试管理器");
        }
        
        /// <summary>
        /// 获取随机位置
        /// </summary>
        private Vector3 GetRandomPosition()
        {
            return new Vector3(
                Random.Range(-50f, 50f),
                0f,
                Random.Range(-50f, 50f)
            );
        }
        
        /// <summary>
        /// 清理测试场景
        /// </summary>
        [ContextMenu("Clear Test Scene")]
        public void ClearTestScene()
        {
            LogDebug("清理测试场景...");
            
            // 查找并删除所有模拟器
            var simulators = FindObjectsOfType<SimulatorBase>();
            foreach (var simulator in simulators)
            {
                if (Application.isPlaying)
                {
                    Destroy(simulator.gameObject);
                }
                else
                {
                    DestroyImmediate(simulator.gameObject);
                }
            }
            
            // 查找并删除测试管理器
            var testManager = FindObjectOfType<DataFlowTestManager>();
            if (testManager != null)
            {
                if (Application.isPlaying)
                {
                    Destroy(testManager.gameObject);
                }
                else
                {
                    DestroyImmediate(testManager.gameObject);
                }
            }
            
            LogDebug("测试场景清理完成");
        }
        
        /// <summary>
        /// 调试日志
        /// </summary>
        private void LogDebug(string message)
        {
            if (enableDebugLog)
            {
                Debug.Log($"[TestSceneSetup] {message}");
            }
        }
    }
}
