using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using Cysharp.Threading.Tasks;
using System.Threading;
using Simulation.IoTSystem;
using Simulation.Sensors;
using Simulation.Simulators;
using Simulation.Data;

namespace Simulation.Testing
{
    /// <summary>
    /// 数据流集成测试管理器
    /// 测试完整的数据流：模拟器→传感器→IoT系统→MQTT→外部系统
    /// 验证数据格式和传输稳定性
    /// </summary>
    public class DataFlowTestManager : MonoBehaviour
    {
        [Header("测试配置")]
        [SerializeField] private bool autoStartTest = false; // 自动开始测试
        [SerializeField] private float testDuration = 300f; // 测试持续时间（秒）
        [SerializeField] private float dataCollectionInterval = 5f; // 数据收集间隔
        [SerializeField] private bool enableDetailedLogging = true; // 启用详细日志
        [SerializeField] private bool enablePerformanceTest = true; // 启用性能测试
        
        [Header("测试目标")]
        [SerializeField] private bool testSensors = true; // 测试传感器
        [SerializeField] private bool testSimulators = true; // 测试模拟器
        [SerializeField] private bool testEvents = true; // 测试事件系统
        [SerializeField] private bool testMQTT = true; // 测试MQTT连接
        
        [Header("性能阈值")]
        [SerializeField] private float maxDataLatency = 1000f; // 最大数据延迟（毫秒）
        [SerializeField] private float minDataRate = 0.8f; // 最小数据传输成功率
        [SerializeField] private int maxMemoryUsage = 100; // 最大内存使用（MB）
        
        [Header("调试配置")]
        [SerializeField] private bool enableDebugLog = true;
        [SerializeField] private bool showTestResults = true;
        
        // 测试状态
        private bool isTestRunning = false;
        private DateTime testStartTime;
        private CancellationTokenSource testCancellationTokenSource;
        
        // 测试数据收集
        private List<TestDataPoint> collectedData = new List<TestDataPoint>();
        private List<TestEvent> collectedEvents = new List<TestEvent>();
        private TestStatistics testStatistics = new TestStatistics();
        
        // 系统组件引用
        private IoTSystem.IoTSystem iotSystem;
        private EventManager eventManager;
        private List<SensorBase> testSensors = new List<SensorBase>();
        private List<SimulatorBase> testSimulators = new List<SimulatorBase>();
        
        // 公共属性
        public bool IsTestRunning => isTestRunning;
        public TestStatistics Statistics => testStatistics;
        public int CollectedDataCount => collectedData.Count;
        public int CollectedEventCount => collectedEvents.Count;
        
        private void Start()
        {
            InitializeTestManager();
            
            if (autoStartTest)
            {
                StartDataFlowTest().Forget();
            }
        }
        
        /// <summary>
        /// 初始化测试管理器
        /// </summary>
        private void InitializeTestManager()
        {
            // 获取系统组件
            iotSystem = IoTSystem.IoTSystem.Instance;
            eventManager = EventManager.Instance;
            
            // 查找所有传感器
            if (testSensors)
            {
                testSensors.AddRange(FindObjectsOfType<SensorBase>());
                LogDebug($"找到 {testSensors.Count} 个传感器");
            }
            
            // 查找所有模拟器
            if (testSimulators)
            {
                testSimulators.AddRange(FindObjectsOfType<SimulatorBase>());
                LogDebug($"找到 {testSimulators.Count} 个模拟器");
            }
            
            LogDebug("数据流测试管理器初始化完成");
        }
        
        /// <summary>
        /// 开始数据流测试
        /// </summary>
        public async UniTaskVoid StartDataFlowTest()
        {
            if (isTestRunning)
            {
                LogDebug("测试已在运行中");
                return;
            }
            
            try
            {
                isTestRunning = true;
                testStartTime = DateTime.UtcNow;
                testCancellationTokenSource = new CancellationTokenSource();
                
                // 重置测试数据
                ResetTestData();
                
                LogDebug($"开始数据流集成测试，持续时间: {testDuration}秒");
                
                // 等待系统启动
                await WaitForSystemReady();
                
                // 启动测试任务
                var testTasks = new List<UniTask>();
                
                if (testSensors)
                    testTasks.Add(TestSensorDataFlow(testCancellationTokenSource.Token));
                
                if (testSimulators)
                    testTasks.Add(TestSimulatorDataFlow(testCancellationTokenSource.Token));
                
                if (testEvents)
                    testTasks.Add(TestEventSystem(testCancellationTokenSource.Token));
                
                if (testMQTT)
                    testTasks.Add(TestMQTTConnection(testCancellationTokenSource.Token));
                
                if (enablePerformanceTest)
                    testTasks.Add(TestPerformanceMetrics(testCancellationTokenSource.Token));
                
                // 启动数据收集
                testTasks.Add(CollectTestData(testCancellationTokenSource.Token));
                
                // 等待测试完成或超时
                await UniTask.WhenAny(
                    UniTask.WhenAll(testTasks),
                    UniTask.Delay(TimeSpan.FromSeconds(testDuration), cancellationToken: testCancellationTokenSource.Token)
                );
                
                // 生成测试报告
                GenerateTestReport();
                
                LogDebug("数据流集成测试完成");
            }
            catch (OperationCanceledException)
            {
                LogDebug("数据流测试被取消");
            }
            catch (Exception ex)
            {
                LogError($"数据流测试异常: {ex.Message}");
            }
            finally
            {
                isTestRunning = false;
                testCancellationTokenSource?.Dispose();
            }
        }
        
        /// <summary>
        /// 停止数据流测试
        /// </summary>
        public void StopDataFlowTest()
        {
            if (isTestRunning)
            {
                testCancellationTokenSource?.Cancel();
                LogDebug("数据流测试已停止");
            }
        }
        
        /// <summary>
        /// 等待系统准备就绪
        /// </summary>
        private async UniTask WaitForSystemReady()
        {
            int maxWaitTime = 30; // 最大等待30秒
            int waitTime = 0;
            
            while (waitTime < maxWaitTime)
            {
                if (iotSystem != null && iotSystem.IsRunning && iotSystem.IsConnected)
                {
                    LogDebug("系统已准备就绪");
                    return;
                }
                
                await UniTask.Delay(1000);
                waitTime++;
            }
            
            throw new TimeoutException("等待系统准备就绪超时");
        }
        
        /// <summary>
        /// 测试传感器数据流
        /// </summary>
        private async UniTask TestSensorDataFlow(CancellationToken cancellationToken)
        {
            LogDebug("开始测试传感器数据流");
            
            while (!cancellationToken.IsCancellationRequested)
            {
                foreach (var sensor in testSensors)
                {
                    if (sensor.IsRunning)
                    {
                        // 记录传感器状态
                        var dataPoint = new TestDataPoint
                        {
                            Timestamp = DateTime.UtcNow,
                            SourceType = "Sensor",
                            SourceId = sensor.SensorId,
                            DataType = "SensorData",
                            IsValid = true,
                            Latency = 0 // 传感器数据延迟通常很低
                        };
                        
                        collectedData.Add(dataPoint);
                        testStatistics.TotalSensorDataPoints++;
                    }
                }
                
                await UniTask.Delay(TimeSpan.FromSeconds(dataCollectionInterval), cancellationToken);
            }
        }
        
        /// <summary>
        /// 测试模拟器数据流
        /// </summary>
        private async UniTask TestSimulatorDataFlow(CancellationToken cancellationToken)
        {
            LogDebug("开始测试模拟器数据流");
            
            while (!cancellationToken.IsCancellationRequested)
            {
                foreach (var simulator in testSimulators)
                {
                    if (simulator.IsRunning)
                    {
                        // 记录模拟器状态
                        var dataPoint = new TestDataPoint
                        {
                            Timestamp = DateTime.UtcNow,
                            SourceType = "Simulator",
                            SourceId = simulator.SimulatorId,
                            DataType = "SimulatorData",
                            IsValid = true,
                            Latency = 0
                        };
                        
                        collectedData.Add(dataPoint);
                        testStatistics.TotalSimulatorDataPoints++;
                    }
                }
                
                await UniTask.Delay(TimeSpan.FromSeconds(dataCollectionInterval), cancellationToken);
            }
        }
        
        /// <summary>
        /// 测试事件系统
        /// </summary>
        private async UniTask TestEventSystem(CancellationToken cancellationToken)
        {
            LogDebug("开始测试事件系统");
            
            int testEventCount = 0;
            
            while (!cancellationToken.IsCancellationRequested)
            {
                // 发送测试事件
                string testEventType = EventTypes.TEST_EVENT;
                string testTitle = $"测试事件 #{testEventCount}";
                string testDescription = $"这是第 {testEventCount} 个测试事件";
                
                iotSystem?.SendIOTEvent("test_source", testEventType, testTitle, testDescription);
                
                var testEvent = new TestEvent
                {
                    Timestamp = DateTime.UtcNow,
                    EventType = testEventType,
                    Title = testTitle,
                    Description = testDescription,
                    IsProcessed = true
                };
                
                collectedEvents.Add(testEvent);
                testStatistics.TotalEventsGenerated++;
                testEventCount++;
                
                await UniTask.Delay(TimeSpan.FromSeconds(dataCollectionInterval * 2), cancellationToken);
            }
        }
        
        /// <summary>
        /// 测试MQTT连接
        /// </summary>
        private async UniTask TestMQTTConnection(CancellationToken cancellationToken)
        {
            LogDebug("开始测试MQTT连接");
            
            while (!cancellationToken.IsCancellationRequested)
            {
                if (iotSystem != null)
                {
                    bool isConnected = iotSystem.IsConnected;
                    testStatistics.MQTTConnectionChecks++;
                    
                    if (isConnected)
                    {
                        testStatistics.SuccessfulMQTTConnections++;
                    }
                    else
                    {
                        LogDebug("MQTT连接断开");
                    }
                }
                
                await UniTask.Delay(TimeSpan.FromSeconds(dataCollectionInterval), cancellationToken);
            }
        }
        
        /// <summary>
        /// 测试性能指标
        /// </summary>
        private async UniTask TestPerformanceMetrics(CancellationToken cancellationToken)
        {
            LogDebug("开始测试性能指标");
            
            while (!cancellationToken.IsCancellationRequested)
            {
                // 检查内存使用
                long memoryUsage = GC.GetTotalMemory(false) / (1024 * 1024); // MB
                testStatistics.CurrentMemoryUsage = memoryUsage;
                
                if (memoryUsage > testStatistics.PeakMemoryUsage)
                {
                    testStatistics.PeakMemoryUsage = memoryUsage;
                }
                
                // 检查数据处理延迟
                if (collectedData.Count > 0)
                {
                    var recentData = collectedData.TakeLast(10);
                    double avgLatency = recentData.Average(d => d.Latency);
                    testStatistics.AverageDataLatency = avgLatency;
                }
                
                await UniTask.Delay(TimeSpan.FromSeconds(dataCollectionInterval), cancellationToken);
            }
        }
        
        /// <summary>
        /// 收集测试数据
        /// </summary>
        private async UniTask CollectTestData(CancellationToken cancellationToken)
        {
            while (!cancellationToken.IsCancellationRequested)
            {
                // 更新统计信息
                testStatistics.TestDuration = (DateTime.UtcNow - testStartTime).TotalSeconds;
                testStatistics.TotalDataPoints = collectedData.Count;
                testStatistics.TotalEvents = collectedEvents.Count;
                
                // 计算数据传输成功率
                if (testStatistics.TotalDataPoints > 0)
                {
                    int validDataPoints = collectedData.Count(d => d.IsValid);
                    testStatistics.DataTransmissionSuccessRate = (double)validDataPoints / testStatistics.TotalDataPoints;
                }
                
                // 计算MQTT连接成功率
                if (testStatistics.MQTTConnectionChecks > 0)
                {
                    testStatistics.MQTTConnectionSuccessRate = (double)testStatistics.SuccessfulMQTTConnections / testStatistics.MQTTConnectionChecks;
                }
                
                await UniTask.Delay(TimeSpan.FromSeconds(1), cancellationToken);
            }
        }
        
        /// <summary>
        /// 重置测试数据
        /// </summary>
        private void ResetTestData()
        {
            collectedData.Clear();
            collectedEvents.Clear();
            testStatistics = new TestStatistics();
        }
        
        /// <summary>
        /// 生成测试报告
        /// </summary>
        private void GenerateTestReport()
        {
            if (!showTestResults) return;
            
            LogDebug("=== 数据流集成测试报告 ===");
            LogDebug($"测试持续时间: {testStatistics.TestDuration:F1} 秒");
            LogDebug($"总数据点: {testStatistics.TotalDataPoints}");
            LogDebug($"传感器数据点: {testStatistics.TotalSensorDataPoints}");
            LogDebug($"模拟器数据点: {testStatistics.TotalSimulatorDataPoints}");
            LogDebug($"总事件数: {testStatistics.TotalEvents}");
            LogDebug($"数据传输成功率: {testStatistics.DataTransmissionSuccessRate:P1}");
            LogDebug($"MQTT连接成功率: {testStatistics.MQTTConnectionSuccessRate:P1}");
            LogDebug($"平均数据延迟: {testStatistics.AverageDataLatency:F1} ms");
            LogDebug($"峰值内存使用: {testStatistics.PeakMemoryUsage} MB");
            
            // 检查性能阈值
            CheckPerformanceThresholds();
        }
        
        /// <summary>
        /// 检查性能阈值
        /// </summary>
        private void CheckPerformanceThresholds()
        {
            bool allTestsPassed = true;
            
            if (testStatistics.AverageDataLatency > maxDataLatency)
            {
                LogError($"数据延迟超过阈值: {testStatistics.AverageDataLatency:F1} ms > {maxDataLatency} ms");
                allTestsPassed = false;
            }
            
            if (testStatistics.DataTransmissionSuccessRate < minDataRate)
            {
                LogError($"数据传输成功率低于阈值: {testStatistics.DataTransmissionSuccessRate:P1} < {minDataRate:P1}");
                allTestsPassed = false;
            }
            
            if (testStatistics.PeakMemoryUsage > maxMemoryUsage)
            {
                LogError($"内存使用超过阈值: {testStatistics.PeakMemoryUsage} MB > {maxMemoryUsage} MB");
                allTestsPassed = false;
            }
            
            if (allTestsPassed)
            {
                LogDebug("✓ 所有性能测试通过");
            }
            else
            {
                LogError("✗ 部分性能测试未通过");
            }
        }
        
        /// <summary>
        /// 调试日志
        /// </summary>
        private void LogDebug(string message)
        {
            if (enableDebugLog)
            {
                Debug.Log($"[DataFlowTest] {message}");
            }
        }
        
        /// <summary>
        /// 错误日志
        /// </summary>
        private void LogError(string message)
        {
            Debug.LogError($"[DataFlowTest] {message}");
        }
    }
