namespace Simulation.IoTSystem
{
    /// <summary>
    /// 标准化事件类型定义
    /// 提供统一的事件类型常量，确保系统中事件类型的一致性
    /// </summary>
    public static class EventTypes
    {
        // 安全事件
        public const string SAFETY_VIOLATION = "safety_violation";
        public const string EXTREME_WEATHER_ALERT = "extreme_weather_alert";
        public const string QUALITY_ISSUE = "quality_issue";
        public const string EQUIPMENT_MALFUNCTION = "equipment_malfunction";
        public const string EMERGENCY_STOP = "emergency_stop";
        public const string ACCIDENT_REPORT = "accident_report";
        public const string SAFETY_INSPECTION = "safety_inspection";
        public const string HAZARD_DETECTED = "hazard_detected";
        
        // 操作事件
        public const string MATERIAL_DELIVERY = "material_delivery";
        public const string MATERIAL_CONSUMPTION = "material_consumption";
        public const string VEHICLE_ENTRY = "vehicle_entry";
        public const string VEHICLE_EXIT = "vehicle_exit";
        public const string WORKER_CHECKIN = "worker_checkin";
        public const string WORKER_CHECKOUT = "worker_checkout";
        public const string TASK_COMPLETION = "task_completion";
        public const string TASK_START = "task_start";
        public const string SHIFT_CHANGE = "shift_change";
        public const string BREAK_START = "break_start";
        public const string BREAK_END = "break_end";
        
        // 系统事件
        public const string SENSOR_START = "sensor_start";
        public const string SENSOR_STOP = "sensor_stop";
        public const string SIMULATOR_START = "simulator_start";
        public const string SIMULATOR_STOP = "simulator_stop";
        public const string CONNECTION_LOST = "connection_lost";
        public const string CONNECTION_RESTORED = "connection_restored";
        public const string SYSTEM_ERROR = "system_error";
        public const string SYSTEM_STARTUP = "system_startup";
        public const string SYSTEM_SHUTDOWN = "system_shutdown";
        public const string DATA_BACKUP = "data_backup";
        public const string CONFIGURATION_CHANGE = "configuration_change";
        
        // 警报事件
        public const string LOW_STOCK_ALERT = "low_stock_alert";
        public const string HIGH_STOCK_ALERT = "high_stock_alert";
        public const string NOISE_THRESHOLD_ALERT = "noise_threshold_alert";
        public const string DUST_THRESHOLD_ALERT = "dust_threshold_alert";
        public const string TEMPERATURE_ALERT = "temperature_alert";
        public const string MAINTENANCE_DUE = "maintenance_due";
        public const string BATTERY_LOW = "battery_low";
        public const string STORAGE_FULL = "storage_full";
        public const string NETWORK_LATENCY = "network_latency";
        
        // 设备事件
        public const string CRANE_OPERATION = "crane_operation";
        public const string ELEVATOR_OPERATION = "elevator_operation";
        public const string EQUIPMENT_STATUS_CHANGE = "equipment_status_change";
        public const string EQUIPMENT_MAINTENANCE = "equipment_maintenance";
        public const string EQUIPMENT_CALIBRATION = "equipment_calibration";
        
        // 环境事件
        public const string WEATHER_CHANGE = "weather_change";
        public const string NOISE_LEVEL_CHANGE = "noise_level_change";
        public const string DUST_LEVEL_CHANGE = "dust_level_change";
        public const string ENVIRONMENTAL_ALERT = "environmental_alert";
        
        // 物料事件
        public const string AUTO_REPLENISHMENT = "auto_replenishment";
        public const string MATERIAL_EXPIRATION = "material_expiration";
        public const string INVENTORY_UPDATE = "inventory_update";
        public const string MATERIAL_WASTE = "material_waste";
        
        // 门禁事件
        public const string ACCESS_GRANTED = "access_granted";
        public const string ACCESS_DENIED = "access_denied";
        public const string UNAUTHORIZED_ACCESS = "unauthorized_access";
        public const string CARD_SCAN = "card_scan";
        
        // 调试和测试事件
        public const string DEBUG_EVENT = "debug_event";
        public const string TEST_EVENT = "test_event";
        public const string PERFORMANCE_METRIC = "performance_metric";
        
        /// <summary>
        /// 获取所有安全事件类型
        /// </summary>
        public static string[] GetSafetyEventTypes()
        {
            return new string[]
            {
                SAFETY_VIOLATION, EXTREME_WEATHER_ALERT, QUALITY_ISSUE,
                EQUIPMENT_MALFUNCTION, EMERGENCY_STOP, ACCIDENT_REPORT,
                SAFETY_INSPECTION, HAZARD_DETECTED
            };
        }
        
        /// <summary>
        /// 获取所有操作事件类型
        /// </summary>
        public static string[] GetOperationalEventTypes()
        {
            return new string[]
            {
                MATERIAL_DELIVERY, MATERIAL_CONSUMPTION, VEHICLE_ENTRY,
                VEHICLE_EXIT, WORKER_CHECKIN, WORKER_CHECKOUT,
                TASK_COMPLETION, TASK_START, SHIFT_CHANGE,
                BREAK_START, BREAK_END
            };
        }
        
        /// <summary>
        /// 获取所有系统事件类型
        /// </summary>
        public static string[] GetSystemEventTypes()
        {
            return new string[]
            {
                SENSOR_START, SENSOR_STOP, SIMULATOR_START, SIMULATOR_STOP,
                CONNECTION_LOST, CONNECTION_RESTORED, SYSTEM_ERROR,
                SYSTEM_STARTUP, SYSTEM_SHUTDOWN, DATA_BACKUP,
                CONFIGURATION_CHANGE
            };
        }
        
        /// <summary>
        /// 获取所有警报事件类型
        /// </summary>
        public static string[] GetAlertEventTypes()
        {
            return new string[]
            {
                LOW_STOCK_ALERT, HIGH_STOCK_ALERT, NOISE_THRESHOLD_ALERT,
                DUST_THRESHOLD_ALERT, TEMPERATURE_ALERT, MAINTENANCE_DUE,
                BATTERY_LOW, STORAGE_FULL, NETWORK_LATENCY
            };
        }
        
        /// <summary>
        /// 获取所有优先事件类型
        /// </summary>
        public static string[] GetPriorityEventTypes()
        {
            return new string[]
            {
                SAFETY_VIOLATION, EMERGENCY_STOP, EXTREME_WEATHER_ALERT,
                ACCIDENT_REPORT, EQUIPMENT_MALFUNCTION, HAZARD_DETECTED
            };
        }
        
        /// <summary>
        /// 检查是否为安全事件
        /// </summary>
        public static bool IsSafetyEvent(string eventType)
        {
            var safetyEvents = GetSafetyEventTypes();
            return System.Array.IndexOf(safetyEvents, eventType) >= 0;
        }
        
        /// <summary>
        /// 检查是否为优先事件
        /// </summary>
        public static bool IsPriorityEvent(string eventType)
        {
            var priorityEvents = GetPriorityEventTypes();
            return System.Array.IndexOf(priorityEvents, eventType) >= 0;
        }
        
        /// <summary>
        /// 获取事件类型的显示名称
        /// </summary>
        public static string GetEventDisplayName(string eventType)
        {
            return eventType switch
            {
                SAFETY_VIOLATION => "安全违规",
                EXTREME_WEATHER_ALERT => "极端天气预警",
                QUALITY_ISSUE => "质量问题",
                EQUIPMENT_MALFUNCTION => "设备故障",
                EMERGENCY_STOP => "紧急停止",
                ACCIDENT_REPORT => "事故报告",
                MATERIAL_DELIVERY => "物料配送",
                MATERIAL_CONSUMPTION => "物料消耗",
                VEHICLE_ENTRY => "车辆进入",
                VEHICLE_EXIT => "车辆离开",
                WORKER_CHECKIN => "工人签到",
                WORKER_CHECKOUT => "工人签退",
                TASK_COMPLETION => "任务完成",
                LOW_STOCK_ALERT => "低库存预警",
                HIGH_STOCK_ALERT => "高库存提醒",
                NOISE_THRESHOLD_ALERT => "噪声超标",
                DUST_THRESHOLD_ALERT => "扬尘超标",
                TEMPERATURE_ALERT => "温度异常",
                SYSTEM_ERROR => "系统错误",
                CONNECTION_LOST => "连接丢失",
                CONNECTION_RESTORED => "连接恢复",
                _ => eventType
            };
        }
    }
}
