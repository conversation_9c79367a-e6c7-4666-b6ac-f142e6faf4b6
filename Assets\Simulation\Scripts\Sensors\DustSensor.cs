using System;
using System.Threading;
using Cysharp.Threading.Tasks;
using UnityEngine;
using Simulation.Data;
using Simulation.Simulators;

namespace Simulation.Sensors
{
    /// <summary>
    /// 扬尘传感器
    /// 监测PM2.5、PM10等扬尘浓度，从环境监测模拟器获取数据
    /// </summary>
    public class DustSensor : SensorBase
    {
        [Header("扬尘传感器配置")]
        [SerializeField] private string dustUnit = "μg/m³"; // 扬尘单位
        [SerializeField] private float minDustLevel = 0f; // 最小扬尘浓度
        [SerializeField] private float maxDustLevel = 500f; // 最大扬尘浓度
        [SerializeField] private float pm25Threshold = 75f; // PM2.5阈值
        [SerializeField] private float pm10Threshold = 150f; // PM10阈值
        [SerializeField] private bool enableThresholdAlert = true; // 启用阈值报警
        
        [Header("监测类型配置")]
        [SerializeField] private bool enablePM25 = true; // 启用PM2.5监测
        [SerializeField] private bool enablePM10 = true; // 启用PM10监测
        [SerializeField] private bool enableTSP = false; // 启用总悬浮颗粒物监测
        [SerializeField] private float pm25Ratio = 0.6f; // PM2.5占总扬尘比例
        [SerializeField] private float pm10Ratio = 0.8f; // PM10占总扬尘比例
        
        [Header("数据源配置")]
        [SerializeField] private EnvironmentalMonitorSimulator environmentalMonitor; // 环境监测器引用
        [SerializeField] private bool autoFindMonitor = true; // 自动查找监测器
        [SerializeField] private float detectionRadius = 50f; // 检测半径
        
        [Header("数据处理配置")]
        [SerializeField] private bool enableSmoothing = true; // 启用数据平滑
        [SerializeField] private float smoothingFactor = 0.2f; // 平滑因子
        [SerializeField] private bool enableDustVariation = true; // 启用扬尘变化
        [SerializeField] private float variationRange = 5f; // 变化范围
        
        [Header("校准配置")]
        [SerializeField] private bool enableCalibration = true; // 启用校准
        [SerializeField] private float calibrationOffset = 0f; // 校准偏移
        [SerializeField] private float calibrationMultiplier = 1f; // 校准倍数
        
        [Header("调试配置")]
        [SerializeField] private bool showDebugInfo = true; // 显示调试信息
        [SerializeField] private bool logThresholdExceeds = true; // 记录超阈值事件
        
        // 传感器状态
        private float currentDustLevel = 0f;
        private float currentPM25Level = 0f;
        private float currentPM10Level = 0f;
        private float currentTSPLevel = 0f;
        private float smoothedDustLevel = 0f;
        private DateTime lastThresholdExceedTime = DateTime.MinValue;
        private bool isPM25Exceeded = false;
        private bool isPM10Exceeded = false;
        
        // 统计数据
        private float maxRecordedDust = 0f;
        private float minRecordedDust = float.MaxValue;
        private float averageDustLevel = 0f;
        private int totalMeasurements = 0;
        private int pm25ExceedCount = 0;
        private int pm10ExceedCount = 0;
        
        // 公共属性
        public override string SensorType => "DustSensor";
        public float CurrentDustLevel => currentDustLevel;
        public float CurrentPM25Level => currentPM25Level;
        public float CurrentPM10Level => currentPM10Level;
        public float CurrentTSPLevel => currentTSPLevel;
        public string DustUnit => dustUnit;
        public bool IsPM25Exceeded => isPM25Exceeded;
        public bool IsPM10Exceeded => isPM10Exceeded;
        public float MaxRecordedDust => maxRecordedDust;
        public float MinRecordedDust => minRecordedDust;
        public float AverageDustLevel => averageDustLevel;
        public int PM25ExceedCount => pm25ExceedCount;
        public int PM10ExceedCount => pm10ExceedCount;
        
        protected override void Awake()
        {
            base.Awake();
            
            // 自动查找环境监测器
            if (autoFindMonitor && environmentalMonitor == null)
            {
                FindEnvironmentalMonitor();
            }
        }
        
        protected override void OnSensorStarted()
        {
            LogDebug("扬尘传感器启动");
            
            // 验证配置
            if (!ValidateDustConfiguration())
            {
                LogError("扬尘传感器配置验证失败");
                return;
            }
            
            // 初始化数据
            ResetStatistics();
        }
        
        protected override void OnSensorStopped()
        {
            LogDebug($"扬尘传感器停止 - 总测量次数: {totalMeasurements}, PM2.5超标: {pm25ExceedCount}次, PM10超标: {pm10ExceedCount}次");
        }
        
        protected override async UniTask<object> GenerateSensorDataAsync(CancellationToken cancellationToken)
        {
            try
            {
                // 获取原始扬尘数据
                float rawDustLevel = GetRawDustLevel();
                
                // 应用校准
                if (enableCalibration)
                {
                    rawDustLevel = ApplyCalibration(rawDustLevel);
                }
                
                // 添加随机变化
                if (enableDustVariation)
                {
                    rawDustLevel += UnityEngine.Random.Range(-variationRange, variationRange);
                }
                
                // 限制范围
                rawDustLevel = Mathf.Clamp(rawDustLevel, minDustLevel, maxDustLevel);
                
                // 应用平滑
                if (enableSmoothing)
                {
                    smoothedDustLevel = ApplySmoothing(rawDustLevel);
                    currentDustLevel = smoothedDustLevel;
                }
                else
                {
                    currentDustLevel = rawDustLevel;
                }
                
                // 计算各类颗粒物浓度
                CalculateParticleConcentrations(currentDustLevel);
                
                // 更新统计
                UpdateStatistics(currentDustLevel);
                
                // 检查阈值
                CheckThresholds();
                
                // 创建传感器数据
                var dustData = new DustSensorData
                {
                    timestamp = DateTime.UtcNow,
                    sensorId = sensorId,
                    deviceId = deviceId,
                    totalDustLevel = currentDustLevel,
                    pm25Level = enablePM25 ? currentPM25Level : 0f,
                    pm10Level = enablePM10 ? currentPM10Level : 0f,
                    tspLevel = enableTSP ? currentTSPLevel : 0f,
                    unit = dustUnit,
                    isPM25Exceeded = isPM25Exceeded,
                    isPM10Exceeded = isPM10Exceeded,
                    pm25Threshold = pm25Threshold,
                    pm10Threshold = pm10Threshold,
                    location = new LocationData
                    {
                        x = transform.position.x,
                        y = transform.position.y,
                        z = transform.position.z
                    }
                };
                
                LogDebug($"扬尘数据: 总扬尘 {currentDustLevel:F1}{dustUnit}, PM2.5 {currentPM25Level:F1}{dustUnit}, PM10 {currentPM10Level:F1}{dustUnit}");
                
                return dustData;
            }
            catch (Exception ex)
            {
                LogError($"生成扬尘数据异常: {ex.Message}");
                return null;
            }
        }
        
        /// <summary>
        /// 获取原始扬尘等级
        /// </summary>
        private float GetRawDustLevel()
        {
            // 优先从环境监测器获取数据
            if (environmentalMonitor != null && environmentalMonitor.isActiveAndEnabled)
            {
                return environmentalMonitor.CurrentDustLevel;
            }
            
            // 如果没有环境监测器，生成模拟数据
            return GenerateSimulatedDustLevel();
        }
        
        /// <summary>
        /// 生成模拟扬尘等级
        /// </summary>
        private float GenerateSimulatedDustLevel()
        {
            // 基础扬尘浓度
            float baseDust = 30f;
            
            // 时间变化（模拟一天中的扬尘变化）
            float timeOfDay = (Time.time % 86400f) / 86400f; // 0-1之间
            float timeVariation = Mathf.Sin(timeOfDay * Mathf.PI * 2f) * 10f;
            
            // 天气影响（模拟风力影响）
            float weatherVariation = Mathf.PerlinNoise(Time.time * 0.01f, 0f) * 15f;
            
            // 随机变化
            float randomVariation = UnityEngine.Random.Range(-5f, 10f);
            
            // 检测附近的扬尘源
            float sourceInfluence = DetectNearbyDustSources();
            
            return baseDust + timeVariation + weatherVariation + randomVariation + sourceInfluence;
        }
        
        /// <summary>
        /// 检测附近的扬尘源
        /// </summary>
        private float DetectNearbyDustSources()
        {
            float totalInfluence = 0f;
            
            Collider[] nearbyObjects = Physics.OverlapSphere(transform.position, detectionRadius);
            
            foreach (var collider in nearbyObjects)
            {
                float influence = 0f;
                float distance = Vector3.Distance(transform.position, collider.transform.position);
                float distanceFactor = Mathf.Max(0f, 1f - distance / detectionRadius);
                
                // 检查车辆（主要扬尘源）
                var vehicleSimulator = collider.GetComponent<VehicleSimulator>();
                if (vehicleSimulator != null && vehicleSimulator.CurrentSpeed > 0.1f)
                {
                    influence = 20f * distanceFactor;
                }
                
                // 检查塔吊
                var craneSimulator = collider.GetComponent<CraneSimulator>();
                if (craneSimulator != null && craneSimulator.CurrentState != CraneState.Idle)
                {
                    influence = 15f * distanceFactor;
                }
                
                // 检查升降机
                var elevatorSimulator = collider.GetComponent<ElevatorSimulator>();
                if (elevatorSimulator != null && elevatorSimulator.CurrentState != ElevatorState.Idle)
                {
                    influence = 8f * distanceFactor;
                }
                
                totalInfluence += influence;
            }
            
            return totalInfluence;
        }
        
        /// <summary>
        /// 计算各类颗粒物浓度
        /// </summary>
        private void CalculateParticleConcentrations(float totalDust)
        {
            if (enablePM25)
            {
                currentPM25Level = totalDust * pm25Ratio;
            }
            
            if (enablePM10)
            {
                currentPM10Level = totalDust * pm10Ratio;
            }
            
            if (enableTSP)
            {
                currentTSPLevel = totalDust;
            }
        }
        
        /// <summary>
        /// 应用校准
        /// </summary>
        private float ApplyCalibration(float rawValue)
        {
            return (rawValue * calibrationMultiplier) + calibrationOffset;
        }
        
        /// <summary>
        /// 应用平滑
        /// </summary>
        private float ApplySmoothing(float newValue)
        {
            if (totalMeasurements == 0)
            {
                return newValue;
            }
            
            return Mathf.Lerp(smoothedDustLevel, newValue, smoothingFactor);
        }
        
        /// <summary>
        /// 更新统计数据
        /// </summary>
        private void UpdateStatistics(float dustLevel)
        {
            totalMeasurements++;
            
            maxRecordedDust = Mathf.Max(maxRecordedDust, dustLevel);
            minRecordedDust = Mathf.Min(minRecordedDust, dustLevel);
            
            // 计算平均值
            averageDustLevel = ((averageDustLevel * (totalMeasurements - 1)) + dustLevel) / totalMeasurements;
        }
        
        /// <summary>
        /// 检查阈值
        /// </summary>
        private void CheckThresholds()
        {
            bool wasPM25Exceeded = isPM25Exceeded;
            bool wasPM10Exceeded = isPM10Exceeded;
            
            isPM25Exceeded = enablePM25 && currentPM25Level > pm25Threshold;
            isPM10Exceeded = enablePM10 && currentPM10Level > pm10Threshold;
            
            // PM2.5超标检查
            if (isPM25Exceeded && !wasPM25Exceeded)
            {
                pm25ExceedCount++;
                lastThresholdExceedTime = DateTime.UtcNow;
                
                if (logThresholdExceeds)
                {
                    LogDebug($"PM2.5超过阈值: {currentPM25Level:F1}{dustUnit} > {pm25Threshold}{dustUnit}");
                }
                
                if (enableThresholdAlert)
                {
                    TriggerThresholdAlert("PM2.5", currentPM25Level, pm25Threshold);
                }
            }
            
            // PM10超标检查
            if (isPM10Exceeded && !wasPM10Exceeded)
            {
                pm10ExceedCount++;
                lastThresholdExceedTime = DateTime.UtcNow;
                
                if (logThresholdExceeds)
                {
                    LogDebug($"PM10超过阈值: {currentPM10Level:F1}{dustUnit} > {pm10Threshold}{dustUnit}");
                }
                
                if (enableThresholdAlert)
                {
                    TriggerThresholdAlert("PM10", currentPM10Level, pm10Threshold);
                }
            }
        }
        
        /// <summary>
        /// 触发阈值报警
        /// </summary>
        private void TriggerThresholdAlert(string particleType, float currentValue, float threshold)
        {
            // 发送阈值超标事件到IoT系统
            var alertData = new
            {
                sensorId = sensorId,
                sensorType = SensorType,
                alertType = "threshold_exceeded",
                particleType = particleType,
                currentValue = currentValue,
                threshold = threshold,
                unit = dustUnit,
                timestamp = DateTime.UtcNow,
                location = new { x = transform.position.x, y = transform.position.y, z = transform.position.z }
            };
            
            IoTSystem.IoTSystem.Instance?.SendIOTEvent(
                sensorId, 
                "dust_threshold_alert", 
                "扬尘阈值报警", 
                $"扬尘传感器 {sensorId} 检测到{particleType} {currentValue:F1}{dustUnit} 超过阈值 {threshold}{dustUnit}",
                alertData
            );
        }
        
        /// <summary>
        /// 查找环境监测器
        /// </summary>
        private void FindEnvironmentalMonitor()
        {
            // 在检测半径内查找环境监测器
            Collider[] colliders = Physics.OverlapSphere(transform.position, detectionRadius);
            
            foreach (var collider in colliders)
            {
                var monitor = collider.GetComponent<EnvironmentalMonitorSimulator>();
                if (monitor != null)
                {
                    environmentalMonitor = monitor;
                    LogDebug($"找到环境监测器: {monitor.StationId}");
                    break;
                }
            }
            
            // 如果没找到，尝试在场景中查找
            if (environmentalMonitor == null)
            {
                environmentalMonitor = FindObjectOfType<EnvironmentalMonitorSimulator>();
                if (environmentalMonitor != null)
                {
                    LogDebug($"在场景中找到环境监测器: {environmentalMonitor.StationId}");
                }
            }
        }
        
        /// <summary>
        /// 验证扬尘配置
        /// </summary>
        private bool ValidateDustConfiguration()
        {
            if (minDustLevel >= maxDustLevel)
            {
                LogError("最小扬尘浓度必须小于最大扬尘浓度");
                return false;
            }
            
            if (pm25Threshold < minDustLevel || pm25Threshold > maxDustLevel)
            {
                LogError("PM2.5阈值必须在最小和最大扬尘浓度之间");
                return false;
            }
            
            if (pm10Threshold < minDustLevel || pm10Threshold > maxDustLevel)
            {
                LogError("PM10阈值必须在最小和最大扬尘浓度之间");
                return false;
            }
            
            if (pm25Ratio < 0f || pm25Ratio > 1f)
            {
                LogError("PM2.5比例必须在0-1之间");
                return false;
            }
            
            if (pm10Ratio < 0f || pm10Ratio > 1f)
            {
                LogError("PM10比例必须在0-1之间");
                return false;
            }
            
            if (smoothingFactor < 0f || smoothingFactor > 1f)
            {
                LogError("平滑因子必须在0-1之间");
                return false;
            }
            
            return true;
        }
        
        /// <summary>
        /// 重置统计数据
        /// </summary>
        private void ResetStatistics()
        {
            maxRecordedDust = 0f;
            minRecordedDust = float.MaxValue;
            averageDustLevel = 0f;
            totalMeasurements = 0;
            pm25ExceedCount = 0;
            pm10ExceedCount = 0;
            smoothedDustLevel = 0f;
        }
        
        /// <summary>
        /// 调试日志
        /// </summary>
        private void LogDebug(string message)
        {
            if (enableDebugLog && showDebugInfo)
            {
                Debug.Log($"[DustSensor:{sensorId}] {message}");
            }
        }
        
        /// <summary>
        /// 错误日志
        /// </summary>
        private void LogError(string message)
        {
            Debug.LogError($"[DustSensor:{sensorId}] {message}");
        }
        
        /// <summary>
        /// 绘制调试信息
        /// </summary>
        private void OnDrawGizmos()
        {
            if (!showDebugInfo) return;
            
            // 绘制检测范围
            Gizmos.color = Color.cyan;
            Gizmos.DrawWireSphere(transform.position, detectionRadius);
            
            // 绘制传感器
            Color sensorColor = (isPM25Exceeded || isPM10Exceeded) ? Color.red : Color.green;
            Gizmos.color = sensorColor;
            Gizmos.DrawWireCube(transform.position, Vector3.one);
            
            // 绘制到环境监测器的连线
            if (environmentalMonitor != null)
            {
                Gizmos.color = Color.blue;
                Gizmos.DrawLine(transform.position, environmentalMonitor.transform.position);
            }
        }
    }
    
    /// <summary>
    /// 扬尘传感器数据结构
    /// </summary>
    [Serializable]
    public class DustSensorData
    {
        public DateTime timestamp;
        public string sensorId;
        public string deviceId;
        public float totalDustLevel;
        public float pm25Level;
        public float pm10Level;
        public float tspLevel;
        public string unit;
        public bool isPM25Exceeded;
        public bool isPM10Exceeded;
        public float pm25Threshold;
        public float pm10Threshold;
        public LocationData location;
    }
}
