using System.Threading;
using Cysharp.Threading.Tasks;
using UnityEngine;
using Simulation.Data;
using Simulation.Simulators;

namespace Simulation.Sensors
{
    /// <summary>
    /// 身份识别传感器
    /// 负责采集人员和车辆的身份信息
    /// </summary>
    public class IdentitySensor : SensorBase
    {
        [Header("身份信息配置")]
        [SerializeField] private string targetName = "张三";
        [SerializeField] private string role = "普通工人";
        [SerializeField] private string department = "施工部";
        [SerializeField] private string cardId = "CARD001";
        [SerializeField] private string targetType = "person"; // person, vehicle
        [SerializeField] private string status = "active";
        
        [Header("人员信息配置")]
        [SerializeField] private string employeeId = "EMP001";
        [SerializeField] private string phoneNumber = "";
        [SerializeField] private string emergencyContact = "";
        [SerializeField] private string[] certifications;
        [SerializeField] private string workGroup = "A组";
        
        [Header("车辆信息配置")]
        [SerializeField] private string vehicleType = ""; // truck, crane, excavator
        [SerializeField] private string licensePlate = "";
        [SerializeField] private string driverName = "";
        [SerializeField] private string driverId = "";
        [SerializeField] private float vehicleWeight = 0f;
        [SerializeField] private string vehicleModel = "";
        
        [Header("检测配置")]
        [SerializeField] private bool continuousMode = false; // 连续模式或触发模式
        [SerializeField] private float detectionRange = 2f; // 检测范围（米）
        [SerializeField] private LayerMask detectionLayers = -1; // 检测层
        [SerializeField] private bool requireLineOfSight = false; // 是否需要视线
        
        [Header("数据更新配置")]
        [SerializeField] private bool onlyReportChanges = true; // 只报告变化
        [SerializeField] private float statusCheckInterval = 5f; // 状态检查间隔（秒）
        
        // 检测状态
        private bool isTargetDetected = false;
        private float lastDetectionTime = 0f;
        private string lastReportedStatus = "";
        
        // 传感器类型
        public override string SensorType => "identity";
        
        protected override void OnSensorStarted()
        {
            base.OnSensorStarted();
            
            // 初始化状态
            lastReportedStatus = status;
            lastDetectionTime = Time.time;
            
            LogDebug($"身份识别传感器启动 - 目标: {targetType}:{targetName}");
            
            // 如果是连续模式，启动检测循环
            if (continuousMode)
            {
                DetectionLoop().Forget();
            }
        }
        
        protected override async UniTask<object> GenerateSensorDataAsync(CancellationToken cancellationToken)
        {
            // 在连续模式下，检查是否有目标在范围内
            if (continuousMode)
            {
                UpdateDetectionStatus();
                
                // 如果没有检测到目标且只报告变化，则不发送数据
                if (!isTargetDetected && onlyReportChanges)
                {
                    return null;
                }
            }
            
            // 检查状态是否有变化
            if (onlyReportChanges && status == lastReportedStatus && !IsFirstSample())
            {
                return null;
            }
            
            // 创建身份数据
            var identityData = new IdentityData(sensorId, deviceId);
            
            // 设置基础身份信息
            identityData.name = targetName;
            identityData.role = role;
            identityData.department = department;
            identityData.cardId = cardId;
            identityData.targetType = targetType;
            identityData.status = status;
            
            // 更新最后报告的状态
            lastReportedStatus = status;
            
            return identityData;
        }
        
        /// <summary>
        /// 检测循环（连续模式）
        /// </summary>
        private async UniTaskVoid DetectionLoop()
        {
            try
            {
                while (isRunning)
                {
                    UpdateDetectionStatus();
                    await UniTask.Delay(System.TimeSpan.FromSeconds(statusCheckInterval));
                }
            }
            catch (System.Exception ex)
            {
                LogError($"检测循环异常: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 更新检测状态
        /// </summary>
        private void UpdateDetectionStatus()
        {
            bool previousDetection = isTargetDetected;
            isTargetDetected = false;
            
            // 在检测范围内查找目标
            Collider[] colliders = Physics.OverlapSphere(transform.position, detectionRange, detectionLayers);
            
            foreach (var collider in colliders)
            {
                // 检查是否是目标对象
                if (IsValidTarget(collider))
                {
                    // 如果需要视线检查
                    if (requireLineOfSight)
                    {
                        if (HasLineOfSight(collider.transform.position))
                        {
                            isTargetDetected = true;
                            break;
                        }
                    }
                    else
                    {
                        isTargetDetected = true;
                        break;
                    }
                }
            }
            
            // 如果检测状态发生变化，更新状态
            if (isTargetDetected != previousDetection)
            {
                status = isTargetDetected ? "detected" : "not_detected";
                lastDetectionTime = Time.time;
                
                LogDebug($"检测状态变化: {previousDetection} -> {isTargetDetected}");
            }
        }
        
        /// <summary>
        /// 检查是否是有效目标
        /// </summary>
        private bool IsValidTarget(Collider collider)
        {
            // 这里可以根据标签、组件或其他条件来判断
            // 简单实现：检查是否有特定的组件或标签
            
            if (targetType == "person")
            {
                return collider.CompareTag("Worker") || collider.GetComponent<WorkerSimulator>() != null;
            }
            else if (targetType == "vehicle")
            {
                return collider.CompareTag("Vehicle") || collider.name.ToLower().Contains("vehicle");
            }
            
            return false;
        }
        
        /// <summary>
        /// 检查是否有视线
        /// </summary>
        private bool HasLineOfSight(Vector3 targetPosition)
        {
            Vector3 direction = (targetPosition - transform.position).normalized;
            float distance = Vector3.Distance(transform.position, targetPosition);
            
            RaycastHit hit;
            if (Physics.Raycast(transform.position, direction, out hit, distance))
            {
                // 如果射线击中的是目标对象，则有视线
                return Vector3.Distance(hit.point, targetPosition) < 0.1f;
            }
            
            return true; // 没有障碍物
        }
        
        /// <summary>
        /// 检查是否是第一次采样
        /// </summary>
        private bool IsFirstSample()
        {
            return string.IsNullOrEmpty(lastReportedStatus);
        }
        
        /// <summary>
        /// 手动触发身份识别
        /// </summary>
        public void TriggerIdentification()
        {
            if (!continuousMode)
            {
                status = "triggered";
                LogDebug("手动触发身份识别");
                
                // 立即生成数据
                GenerateDataManually().Forget();
            }
        }
        
        /// <summary>
        /// 手动生成数据
        /// </summary>
        private async UniTaskVoid GenerateDataManually()
        {
            var data = await GenerateSensorDataAsync(CancellationToken.None);
            if (data != null)
            {
                TriggerDataGenerated(data);
            }
        }
        
        /// <summary>
        /// 设置人员信息
        /// </summary>
        public void SetPersonInfo(string name, string empId, string role, string dept, string cardId)
        {
            targetName = name;
            employeeId = empId;
            this.role = role;
            department = dept;
            this.cardId = cardId;
            targetType = "person";
            
            LogDebug($"设置人员信息: {name} ({empId})");
        }
        
        /// <summary>
        /// 设置车辆信息
        /// </summary>
        public void SetVehicleInfo(string vehicleType, string plate, string driverName, string driverId)
        {
            this.vehicleType = vehicleType;
            licensePlate = plate;
            this.driverName = driverName;
            this.driverId = driverId;
            targetType = "vehicle";
            targetName = $"{vehicleType}_{plate}";
            
            LogDebug($"设置车辆信息: {vehicleType} ({plate})");
        }
        
        /// <summary>
        /// 更新状态
        /// </summary>
        public void UpdateStatus(string newStatus)
        {
            if (status != newStatus)
            {
                string oldStatus = status;
                status = newStatus;
                LogDebug($"状态更新: {oldStatus} -> {newStatus}");
            }
        }
        
        /// <summary>
        /// 获取完整身份信息
        /// </summary>
        public IdentityData GetFullIdentityInfo()
        {
            var data = new IdentityData(sensorId, deviceId);
            data.name = targetName;
            data.role = role;
            data.department = department;
            data.cardId = cardId;
            data.targetType = targetType;
            data.status = status;
            
            return data;
        }
        
        /// <summary>
        /// 检查身份是否有效
        /// </summary>
        public bool IsIdentityValid()
        {
            if (string.IsNullOrEmpty(targetName)) return false;
            if (string.IsNullOrEmpty(cardId)) return false;
            
            if (targetType == "person")
            {
                return !string.IsNullOrEmpty(role) && !string.IsNullOrEmpty(department);
            }
            else if (targetType == "vehicle")
            {
                return !string.IsNullOrEmpty(vehicleType) && !string.IsNullOrEmpty(licensePlate);
            }
            
            return false;
        }
        
        /// <summary>
        /// 获取检测范围内的目标数量
        /// </summary>
        public int GetTargetsInRange()
        {
            Collider[] colliders = Physics.OverlapSphere(transform.position, detectionRange, detectionLayers);
            int count = 0;
            
            foreach (var collider in colliders)
            {
                if (IsValidTarget(collider))
                {
                    count++;
                }
            }
            
            return count;
        }
        
        public override bool ValidateConfiguration()
        {
            if (!base.ValidateConfiguration()) return false;
            
            if (string.IsNullOrEmpty(targetName))
            {
                LogError("目标名称不能为空");
                return false;
            }
            
            if (string.IsNullOrEmpty(cardId))
            {
                LogError("卡片ID不能为空");
                return false;
            }
            
            if (detectionRange <= 0)
            {
                LogError("检测范围必须大于0");
                return false;
            }
            
            if (targetType == "person" && (string.IsNullOrEmpty(role) || string.IsNullOrEmpty(department)))
            {
                LogError("人员类型需要设置角色和部门");
                return false;
            }
            
            if (targetType == "vehicle" && (string.IsNullOrEmpty(vehicleType) || string.IsNullOrEmpty(licensePlate)))
            {
                LogError("车辆类型需要设置车辆类型和车牌号");
                return false;
            }
            
            return true;
        }
        
        /// <summary>
        /// 绘制调试信息
        /// </summary>
        private void OnDrawGizmos()
        {
            // 绘制检测范围
            Gizmos.color = isTargetDetected ? Color.green : Color.red;
            Gizmos.DrawWireSphere(transform.position, detectionRange);
            
            // 绘制传感器方向
            Gizmos.color = Color.blue;
            Gizmos.DrawRay(transform.position, transform.forward * detectionRange * 0.5f);
            
            // 如果需要视线检查，绘制视线
            if (requireLineOfSight && isTargetDetected)
            {
                Gizmos.color = Color.yellow;
                // 这里可以绘制到检测目标的视线
            }
        }
        
        /// <summary>
        /// 绘制选中时的调试信息
        /// </summary>
        private void OnDrawGizmosSelected()
        {
            // 绘制详细的检测信息
            Gizmos.color = Color.cyan;
            Gizmos.DrawWireSphere(transform.position, detectionRange);
            
            // 显示检测到的目标
            Collider[] colliders = Physics.OverlapSphere(transform.position, detectionRange, detectionLayers);
            foreach (var collider in colliders)
            {
                if (IsValidTarget(collider))
                {
                    Gizmos.color = Color.green;
                    Gizmos.DrawLine(transform.position, collider.transform.position);
                    Gizmos.DrawWireCube(collider.transform.position, Vector3.one * 0.2f);
                }
            }
        }
    }
}
