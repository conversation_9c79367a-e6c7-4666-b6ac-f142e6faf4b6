using System;
using System.Collections.Generic;
using UnityEngine;
using Cysharp.Threading.Tasks;
using System.Threading;

namespace Simulation.IoT
{
    /// <summary>
    /// MQTT连接测试器
    /// 提供全面的MQTT连接测试功能
    /// </summary>
    public class MQTTConnectionTester : MonoBehaviour
    {
        [Header("测试配置")]
        [SerializeField] private MQTTConnectionPreset[] testPresets;
        [SerializeField] private bool runAllPresetsOnStart = false;
        [SerializeField] private float testTimeout = 30f;
        [SerializeField] private int testMessageCount = 5;
        [SerializeField] private float messageSendInterval = 2f;
        
        [Header("测试结果")]
        [SerializeField] private List<TestResult> testResults = new List<TestResult>();
        
        private MQTTManager mqttManager;
        private CancellationTokenSource testCancellationTokenSource;
        private bool isTestRunning = false;
        
        // 测试事件
        public event Action<TestResult> OnTestCompleted;
        public event Action<string> OnTestProgress;
        
        private void Start()
        {
            // 获取或创建MQTT管理器
            mqttManager = GetComponent<MQTTManager>();
            if (mqttManager == null)
            {
                mqttManager = gameObject.AddComponent<MQTTManager>();
            }
            
            if (runAllPresetsOnStart)
            {
                RunAllPresetsTest();
            }
        }
        
        private void OnDestroy()
        {
            testCancellationTokenSource?.Cancel();
        }
        
        /// <summary>
        /// 运行所有预设的测试
        /// </summary>
        [ContextMenu("运行所有预设测试")]
        public void RunAllPresetsTest()
        {
            if (isTestRunning)
            {
                Debug.LogWarning("[MQTTTester] 测试已在运行中");
                return;
            }
            
            RunAllPresetsTestAsync().Forget();
        }
        
        /// <summary>
        /// 运行单个预设测试
        /// </summary>
        public void RunPresetTest(MQTTConnectionPreset preset)
        {
            if (isTestRunning)
            {
                Debug.LogWarning("[MQTTTester] 测试已在运行中");
                return;
            }
            
            RunSinglePresetTestAsync(preset).Forget();
        }
        
        /// <summary>
        /// 停止当前测试
        /// </summary>
        [ContextMenu("停止测试")]
        public void StopTest()
        {
            testCancellationTokenSource?.Cancel();
            isTestRunning = false;
            OnTestProgress?.Invoke("测试已停止");
        }
        
        /// <summary>
        /// 清除测试结果
        /// </summary>
        [ContextMenu("清除测试结果")]
        public void ClearTestResults()
        {
            testResults.Clear();
            Debug.Log("[MQTTTester] 测试结果已清除");
        }
        
        /// <summary>
        /// 运行所有预设测试的异步方法
        /// </summary>
        private async UniTaskVoid RunAllPresetsTestAsync()
        {
            if (testPresets == null || testPresets.Length == 0)
            {
                Debug.LogWarning("[MQTTTester] 没有配置测试预设");
                return;
            }
            
            isTestRunning = true;
            testCancellationTokenSource = new CancellationTokenSource();
            
            try
            {
                OnTestProgress?.Invoke($"开始测试 {testPresets.Length} 个预设配置");
                
                for (int i = 0; i < testPresets.Length; i++)
                {
                    if (testCancellationTokenSource.Token.IsCancellationRequested)
                        break;
                    
                    var preset = testPresets[i];
                    if (preset == null) continue;
                    
                    OnTestProgress?.Invoke($"测试预设 {i + 1}/{testPresets.Length}: {preset.PresetName}");
                    
                    var result = await TestPresetAsync(preset, testCancellationTokenSource.Token);
                    testResults.Add(result);
                    OnTestCompleted?.Invoke(result);
                    
                    // 测试间隔
                    if (i < testPresets.Length - 1)
                    {
                        await UniTask.Delay(2000, cancellationToken: testCancellationTokenSource.Token);
                    }
                }
                
                OnTestProgress?.Invoke("所有预设测试完成");
                LogTestSummary();
            }
            catch (OperationCanceledException)
            {
                OnTestProgress?.Invoke("测试被取消");
            }
            catch (Exception ex)
            {
                Debug.LogError($"[MQTTTester] 测试异常: {ex.Message}");
                OnTestProgress?.Invoke($"测试异常: {ex.Message}");
            }
            finally
            {
                isTestRunning = false;
                testCancellationTokenSource?.Dispose();
                testCancellationTokenSource = null;
            }
        }
        
        /// <summary>
        /// 运行单个预设测试的异步方法
        /// </summary>
        private async UniTaskVoid RunSinglePresetTestAsync(MQTTConnectionPreset preset)
        {
            isTestRunning = true;
            testCancellationTokenSource = new CancellationTokenSource();
            
            try
            {
                OnTestProgress?.Invoke($"开始测试预设: {preset.PresetName}");
                
                var result = await TestPresetAsync(preset, testCancellationTokenSource.Token);
                testResults.Add(result);
                OnTestCompleted?.Invoke(result);
                
                OnTestProgress?.Invoke("测试完成");
            }
            catch (OperationCanceledException)
            {
                OnTestProgress?.Invoke("测试被取消");
            }
            catch (Exception ex)
            {
                Debug.LogError($"[MQTTTester] 测试异常: {ex.Message}");
                OnTestProgress?.Invoke($"测试异常: {ex.Message}");
            }
            finally
            {
                isTestRunning = false;
                testCancellationTokenSource?.Dispose();
                testCancellationTokenSource = null;
            }
        }
        
        /// <summary>
        /// 测试单个预设配置
        /// </summary>
        private async UniTask<TestResult> TestPresetAsync(MQTTConnectionPreset preset, CancellationToken cancellationToken)
        {
            var result = new TestResult
            {
                PresetName = preset.PresetName,
                BrokerAddress = preset.BrokerAddress,
                BrokerPort = preset.BrokerPort,
                Transport = preset.Transport.ToString(),
                UseSSL = preset.UseSSL,
                StartTime = DateTime.Now
            };
            
            try
            {
                // 1. 验证配置
                if (!preset.ValidateConfiguration())
                {
                    result.Success = false;
                    result.ErrorMessage = "配置验证失败";
                    result.EndTime = DateTime.Now;
                    return result;
                }
                
                // 2. 应用配置
                preset.ApplyToMQTTManager(mqttManager);
                
                // 3. 测试连接
                var connectStartTime = DateTime.Now;
                bool connected = await mqttManager.ConnectAsync();
                result.ConnectionTime = (DateTime.Now - connectStartTime).TotalMilliseconds;
                
                if (!connected)
                {
                    result.Success = false;
                    result.ErrorMessage = "连接失败";
                    result.EndTime = DateTime.Now;
                    return result;
                }
                
                result.ConnectionSuccess = true;
                
                // 4. 测试发布和订阅
                await TestPublishSubscribeAsync(result, cancellationToken);
                
                // 5. 断开连接
                await mqttManager.DisconnectAsync();
                
                result.Success = result.ConnectionSuccess && result.PublishSuccess && result.SubscribeSuccess;
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.ErrorMessage = ex.Message;
            }
            finally
            {
                result.EndTime = DateTime.Now;
            }
            
            return result;
        }
        
        /// <summary>
        /// 测试发布和订阅功能
        /// </summary>
        private async UniTask TestPublishSubscribeAsync(TestResult result, CancellationToken cancellationToken)
        {
            try
            {
                string testTopic = "test/mqtt_tester";
                int receivedMessages = 0;
                
                // 订阅测试主题
                void OnMessageReceived(string topic, string message)
                {
                    if (topic.Contains(testTopic))
                    {
                        receivedMessages++;
                    }
                }
                
                mqttManager.OnMessageReceived += OnMessageReceived;
                
                // 订阅主题
                bool subscribed = mqttManager.SubscribeToTopic(testTopic);
                result.SubscribeSuccess = subscribed;
                
                if (subscribed)
                {
                    // 等待订阅生效
                    await UniTask.Delay(1000, cancellationToken: cancellationToken);
                    
                    // 发布测试消息
                    var publishStartTime = DateTime.Now;
                    
                    for (int i = 0; i < testMessageCount; i++)
                    {
                        if (cancellationToken.IsCancellationRequested)
                            break;
                        
                        string message = $"测试消息 #{i + 1} - {DateTime.Now:HH:mm:ss.fff}";
                        bool published = mqttManager.PublishMessage(testTopic, message, false);
                        
                        if (published)
                        {
                            result.PublishedMessages++;
                        }
                        
                        await UniTask.Delay(TimeSpan.FromSeconds(messageSendInterval), cancellationToken: cancellationToken);
                    }
                    
                    result.PublishTime = (DateTime.Now - publishStartTime).TotalMilliseconds;
                    result.PublishSuccess = result.PublishedMessages > 0;
                    
                    // 等待接收消息
                    await UniTask.Delay(3000, cancellationToken: cancellationToken);
                    result.ReceivedMessages = receivedMessages;
                }
                
                mqttManager.OnMessageReceived -= OnMessageReceived;
            }
            catch (Exception ex)
            {
                result.ErrorMessage += $" 发布订阅测试异常: {ex.Message}";
            }
        }
        
        /// <summary>
        /// 记录测试摘要
        /// </summary>
        private void LogTestSummary()
        {
            int successCount = 0;
            int totalCount = testResults.Count;
            
            foreach (var result in testResults)
            {
                if (result.Success)
                    successCount++;
            }
            
            Debug.Log($"[MQTTTester] 测试摘要: {successCount}/{totalCount} 个预设测试成功");
            
            foreach (var result in testResults)
            {
                string status = result.Success ? "成功" : "失败";
                Debug.Log($"[MQTTTester] {result.PresetName}: {status} - {result.BrokerAddress}:{result.BrokerPort}");
                
                if (!result.Success && !string.IsNullOrEmpty(result.ErrorMessage))
                {
                    Debug.LogError($"[MQTTTester] 错误: {result.ErrorMessage}");
                }
            }
        }
    }
    
    /// <summary>
    /// 测试结果数据结构
    /// </summary>
    [Serializable]
    public class TestResult
    {
        public string PresetName;
        public string BrokerAddress;
        public int BrokerPort;
        public string Transport;
        public bool UseSSL;
        public DateTime StartTime;
        public DateTime EndTime;
        public bool Success;
        public string ErrorMessage;
        
        // 连接测试结果
        public bool ConnectionSuccess;
        public double ConnectionTime; // 毫秒
        
        // 发布测试结果
        public bool PublishSuccess;
        public int PublishedMessages;
        public double PublishTime; // 毫秒
        
        // 订阅测试结果
        public bool SubscribeSuccess;
        public int ReceivedMessages;
        
        public double TotalTestTime => (EndTime - StartTime).TotalMilliseconds;
    }
}
