using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using Simulation.Data;

namespace Simulation.IoT
{
    /// <summary>
    /// 事件管理器
    /// 扩展IoT系统的事件处理能力，提供事件分类、过滤、统计和批量处理功能
    /// </summary>
    public class EventManager : MonoBehaviour
    {
        [Header("事件管理配置")]
        [SerializeField] private bool enableEventFiltering = true; // 启用事件过滤
        [SerializeField] private bool enableEventStatistics = true; // 启用事件统计
        [SerializeField] private bool enableEventHistory = true; // 启用事件历史
        [SerializeField] private int maxHistorySize = 1000; // 最大历史记录数

        [Header("事件过滤配置")]
        [SerializeField] private List<string> blockedEventTypes = new List<string>(); // 阻止的事件类型
        [SerializeField] private List<string> priorityEventTypes = new List<string>(); // 优先事件类型
        [SerializeField] private float duplicateEventThreshold = 5f; // 重复事件阈值（秒）
        [SerializeField] private bool enableDuplicateFiltering = true; // 启用重复过滤

        [Header("事件统计配置")]
        [SerializeField] private float statisticsInterval = 300f; // 统计间隔（秒）
        [SerializeField] private bool enablePerformanceMetrics = true; // 启用性能指标
        [SerializeField] private bool logStatistics = true; // 记录统计信息

        [Header("调试配置")]
        [SerializeField] private bool enableDebugLog = true;
        [SerializeField] private bool showEventGizmos = false;

        // 事件历史和统计
        private List<EventRecord> eventHistory = new List<EventRecord>();
        private Dictionary<string, EventStatistics> eventStatistics = new Dictionary<string, EventStatistics>();
        private Dictionary<string, DateTime> lastEventTimes = new Dictionary<string, DateTime>();

        // 事件分类（使用标准化事件类型）
        private readonly HashSet<string> safetyEventTypes = new HashSet<string>(EventTypes.GetSafetyEventTypes());
        private readonly HashSet<string> operationalEventTypes = new HashSet<string>(EventTypes.GetOperationalEventTypes());
        private readonly HashSet<string> systemEventTypes = new HashSet<string>(EventTypes.GetSystemEventTypes());
        private readonly HashSet<string> alertEventTypes = new HashSet<string>(EventTypes.GetAlertEventTypes());

        // 单例实例
        public static EventManager Instance { get; private set; }

        // 公共属性
        public int TotalEventsProcessed => eventHistory.Count;
        public int SafetyEventsCount => GetEventCountByCategory(EventCategory.Safety);
        public int OperationalEventsCount => GetEventCountByCategory(EventCategory.Operational);
        public int SystemEventsCount => GetEventCountByCategory(EventCategory.System);
        public int AlertEventsCount => GetEventCountByCategory(EventCategory.Alert);

        private void Awake()
        {
            // 单例模式
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                InitializeEventManager();
            }
            else
            {
                Destroy(gameObject);
            }
        }

        private void Start()
        {
            // 启动统计循环
            if (enableEventStatistics)
            {
                InvokeRepeating(nameof(LogEventStatistics), statisticsInterval, statisticsInterval);
            }
        }

        /// <summary>
        /// 初始化事件管理器
        /// </summary>
        private void InitializeEventManager()
        {
            // 初始化默认的阻止事件类型
            if (blockedEventTypes.Count == 0)
            {
                blockedEventTypes.AddRange(new[] { "debug_event", "test_event" });
            }

            // 初始化默认的优先事件类型
            if (priorityEventTypes.Count == 0)
            {
                priorityEventTypes.AddRange(EventTypes.GetPriorityEventTypes());
            }

            LogDebug("事件管理器初始化完成");
        }

        /// <summary>
        /// 处理事件
        /// </summary>
        public bool ProcessEvent(EventPacket eventPacket)
        {
            try
            {
                // 事件过滤
                if (enableEventFiltering && !ShouldProcessEvent(eventPacket))
                {
                    return false;
                }

                // 分类事件
                EventCategory category = ClassifyEvent(eventPacket.EventType);

                // 创建事件记录
                var eventRecord = new EventRecord
                {
                    EventId = eventPacket.EventId,
                    EventType = eventPacket.EventType,
                    Category = category,
                    SourceId = eventPacket.SourceId,
                    Timestamp = eventPacket.Timestamp,
                    Title = eventPacket.Title,
                    Description = eventPacket.Description,
                    Severity = DetermineSeverity(eventPacket.EventType, category),
                    ProcessedAt = DateTime.UtcNow
                };

                // 添加到历史记录
                if (enableEventHistory)
                {
                    AddToHistory(eventRecord);
                }

                // 更新统计
                if (enableEventStatistics)
                {
                    UpdateStatistics(eventRecord);
                }

                // 处理特殊事件
                HandleSpecialEvents(eventRecord, eventPacket);

                LogDebug($"事件处理完成: {eventPacket.EventType} - {eventPacket.Title}");
                return true;
            }
            catch (Exception ex)
            {
                LogError($"事件处理异常: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 判断是否应该处理事件
        /// </summary>
        private bool ShouldProcessEvent(EventPacket eventPacket)
        {
            // 检查阻止列表
            if (blockedEventTypes.Contains(eventPacket.EventType))
            {
                LogDebug($"事件被阻止: {eventPacket.EventType}");
                return false;
            }

            // 检查重复事件
            if (enableDuplicateFiltering && IsDuplicateEvent(eventPacket))
            {
                LogDebug($"重复事件被过滤: {eventPacket.EventType}");
                return false;
            }

            return true;
        }

        /// <summary>
        /// 检查是否为重复事件
        /// </summary>
        private bool IsDuplicateEvent(EventPacket eventPacket)
        {
            string eventKey = $"{eventPacket.SourceId}_{eventPacket.EventType}";

            if (lastEventTimes.TryGetValue(eventKey, out DateTime lastTime))
            {
                double timeDifference = (eventPacket.Timestamp - lastTime).TotalSeconds;
                if (timeDifference < duplicateEventThreshold)
                {
                    return true;
                }
            }

            lastEventTimes[eventKey] = eventPacket.Timestamp;
            return false;
        }

        /// <summary>
        /// 分类事件
        /// </summary>
        private EventCategory ClassifyEvent(string eventType)
        {
            if (safetyEventTypes.Contains(eventType))
                return EventCategory.Safety;

            if (operationalEventTypes.Contains(eventType))
                return EventCategory.Operational;

            if (systemEventTypes.Contains(eventType))
                return EventCategory.System;

            if (alertEventTypes.Contains(eventType))
                return EventCategory.Alert;

            return EventCategory.Other;
        }

        /// <summary>
        /// 确定事件严重程度
        /// </summary>
        private EventSeverity DetermineSeverity(string eventType, EventCategory category)
        {
            // 优先事件为高严重程度
            if (priorityEventTypes.Contains(eventType))
                return EventSeverity.Critical;

            // 根据分类确定严重程度
            switch (category)
            {
                case EventCategory.Safety:
                    return EventSeverity.High;
                case EventCategory.Alert:
                    return EventSeverity.Medium;
                case EventCategory.System:
                    return EventSeverity.Low;
                case EventCategory.Operational:
                    return EventSeverity.Low;
                default:
                    return EventSeverity.Low;
            }
        }

        /// <summary>
        /// 添加到历史记录
        /// </summary>
        private void AddToHistory(EventRecord eventRecord)
        {
            eventHistory.Add(eventRecord);

            // 限制历史记录大小
            if (eventHistory.Count > maxHistorySize)
            {
                eventHistory.RemoveAt(0);
            }
        }

        /// <summary>
        /// 更新统计信息
        /// </summary>
        private void UpdateStatistics(EventRecord eventRecord)
        {
            if (!eventStatistics.TryGetValue(eventRecord.EventType, out EventStatistics stats))
            {
                stats = new EventStatistics
                {
                    EventType = eventRecord.EventType,
                    Category = eventRecord.Category,
                    FirstOccurrence = eventRecord.Timestamp,
                    LastOccurrence = eventRecord.Timestamp,
                    Count = 0,
                    TotalProcessingTime = 0
                };
                eventStatistics[eventRecord.EventType] = stats;
            }

            stats.Count++;
            stats.LastOccurrence = eventRecord.Timestamp;

            if (enablePerformanceMetrics)
            {
                double processingTime = (eventRecord.ProcessedAt - eventRecord.Timestamp).TotalMilliseconds;
                stats.TotalProcessingTime += processingTime;
                stats.AverageProcessingTime = stats.TotalProcessingTime / stats.Count;
            }
        }

        /// <summary>
        /// 处理特殊事件
        /// </summary>
        private void HandleSpecialEvents(EventRecord eventRecord, EventPacket eventPacket)
        {
            // 处理安全事件
            if (eventRecord.Category == EventCategory.Safety)
            {
                HandleSafetyEvent(eventRecord, eventPacket);
            }

            // 处理系统事件
            if (eventRecord.Category == EventCategory.System)
            {
                HandleSystemEvent(eventRecord, eventPacket);
            }

            // 处理优先事件
            if (priorityEventTypes.Contains(eventRecord.EventType))
            {
                HandlePriorityEvent(eventRecord, eventPacket);
            }
        }

        /// <summary>
        /// 处理安全事件
        /// </summary>
        private void HandleSafetyEvent(EventRecord eventRecord, EventPacket eventPacket)
        {
            LogDebug($"安全事件处理: {eventRecord.EventType}");

            // 可以在这里添加特殊的安全事件处理逻辑
            // 例如：发送紧急通知、触发报警等
            _ = eventPacket; // 避免未使用参数警告
        }

        /// <summary>
        /// 处理系统事件
        /// </summary>
        private void HandleSystemEvent(EventRecord eventRecord, EventPacket eventPacket)
        {
            LogDebug($"系统事件处理: {eventRecord.EventType}");

            // 可以在这里添加系统事件处理逻辑
            // 例如：系统监控、性能分析等
            _ = eventPacket; // 避免未使用参数警告
        }

        /// <summary>
        /// 处理优先事件
        /// </summary>
        private void HandlePriorityEvent(EventRecord eventRecord, EventPacket eventPacket)
        {
            LogDebug($"优先事件处理: {eventRecord.EventType}");

            // 可以在这里添加优先事件处理逻辑
            // 例如：立即发送、特殊路由等
            _ = eventPacket; // 避免未使用参数警告
        }

        /// <summary>
        /// 获取指定分类的事件数量
        /// </summary>
        public int GetEventCountByCategory(EventCategory category)
        {
            return eventHistory.Count(e => e.Category == category);
        }

        /// <summary>
        /// 获取指定时间范围内的事件
        /// </summary>
        public List<EventRecord> GetEventsInTimeRange(DateTime startTime, DateTime endTime)
        {
            return eventHistory.Where(e => e.Timestamp >= startTime && e.Timestamp <= endTime).ToList();
        }

        /// <summary>
        /// 获取事件统计信息
        /// </summary>
        public Dictionary<string, EventStatistics> GetEventStatistics()
        {
            return new Dictionary<string, EventStatistics>(eventStatistics);
        }

        /// <summary>
        /// 记录事件统计信息
        /// </summary>
        private void LogEventStatistics()
        {
            if (!logStatistics || eventStatistics.Count == 0) return;

            LogDebug($"=== 事件统计 (过去{statisticsInterval}秒) ===");
            LogDebug($"总事件数: {TotalEventsProcessed}");
            LogDebug($"安全事件: {SafetyEventsCount}, 操作事件: {OperationalEventsCount}");
            LogDebug($"系统事件: {SystemEventsCount}, 警报事件: {AlertEventsCount}");

            // 显示最频繁的事件类型
            var topEvents = eventStatistics.Values
                .OrderByDescending(s => s.Count)
                .Take(5)
                .ToList();

            LogDebug("最频繁事件类型:");
            foreach (var eventStat in topEvents)
            {
                LogDebug($"  {eventStat.EventType}: {eventStat.Count}次");
            }
        }

        /// <summary>
        /// 清理历史记录
        /// </summary>
        public void ClearHistory()
        {
            eventHistory.Clear();
            eventStatistics.Clear();
            lastEventTimes.Clear();
            LogDebug("事件历史记录已清理");
        }

        /// <summary>
        /// 调试日志
        /// </summary>
        private void LogDebug(string message)
        {
            if (enableDebugLog)
            {
                Debug.Log($"[EventManager] {message}");
            }
        }

        /// <summary>
        /// 错误日志
        /// </summary>
        private void LogError(string message)
        {
            Debug.LogError($"[EventManager] {message}");
        }
    }
}
