using System.Collections;
using System.Threading;
using UnityEngine;
using UnityEngine.TestTools;
using NUnit.Framework;
using Cysharp.Threading.Tasks;
using Simulation.IoT;

namespace Simulation.Tests
{
    /// <summary>
    /// 测试基类，提供通用的测试工具和设置
    /// </summary>
    public abstract class TestBase
    {
        protected GameObject testGameObject;
        protected CancellationTokenSource cancellationTokenSource;
        
        /// <summary>
        /// 每个测试开始前的设置
        /// </summary>
        [SetUp]
        public virtual void SetUp()
        {
            // 创建测试用的GameObject
            testGameObject = new GameObject("TestObject");
            
            // 创建取消令牌源
            cancellationTokenSource = new CancellationTokenSource();
            
            // 初始化IoT系统（如果需要）
            InitializeIoTSystemIfNeeded();
        }
        
        /// <summary>
        /// 每个测试结束后的清理
        /// </summary>
        [TearDown]
        public virtual void TearDown()
        {
            // 取消所有异步操作
            cancellationTokenSource?.Cancel();
            cancellationTokenSource?.Dispose();
            
            // 清理测试GameObject
            if (testGameObject != null)
            {
                Object.DestroyImmediate(testGameObject);
            }
            
            // 清理IoT系统
            CleanupIoTSystem();
        }
        
        /// <summary>
        /// 初始化IoT系统（如果需要）
        /// </summary>
        protected virtual void InitializeIoTSystemIfNeeded()
        {
            // 检查是否已经存在IoT系统实例
            if (IoTSystem.Instance == null)
            {
                // 创建IoT系统GameObject
                var iotGameObject = new GameObject("IoTSystem");
                iotGameObject.AddComponent<IoTSystem>();
            }
        }
        
        /// <summary>
        /// 清理IoT系统
        /// </summary>
        protected virtual void CleanupIoTSystem()
        {
            // 查找并销毁IoT系统GameObject
            var iotSystem = Object.FindObjectOfType<IoTSystem>();
            if (iotSystem != null)
            {
                Object.DestroyImmediate(iotSystem.gameObject);
            }
        }
        
        /// <summary>
        /// 等待异步操作完成的协程
        /// </summary>
        protected IEnumerator WaitForAsync(UniTask task, float timeoutSeconds = 5f)
        {
            bool completed = false;
            bool timedOut = false;
            
            // 启动异步任务
            task.ContinueWith(() => completed = true);
            
            // 启动超时计时器
            float startTime = Time.realtimeSinceStartup;
            
            // 等待完成或超时
            while (!completed && !timedOut)
            {
                if (Time.realtimeSinceStartup - startTime > timeoutSeconds)
                {
                    timedOut = true;
                }
                yield return null;
            }
            
            if (timedOut)
            {
                Assert.Fail($"异步操作超时 ({timeoutSeconds}秒)");
            }
        }
        
        /// <summary>
        /// 等待条件满足的协程
        /// </summary>
        protected IEnumerator WaitForCondition(System.Func<bool> condition, float timeoutSeconds = 5f, string errorMessage = "条件等待超时")
        {
            float startTime = Time.realtimeSinceStartup;
            
            while (!condition() && (Time.realtimeSinceStartup - startTime) < timeoutSeconds)
            {
                yield return null;
            }
            
            if (!condition())
            {
                Assert.Fail($"{errorMessage} ({timeoutSeconds}秒)");
            }
        }
        
        /// <summary>
        /// 创建测试用的Transform组件
        /// </summary>
        protected Transform CreateTestTransform(Vector3 position = default, Quaternion rotation = default)
        {
            var go = new GameObject("TestTransform");
            go.transform.position = position;
            go.transform.rotation = rotation;
            return go.transform;
        }
        
        /// <summary>
        /// 创建测试用的组件
        /// </summary>
        protected T CreateTestComponent<T>() where T : Component
        {
            return testGameObject.AddComponent<T>();
        }
        
        /// <summary>
        /// 断言两个浮点数近似相等
        /// </summary>
        protected void AssertApproximately(float expected, float actual, float tolerance = 0.01f, string message = "")
        {
            Assert.That(Mathf.Abs(expected - actual), Is.LessThanOrEqualTo(tolerance), 
                $"{message} Expected: {expected}, Actual: {actual}, Tolerance: {tolerance}");
        }
        
        /// <summary>
        /// 断言Vector3近似相等
        /// </summary>
        protected void AssertVector3Approximately(Vector3 expected, Vector3 actual, float tolerance = 0.01f, string message = "")
        {
            AssertApproximately(expected.x, actual.x, tolerance, $"{message} (X)");
            AssertApproximately(expected.y, actual.y, tolerance, $"{message} (Y)");
            AssertApproximately(expected.z, actual.z, tolerance, $"{message} (Z)");
        }
        
        /// <summary>
        /// 模拟时间流逝
        /// </summary>
        protected IEnumerator SimulateTimeElapse(float seconds)
        {
            float elapsed = 0f;
            while (elapsed < seconds)
            {
                elapsed += Time.deltaTime;
                yield return null;
            }
        }
        
        /// <summary>
        /// 验证JSON序列化和反序列化
        /// </summary>
        protected void AssertJsonSerialization<T>(T original) where T : class
        {
            string json = JsonUtility.ToJson(original);
            Assert.IsNotNull(json, "JSON序列化失败");
            Assert.IsNotEmpty(json, "JSON序列化结果为空");
            
            T deserialized = JsonUtility.FromJson<T>(json);
            Assert.IsNotNull(deserialized, "JSON反序列化失败");
        }
        
        /// <summary>
        /// 创建模拟的传感器数据
        /// </summary>
        protected string CreateMockSensorId(string prefix = "test")
        {
            return $"{prefix}_{System.Guid.NewGuid().ToString("N")[..8]}";
        }
        
        /// <summary>
        /// 验证事件是否被正确触发
        /// </summary>
        protected void AssertEventTriggered<T>(System.Action<T> eventSubscription, System.Action triggerAction, float timeoutSeconds = 1f)
        {
            bool eventTriggered = false;
            T eventData = default(T);
            
            // 订阅事件
            System.Action<T> handler = (data) =>
            {
                eventTriggered = true;
                eventData = data;
            };
            
            eventSubscription(handler);
            
            // 触发操作
            triggerAction();
            
            // 等待事件触发
            float startTime = Time.realtimeSinceStartup;
            while (!eventTriggered && (Time.realtimeSinceStartup - startTime) < timeoutSeconds)
            {
                // 让Unity处理事件
                System.Threading.Thread.Sleep(10);
            }
            
            Assert.IsTrue(eventTriggered, "期望的事件未被触发");
        }
    }
}
