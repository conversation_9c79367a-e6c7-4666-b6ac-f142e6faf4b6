# MQTTManager使用指南

## 概述

MQTTManager是基于BestMQTT插件实现的MQTT客户端管理器，提供了完整的MQTT连接、发布、订阅功能，支持TCP和WebSocket传输，TLS安全连接，以及自动重连机制。

## 主要功能

### 1. 连接管理
- **自动连接**: 支持TCP和WebSocket两种传输方式
- **TLS安全连接**: 支持加密传输
- **自动重连**: 连接断开时自动尝试重连
- **连接状态监控**: 实时监控连接状态

### 2. 消息发布
- **同步发布**: 立即发布消息
- **队列发布**: 离线时消息加入队列，连接后自动发送
- **QoS支持**: 支持QoS 0、1、2三个等级
- **消息保留**: 支持retain消息

### 3. 主题订阅
- **动态订阅**: 运行时订阅/取消订阅主题
- **消息回调**: 接收到消息时触发回调
- **通配符支持**: 支持MQTT主题通配符

## 配置参数

### MQTT连接配置
- **Broker Address**: MQTT代理服务器地址（默认：broker.emqx.io）
- **Broker Port**: 端口号（TCP默认1883，TLS默认8883）
- **Client Id**: 客户端标识符
- **Username/Password**: 认证信息
- **Use SSL**: 是否使用TLS加密
- **Keep Alive Interval**: 保活间隔（秒）
- **Connection Timeout**: 连接超时时间（秒）
- **Transport**: 传输方式（TCP/WebSocket）
- **Websocket Path**: WebSocket路径（默认：/mqtt）

### 重连配置
- **Auto Reconnect**: 是否自动重连
- **Reconnect Delay**: 重连延迟（秒）
- **Max Reconnect Attempts**: 最大重连次数

### 主题配置
- **Topic Prefix**: 主题前缀
- **QoS Level**: 默认QoS等级（0-2）
- **Retain Messages**: 是否保留消息

### 调试配置
- **Enable Debug Log**: 是否启用调试日志
- **Log Published Messages**: 是否记录发布的消息

## 使用方法

### 1. 基本使用

```csharp
// 获取MQTTManager组件
var mqttManager = GetComponent<MQTTManager>();

// 连接到MQTT代理
bool connected = await mqttManager.ConnectAsync();

if (connected)
{
    // 发布消息
    mqttManager.PublishMessage("test/topic", "Hello MQTT!");
    
    // 订阅主题
    mqttManager.SubscribeToTopic("test/response");
}
```

### 2. 事件处理

```csharp
// 订阅连接事件
mqttManager.OnConnected += () => {
    Debug.Log("MQTT连接成功");
};

mqttManager.OnDisconnected += () => {
    Debug.Log("MQTT连接断开");
};

mqttManager.OnConnectionError += (error) => {
    Debug.LogError($"MQTT连接错误: {error}");
};

// 订阅消息接收事件
mqttManager.OnMessageReceived += (topic, message) => {
    Debug.Log($"收到消息 - 主题: {topic}, 内容: {message}");
};
```

### 3. 发布不同类型的数据

```csharp
// 发布字符串消息
mqttManager.PublishMessage("sensors/temperature", "25.6");

// 发布对象（自动序列化为JSON）
var sensorData = new SensorData { 
    Temperature = 25.6f, 
    Humidity = 60.2f 
};
mqttManager.PublishMessage("sensors/data", sensorData);
```

## 测试验证

### 使用MQTTManagerTest组件

1. 在GameObject上添加`MQTTManagerTest`组件
2. 配置测试参数：
   - **Auto Start Test**: 是否自动开始测试
   - **Test Topic**: 测试主题名称
   - **Publish Interval**: 发布间隔（秒）

3. 运行测试：
   - 右键点击组件，选择"开始测试"
   - 或在Inspector中勾选"Auto Start Test"

### 测试流程

1. **连接测试**: 验证MQTT代理连接
2. **订阅测试**: 订阅测试主题
3. **发布测试**: 定期发布测试消息
4. **接收测试**: 验证消息接收功能

## 与IoT系统集成

MQTTManager已完全集成到IoT系统中：

```csharp
// IoTSystem自动管理MQTTManager
var iotSystem = IoTSystem.Instance;

// 发布传感器数据
iotSystem.PublishSensorData(sensorData);

// 发布事件
iotSystem.PublishEvent(eventData);
```

## 故障排除

### 常见问题

1. **连接失败**
   - 检查代理地址和端口
   - 验证网络连接
   - 确认认证信息

2. **消息发布失败**
   - 确认MQTT连接状态
   - 检查主题名称格式
   - 验证消息内容

3. **订阅无响应**
   - 确认主题过滤器正确
   - 检查QoS等级设置
   - 验证消息回调注册

### 调试技巧

1. 启用调试日志查看详细信息
2. 使用MQTT客户端工具验证代理连接
3. 检查Unity Console中的错误信息
4. 使用MQTTManagerTest组件进行功能验证

## 性能优化

1. **合理设置QoS等级**：根据消息重要性选择合适的QoS
2. **控制发布频率**：避免过于频繁的消息发布
3. **使用消息队列**：离线时使用队列缓存消息
4. **优化主题结构**：使用层次化的主题命名

## 安全建议

1. **使用TLS加密**：生产环境建议启用SSL/TLS
2. **设置认证信息**：配置用户名和密码
3. **限制主题权限**：在代理端配置主题访问控制
4. **定期更新凭据**：定期更换认证信息
