using UnityEngine;
using Cysharp.Threading.Tasks;
using System.Threading;

namespace Simulation.Config
{
    /// <summary>
    /// UniTask配置管理器
    /// 负责初始化和配置UniTask相关设置
    /// </summary>
    [CreateAssetMenu(fileName = "UniTaskConfig", menuName = "Simulation/Config/UniTask Config")]
    public class UniTaskConfig : ScriptableObject
    {
        [Header("UniTask 配置")]
        [SerializeField] private bool enableTaskTracker = true;
        [SerializeField] private bool enableStackTrace = false;
        [SerializeField] private bool propagateOperationCanceledException = false;
        
        [Header("性能配置")]
        [SerializeField] private int maxPoolSize = 100;
        [SerializeField] private bool enableAutoDisposeWhenPlayModeExit = true;
        
        /// <summary>
        /// 初始化UniTask配置
        /// </summary>
        [RuntimeInitializeOnLoadMethod(RuntimeInitializeLoadType.BeforeSceneLoad)]
        public static void Initialize()
        {
            // 设置UniTask调度器的异常处理
            UniTaskScheduler.UnobservedTaskException += OnUnobservedTaskException;
            
            // 设置是否传播OperationCanceledException
            UniTaskScheduler.PropagateOperationCanceledException = false;
            
            Debug.Log("[UniTaskConfig] UniTask配置初始化完成");
        }
        
        /// <summary>
        /// 处理未观察到的任务异常
        /// </summary>
        private static void OnUnobservedTaskException(System.Exception ex)
        {
            if (ex is OperationCanceledException)
            {
                // 忽略取消异常，这是正常的
                return;
            }
            
            Debug.LogError($"[UniTaskConfig] 未处理的UniTask异常: {ex}");
        }
        
        /// <summary>
        /// 应用配置设置
        /// </summary>
        public void ApplySettings()
        {
            UniTaskScheduler.PropagateOperationCanceledException = propagateOperationCanceledException;
            
            Debug.Log($"[UniTaskConfig] 配置已应用 - TaskTracker: {enableTaskTracker}, StackTrace: {enableStackTrace}");
        }
        
        /// <summary>
        /// 获取默认的CancellationToken
        /// </summary>
        public static CancellationToken GetApplicationCancellationToken()
        {
#if UNITY_EDITOR
            return Application.exitCancellationToken;
#else
            return CancellationToken.None;
#endif
        }
        
        /// <summary>
        /// 创建带超时的CancellationToken
        /// </summary>
        public static CancellationToken CreateTimeoutToken(float timeoutSeconds)
        {
            var cts = new CancellationTokenSource();
            cts.CancelAfterSlim(System.TimeSpan.FromSeconds(timeoutSeconds));
            return cts.Token;
        }
        
        /// <summary>
        /// 创建组合的CancellationToken
        /// </summary>
        public static CancellationToken CreateLinkedToken(params CancellationToken[] tokens)
        {
            return CancellationTokenSource.CreateLinkedTokenSource(tokens).Token;
        }
    }
    
    /// <summary>
    /// UniTask扩展方法
    /// </summary>
    public static class UniTaskExtensions
    {
        /// <summary>
        /// 安全执行UniTask，自动处理异常
        /// </summary>
        public static async UniTaskVoid SafeForget(this UniTask task, string context = "")
        {
            try
            {
                await task;
            }
            catch (OperationCanceledException)
            {
                // 忽略取消异常
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"[UniTaskExtensions] SafeForget异常 {context}: {ex}");
            }
        }
        
        /// <summary>
        /// 带超时的UniTask执行
        /// </summary>
        public static async UniTask<T> WithTimeout<T>(this UniTask<T> task, float timeoutSeconds)
        {
            var timeoutToken = UniTaskConfig.CreateTimeoutToken(timeoutSeconds);
            return await task.AttachExternalCancellation(timeoutToken);
        }
        
        /// <summary>
        /// 带超时的UniTask执行（无返回值）
        /// </summary>
        public static async UniTask WithTimeout(this UniTask task, float timeoutSeconds)
        {
            var timeoutToken = UniTaskConfig.CreateTimeoutToken(timeoutSeconds);
            await task.AttachExternalCancellation(timeoutToken);
        }
    }
}
