using UnityEngine;
using UnityEditor;
using UnityEngine.TestTools;
using System.Collections.Generic;
using System.Linq;

namespace Simulation.Tests
{
    /// <summary>
    /// 测试运行器，提供便捷的测试执行和管理功能
    /// </summary>
    public class TestRunner : MonoBehaviour
    {
        [Header("测试配置")]
        [SerializeField] private bool runOnStart = false;
        [SerializeField] private bool enableDetailedLogging = true;
        [SerializeField] private float testTimeout = 30f;
        
        [Header("测试过滤")]
        [SerializeField] private List<string> testCategories = new List<string>();
        [SerializeField] private List<string> excludeCategories = new List<string>();
        
        private void Start()
        {
            if (runOnStart)
            {
                RunAllTests();
            }
        }
        
        /// <summary>
        /// 运行所有测试
        /// </summary>
        [ContextMenu("运行所有测试")]
        public void RunAllTests()
        {
            LogTestInfo("开始运行所有测试...");
            
#if UNITY_EDITOR
            var testRunnerApi = UnityEditor.TestTools.TestRunner.Api.ScriptableObject.CreateInstance<UnityEditor.TestTools.TestRunner.Api.TestRunnerApi>();
            
            var filter = new UnityEditor.TestTools.TestRunner.Api.Filter()
            {
                testMode = UnityEditor.TestTools.TestRunner.Api.TestMode.EditMode | UnityEditor.TestTools.TestRunner.Api.TestMode.PlayMode,
                testNames = null,
                groupNames = null,
                categoryNames = testCategories.Count > 0 ? testCategories.ToArray() : null
            };
            
            testRunnerApi.Execute(filter);
#else
            LogTestWarning("测试运行器仅在编辑器模式下可用");
#endif
        }
        
        /// <summary>
        /// 运行IoT系统测试
        /// </summary>
        [ContextMenu("运行IoT系统测试")]
        public void RunIoTSystemTests()
        {
            RunTestsByClass("IoTSystemTests");
        }
        
        /// <summary>
        /// 运行传感器系统测试
        /// </summary>
        [ContextMenu("运行传感器系统测试")]
        public void RunSensorSystemTests()
        {
            RunTestsByClass("SensorSystemTests");
        }
        
        /// <summary>
        /// 运行模拟器系统测试
        /// </summary>
        [ContextMenu("运行模拟器系统测试")]
        public void RunSimulatorSystemTests()
        {
            RunTestsByClass("SimulatorSystemTests");
        }
        
        /// <summary>
        /// 按类名运行测试
        /// </summary>
        private void RunTestsByClass(string className)
        {
            LogTestInfo($"运行 {className} 测试...");
            
#if UNITY_EDITOR
            var testRunnerApi = UnityEditor.TestTools.TestRunner.Api.ScriptableObject.CreateInstance<UnityEditor.TestTools.TestRunner.Api.TestRunnerApi>();
            
            var filter = new UnityEditor.TestTools.TestRunner.Api.Filter()
            {
                testMode = UnityEditor.TestTools.TestRunner.Api.TestMode.EditMode | UnityEditor.TestTools.TestRunner.Api.TestMode.PlayMode,
                groupNames = new[] { $"Simulation.Tests.{className}" }
            };
            
            testRunnerApi.Execute(filter);
#endif
        }
        
        /// <summary>
        /// 验证测试环境
        /// </summary>
        [ContextMenu("验证测试环境")]
        public void ValidateTestEnvironment()
        {
            LogTestInfo("验证测试环境...");
            
            // 检查必要的组件
            bool allValid = true;
            
            // 检查IoT系统
            var iotSystem = FindObjectOfType<Simulation.IoT.IoTSystem>();
            if (iotSystem == null)
            {
                LogTestWarning("未找到IoT系统实例");
                allValid = false;
            }
            else
            {
                LogTestInfo("✓ IoT系统实例存在");
            }
            
            // 检查测试程序集
            var testAssembly = System.Reflection.Assembly.GetAssembly(typeof(TestBase));
            if (testAssembly == null)
            {
                LogTestError("未找到测试程序集");
                allValid = false;
            }
            else
            {
                LogTestInfo("✓ 测试程序集加载正常");
            }
            
            // 检查UniTask
            try
            {
                var uniTaskType = typeof(Cysharp.Threading.Tasks.UniTask);
                LogTestInfo("✓ UniTask可用");
            }
            catch
            {
                LogTestError("UniTask不可用");
                allValid = false;
            }
            
            if (allValid)
            {
                LogTestInfo("✅ 测试环境验证通过");
            }
            else
            {
                LogTestError("❌ 测试环境验证失败");
            }
        }
        
        /// <summary>
        /// 清理测试环境
        /// </summary>
        [ContextMenu("清理测试环境")]
        public void CleanupTestEnvironment()
        {
            LogTestInfo("清理测试环境...");
            
            // 清理测试对象
            var testObjects = FindObjectsOfType<GameObject>()
                .Where(go => go.name.Contains("Test") || go.name.Contains("Mock"))
                .ToArray();
            
            foreach (var obj in testObjects)
            {
                if (Application.isPlaying)
                {
                    Destroy(obj);
                }
                else
                {
                    DestroyImmediate(obj);
                }
            }
            
            LogTestInfo($"清理了 {testObjects.Length} 个测试对象");
            
            // 强制垃圾回收
            System.GC.Collect();
            
            LogTestInfo("✅ 测试环境清理完成");
        }
        
        /// <summary>
        /// 生成测试报告
        /// </summary>
        [ContextMenu("生成测试报告")]
        public void GenerateTestReport()
        {
            LogTestInfo("生成测试报告...");
            
            var report = new System.Text.StringBuilder();
            report.AppendLine("# Simulation 项目测试报告");
            report.AppendLine($"生成时间: {System.DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            report.AppendLine();
            
            report.AppendLine("## 测试环境信息");
            report.AppendLine($"- Unity版本: {Application.unityVersion}");
            report.AppendLine($"- 平台: {Application.platform}");
            report.AppendLine($"- 测试超时: {testTimeout}秒");
            report.AppendLine();
            
            report.AppendLine("## 测试模块");
            report.AppendLine("- IoT系统测试 (IoTSystemTests)");
            report.AppendLine("- 传感器系统测试 (SensorSystemTests)");
            report.AppendLine("- 模拟器系统测试 (SimulatorSystemTests)");
            report.AppendLine();
            
            report.AppendLine("## 测试覆盖范围");
            report.AppendLine("- 单例模式验证");
            report.AppendLine("- 传感器注册/注销");
            report.AppendLine("- 数据收集和上报");
            report.AppendLine("- 异步操作处理");
            report.AppendLine("- 事件系统验证");
            report.AppendLine("- JSON序列化测试");
            report.AppendLine("- 异常处理测试");
            report.AppendLine("- 并发操作测试");
            
            string reportPath = $"Assets/Simulation/Tests/TestReport_{System.DateTime.Now:yyyyMMdd_HHmmss}.md";
            System.IO.File.WriteAllText(reportPath, report.ToString());
            
            LogTestInfo($"测试报告已生成: {reportPath}");
            
#if UNITY_EDITOR
            AssetDatabase.Refresh();
#endif
        }
        
        private void LogTestInfo(string message)
        {
            if (enableDetailedLogging)
            {
                Debug.Log($"[TestRunner] {message}");
            }
        }
        
        private void LogTestWarning(string message)
        {
            Debug.LogWarning($"[TestRunner] {message}");
        }
        
        private void LogTestError(string message)
        {
            Debug.LogError($"[TestRunner] {message}");
        }
    }
}
