# 建筑工地数字孪生模拟系统开发指导文档

## 1. 项目架构概述

### 1.1 核心设计原则
- **简单高效，敏捷开发**：专注业务逻辑实现，避免过度工程化
- **数据优先，模拟为本**：专注于生成高质量的结构化数据
- **直接方法调用**：使用传统的方法调用，移除复杂的事件系统
- **异步编程优先**：采用异步编程模式替代传统状态机，保持业务逻辑高内聚性
- **技术精简**：基于现有Unity框架和UniTask，避免引入额外技术栈
- **渐进式开发**：先实现核心功能，后续迭代优化

### 1.2 系统架构分层

```
┌─────────────────────────────────────────────────────────────┐
│                    数字孪生系统 (外部)                        │
│                   MQTT订阅 + 数据处理                        │
└─────────────────────────────────────────────────────────────┘
                              ↑ MQTT协议
┌─────────────────────────────────────────────────────────────┐
│                    物联网系统 (IoTSystem)                     │
│              数据收集 + 处理 + MQTT发布 + 事件管理             │
└─────────────────────────────────────────────────────────────┘
                              ↑ 数据上报
┌─────────────────────────────────────────────────────────────┐
│                    传感器组件层                              │
│        位置追踪 | 身份识别 | 运动状态 | 噪声 | 扬尘           │
└─────────────────────────────────────────────────────────────┘
                              ↑ 数据获取
┌─────────────────────────────────────────────────────────────┐
│                    模拟系统层                                │
│     工人模拟 | 车辆模拟 | 设备模拟 | 环境模拟 | 门禁模拟      │
└─────────────────────────────────────────────────────────────┘
```

### 1.3 职责分离原则
- **模拟系统**：专注于工地运行模拟，生成模拟事件和状态变化
- **传感器组件系统**：独立的传感器组件，用户可自由添加和配置
- **物联网系统**：统一管理所有传感器，负责数据收集、处理和传输
- **数据传输系统**：基于MQTT协议的标准化数据传输

## 2. 核心业务模块

### 2.1 工人管理模块
**核心功能**：
- 工人行为模拟（日常行为、工作行为、工作状态）
- 施工过程模拟（不同工种、多人协作、施工进度）
- 安全违规模拟（安全装备违规、行为违规）

**异步实现策略**：
```csharp
public class WorkerSimulator : MonoBehaviour
{
    public async UniTask StartWorkDayAsync(CancellationToken cancellationToken)
    {
        await EnterWorkSiteAsync(cancellationToken);
        await PerformDailyWorkAsync(cancellationToken);
        await ExitWorkSiteAsync(cancellationToken);
    }
    
    private async UniTask PerformDailyWorkAsync(CancellationToken cancellationToken)
    {
        while (!cancellationToken.IsCancellationRequested)
        {
            await MoveToWorkLocationAsync(cancellationToken);
            await PerformWorkTaskAsync(cancellationToken);
            await RandomSafetyViolationCheckAsync(cancellationToken);
            await UniTask.Delay(TimeSpan.FromSeconds(workInterval), cancellationToken);
        }
    }
}
```

### 2.2 车辆管理模块
**核心功能**：
- 车辆基础信息管理（车辆类型、车牌号码、载重信息、驾驶员信息）
- 车辆行驶模拟（进出工地路径、工地内部行驶、装卸货物、停车等待）
- 门禁系统集成（车辆出入管理）

### 2.3 设备管理模块
**核心功能**：
- 塔吊机模拟（设备信息、运行状态、工作过程）
- 升降机模拟（设备信息、运行状态、工作过程）
- 监控摄像头模拟（设备信息、监控范围、实时画面）

### 2.4 环境监测模块
**核心功能**：
- 噪声源模拟（位置信息、噪声强度、噪声类型）
- 扬尘模拟（扬尘源位置、扬尘参数、影响因素）

**环境监测设计原则**：
- 环境模拟器监听工人、车辆、设备模拟器的事件
- 使用简单算法模拟环境影响和传播
- 避免内部随机生成事件，确保数据的可追溯性

### 2.5 气象模拟模块
**核心功能**：
- 天气系统模拟（晴天、雨天、雾天等）
- 风力系统模拟（风向、风速对扬尘的影响）

### 2.6 门禁管理模块
**核心功能**：
- 门禁设备管理（门禁点编号、位置、类型）
- 人员出入管理（身份识别、出入记录、数据统计）
- 车辆出入管理（车辆识别、通行控制、出入记录）

### 2.7 物料管理模块
**核心功能**：
- 物料存储点管理（存储区域信息、区域状态监控）
- 物料分类管理（建筑材料、施工辅料、机械配件）
- 物料出入库管理（入库管理、出库管理）

## 3. 传感器组件系统

### 3.1 传感器类型定义

#### 3.1.1 位置追踪传感器 (PositionTracker)
**功能**：统一处理位置相关数据
- 人员位置追踪
- 车辆位置追踪  
- 设备位置状态

**配置选项**：
- 位置数据：enablePositionX/Y/Z
- 旋转数据：enableRotationX/Y/Z

#### 3.1.2 身份识别传感器 (IdentitySensor)
**功能**：统一处理身份和属性信息
- 人员身份识别
- 车辆信息识别

#### 3.1.3 运动状态传感器 (MotionSensor)
**功能**：统一处理运动和工作状态数据
- 车辆速度监测
- 塔吊机工作状态
- 升降机工作状态

#### 3.1.4 噪声传感器 (NoiseSensor)
**功能**：专门处理噪声数据
- 点噪声数据采集
- 噪声等级监测

#### 3.1.5 扬尘传感器 (DustSensor)
**功能**：专门处理扬尘数据
- 点扬尘数据采集
- PM2.5和PM10监测

### 3.2 传感器组件基础架构

```csharp
public abstract class SensorComponentBase : MonoBehaviour
{
    [SerializeField] protected string sensorId;
    [SerializeField] protected float publishInterval = 1.0f;
    [SerializeField] protected bool isActive = true;
    
    protected virtual void Start()
    {
        IoTSystem.Instance?.RegisterSensor(this);
    }
    
    protected virtual void Update()
    {
        if (isActive && ShouldPublishData())
        {
            UpdateSensorData();
        }
    }
    
    public abstract void UpdateSensorData();
    
    protected virtual void ReportData(object data)
    {
        IoTSystem.Instance?.CollectSensorData(sensorId, data);
    }
}
```

## 4. 物联网系统 (IoTSystem)

### 4.1 系统职责
- **传感器管理**：统一管理所有传感器组件的注册、配置和状态监控
- **数据收集**：从各个传感器组件收集数据
- **数据处理**：对传感器数据进行预处理、验证和格式化
- **事件管理**：统一管理和发布来自各个模拟器的事件通知
- **数据传输**：通过MQTT协议统一发布数据和事件

### 4.2 核心接口设计

```csharp
public class IoTSystem : MonoBehaviour
{
    public static IoTSystem Instance { get; private set; }
    
    // 传感器管理
    public void RegisterSensor(ISensorComponent sensor);
    public void UnregisterSensor(string sensorId);
    
    // 数据收集
    public void CollectSensorData(string sensorId, object data);
    
    // 事件管理
    public void SendIOTEvent(string sourceId, string eventType, string title, string description, object data);
    
    // 连接管理
    public void StartDataTransmission();
    public void StopDataTransmission();
    public bool IsConnected { get; private set; }
}
```

## 5. MQTT数据传输系统

### 5.1 技术选型
- **MQTT客户端**：使用BestMQTT插件作为底层MQTT通信实现
- **协议版本**：支持MQTT 3.1.1和5.0
- **连接管理**：内置连接管理和自动重连机制
- **安全性**：支持TLS/SSL加密连接

### 5.2 主题命名规范
- **位置数据**：`sensors/position/{targetType}/{targetId}`
- **身份数据**：`sensors/identity/{targetType}/{targetId}`
- **运动数据**：`sensors/motion/{targetType}/{targetId}`
- **噪声数据**：`sensors/noise/{sensorId}`
- **扬尘数据**：`sensors/dust/{sensorId}`
- **系统状态**：`system/status/{componentId}`
- **事件通知**：`events/{eventType}/{sourceId}`

### 5.3 QoS等级设置
- **实时数据**：QoS 0 (最多一次传递)
- **重要事件**：QoS 1 (至少一次传递)
- **关键状态**：QoS 2 (恰好一次传递)

## 6. 异步编程实施指南

### 6.1 异步编程优势
- **逻辑聚合性提升**：将复杂的多步骤操作组织在单一异步方法中
- **代码复杂度简化**：消除复杂的状态管理和状态转换逻辑
- **维护性增强**：业务流程变更只需在一个异步方法中进行

### 6.2 实施策略
- **直接实现**：直接使用异步编程模式，不使用复杂的状态机
- **统一开发规范**：建立简化的异步编程开发指导原则

### 6.3 异步编程模式示例

```csharp
public class VehicleSimulator : MonoBehaviour
{
    public async UniTask StartTransportMissionAsync(CancellationToken cancellationToken)
    {
        await EnterWorkSiteAsync(cancellationToken);
        await MoveToLoadingAreaAsync(cancellationToken);
        await LoadMaterialsAsync(cancellationToken);
        await TransportToDestinationAsync(cancellationToken);
        await UnloadMaterialsAsync(cancellationToken);
        await ExitWorkSiteAsync(cancellationToken);
    }
}
```

## 7. 开发实施流程

### 7.1 开发阶段划分
1. **基础架构搭建**：IoTSystem、传感器基类、MQTT集成
2. **核心模拟器开发**：工人、车辆、设备模拟器
3. **传感器组件开发**：各类传感器组件实现
4. **环境系统开发**：噪声、扬尘、气象模拟
5. **门禁物料系统**：门禁管理、物料管理
6. **数据导出系统**：统一数据收集和导出
7. **系统集成测试**：端到端测试和性能优化

### 7.2 质量保证
- **单元测试**：每个模块都需要编写对应的单元测试
- **集成测试**：模块间集成测试，确保数据流正确
- **性能测试**：异步操作性能测试，确保系统稳定性
- **数据验证**：传感器数据格式和内容验证

### 7.3 部署和维护
- **配置管理**：通过Unity Inspector面板进行可视化配置
- **监控机制**：系统运行状态监控和日志记录
- **错误处理**：完善的错误处理和恢复机制
- **文档维护**：保持开发文档和API文档的及时更新
