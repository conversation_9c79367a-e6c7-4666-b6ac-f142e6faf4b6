using UnityEngine;
using Cysharp.Threading.Tasks;
using System.Threading;
using System;

namespace Simulation.Utils
{
    /// <summary>
    /// UniTask功能测试脚本
    /// 用于验证UniTask是否正确安装和配置
    /// </summary>
    public class UniTaskTest : MonoBehaviour
    {
        [Header("测试配置")]
        [SerializeField] private bool autoStartTest = true;
        [SerializeField] private float delaySeconds = 2.0f;
        
        private CancellationTokenSource cancellationTokenSource;
        
        private void Start()
        {
            if (autoStartTest)
            {
                StartUniTaskTest();
            }
        }
        
        private void OnDestroy()
        {
            cancellationTokenSource?.Cancel();
            cancellationTokenSource?.Dispose();
        }
        
        /// <summary>
        /// 开始UniTask测试
        /// </summary>
        [ContextMenu("Start UniTask Test")]
        public void StartUniTaskTest()
        {
            cancellationTokenSource = new CancellationTokenSource();
            TestUniTaskAsync(cancellationTokenSource.Token).Forget();
        }
        
        /// <summary>
        /// 停止UniTask测试
        /// </summary>
        [ContextMenu("Stop UniTask Test")]
        public void StopUniTaskTest()
        {
            cancellationTokenSource?.Cancel();
        }
        
        /// <summary>
        /// 异步测试方法
        /// </summary>
        private async UniTaskVoid TestUniTaskAsync(CancellationToken cancellationToken)
        {
            try
            {
                Debug.Log("[UniTaskTest] 开始UniTask功能测试...");
                
                // 测试1: 基础延迟功能
                Debug.Log("[UniTaskTest] 测试1: 基础延迟功能");
                await UniTask.Delay(TimeSpan.FromSeconds(delaySeconds), cancellationToken: cancellationToken);
                Debug.Log($"[UniTaskTest] ✓ 延迟 {delaySeconds} 秒完成");
                
                // 测试2: 帧延迟功能
                Debug.Log("[UniTaskTest] 测试2: 帧延迟功能");
                await UniTask.DelayFrame(60, cancellationToken: cancellationToken);
                Debug.Log("[UniTaskTest] ✓ 延迟 60 帧完成");
                
                // 测试3: 异步循环
                Debug.Log("[UniTaskTest] 测试3: 异步循环");
                for (int i = 0; i < 3; i++)
                {
                    Debug.Log($"[UniTaskTest] 循环第 {i + 1} 次");
                    await UniTask.Delay(500, cancellationToken: cancellationToken);
                }
                Debug.Log("[UniTaskTest] ✓ 异步循环完成");
                
                // 测试4: 并行任务
                Debug.Log("[UniTaskTest] 测试4: 并行任务");
                var task1 = DelayedLogAsync("任务1", 1000, cancellationToken);
                var task2 = DelayedLogAsync("任务2", 1500, cancellationToken);
                var task3 = DelayedLogAsync("任务3", 800, cancellationToken);
                
                await UniTask.WhenAll(task1, task2, task3);
                Debug.Log("[UniTaskTest] ✓ 并行任务完成");
                
                Debug.Log("[UniTaskTest] ✅ 所有UniTask功能测试通过！");
            }
            catch (OperationCanceledException)
            {
                Debug.Log("[UniTaskTest] ⚠️ 测试被取消");
            }
            catch (Exception ex)
            {
                Debug.LogError($"[UniTaskTest] ❌ 测试失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 延迟日志输出的异步方法
        /// </summary>
        private async UniTask DelayedLogAsync(string taskName, int delayMs, CancellationToken cancellationToken)
        {
            await UniTask.Delay(delayMs, cancellationToken: cancellationToken);
            Debug.Log($"[UniTaskTest] {taskName} 完成 (延迟 {delayMs}ms)");
        }
    }
}
