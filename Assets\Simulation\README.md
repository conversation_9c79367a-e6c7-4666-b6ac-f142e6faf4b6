# 建筑工地数字孪生模拟系统

## 项目目录结构

```
Assets/Simulation/
├── Config/                     # 配置文件目录
├── Docs/                       # 项目文档目录
│   ├── 需求文档.md
│   ├── 开发指导.md
│   ├── 项目业务逻辑.md
│   └── 异步编程参考规范.md
├── Prefabs/                    # 预制体目录
│   ├── Sensors/               # 传感器预制体
│   └── Simulators/            # 模拟器预制体
├── Resources/                  # 资源文件目录
├── Scenes/                     # 场景文件目录
└── Scripts/                    # 脚本文件目录
    ├── IoTSystem/             # IoT系统模块
    │   ├── Core/              # 核心功能
    │   ├── Data/              # 数据处理
    │   └── MQTT/              # MQTT通信
    ├── Sensors/               # 传感器组件模块
    │   ├── Position/          # 位置追踪传感器
    │   ├── Identity/          # 身份识别传感器
    │   ├── Motion/            # 运动状态传感器
    │   ├── Noise/             # 噪声传感器
    │   └── Dust/              # 扬尘传感器
    ├── Simulators/            # 模拟器模块
    │   ├── Worker/            # 工人模拟器
    │   ├── Vehicle/           # 车辆模拟器
    │   ├── Equipment/         # 设备模拟器
    │   ├── Environment/       # 环境模拟器
    │   ├── Access/            # 门禁模拟器
    │   └── Material/          # 物料模拟器
    └── Utils/                 # 工具类模块
        ├── Constants/         # 常量定义
        ├── Extensions/        # 扩展方法
        └── Helpers/           # 辅助工具
```

## 模块说明

### IoTSystem 模块
- **Core**: IoTSystem单例、传感器管理、事件管理
- **Data**: 数据格式化、验证、处理
- **MQTT**: BestMQTT集成、连接管理、消息发布

### Sensors 模块
- **Position**: 位置追踪传感器，获取位置和旋转信息
- **Identity**: 身份识别传感器，获取身份和属性信息
- **Motion**: 运动状态传感器，获取速度和工作状态
- **Noise**: 噪声传感器，获取环境噪声数据
- **Dust**: 扬尘传感器，获取环境扬尘数据

### Simulators 模块
- **Worker**: 工人模拟器，模拟日常作业和安全违规
- **Vehicle**: 车辆模拟器，模拟运输任务和状态监控
- **Equipment**: 设备模拟器，模拟塔吊、升降机等设备
- **Environment**: 环境模拟器，监听事件并生成环境数据
- **Access**: 门禁模拟器，模拟出入管理（仅识别记录）
- **Material**: 物料模拟器，模拟出入库和库存管理

### Utils 模块
- **Constants**: 系统常量、配置参数、MQTT主题定义
- **Extensions**: Unity扩展方法、异步扩展
- **Helpers**: 数据转换、时间处理、随机数生成等工具

## 开发原则

1. **简单高效，敏捷开发**：专注业务逻辑，避免过度工程化
2. **异步编程优先**：使用UniTask实现异步业务流程
3. **职责分离**：模拟系统、传感器、IoT系统各司其职
4. **模块化设计**：每个模块独立开发，便于维护和扩展

## 技术栈

- **Unity Engine**: 3D模拟环境
- **UniTask**: 异步编程框架
- **BestMQTT**: MQTT通信插件
- **C# 7.0+**: 支持异步编程特性
