<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AnalysisUIOptions">
    <option name="SCOPE_TYPE" value="3" />
  </component>
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="8dfa6a06-767a-4291-b9b4-6d25cce73568" name="Changes" comment="">
      <change afterPath="$PROJECT_DIR$/Assets/GeneralEditor/Scripts/Components/GeneralEnviroManager.cs" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/Assets/GeneralEditor/Scripts/Components/GeneralEnviroManager.cs.meta" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/AddressableAssetsData/AddressableAssetSettings.asset" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/AddressableAssetsData/AddressableAssetSettings.asset" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/AddressableAssetsData/AssetGroups/Default Local Group.asset" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/AddressableAssetsData/AssetGroups/Default Local Group.asset" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/AddressableAssetsData/AssetGroups/FactoryModel.asset" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/AddressableAssetsData/AssetGroups/FactoryModel.asset" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/AddressableAssetsData/AssetGroups/Schemas/Default Local Group_BundledAssetGroupSchema.asset" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/AddressableAssetsData/AssetGroups/Schemas/Default Local Group_BundledAssetGroupSchema.asset" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/AddressableAssetsData/Windows/addressables_content_state.bin" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/AddressableAssetsData/Windows/addressables_content_state.bin" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/1 UniversalRP Support.unitypackage" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/1 UniversalRP Support.unitypackage" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/1 UniversalRP Support.unitypackage.meta" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/1 UniversalRP Support.unitypackage.meta" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/2 RTEditor Demo.unitypackage" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/2 RTEditor Demo.unitypackage" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/2 RTEditor Demo.unitypackage.meta" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/2 RTEditor Demo.unitypackage.meta" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/3 RTEditor Scripting (Jint).unitypackage" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/3 RTEditor Scripting (Jint).unitypackage" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/3 RTEditor Scripting (Jint).unitypackage.meta" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/3 RTEditor Scripting (Jint).unitypackage.meta" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/3 RTEditor Scripting Demo (Jint).unitypackage" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/3 RTEditor Scripting Demo (Jint).unitypackage" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/3 RTEditor Scripting Demo (Jint).unitypackage.meta" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/3 RTEditor Scripting Demo (Jint).unitypackage.meta" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/4 RTEditor Scripting (Roslyn).unitypackage" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/4 RTEditor Scripting (Roslyn).unitypackage" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/4 RTEditor Scripting (Roslyn).unitypackage.meta" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/4 RTEditor Scripting (Roslyn).unitypackage.meta" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/4 RTEditor Scripting Demo (Roslyn).unitypackage" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/4 RTEditor Scripting Demo (Roslyn).unitypackage" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/4 RTEditor Scripting Demo (Roslyn).unitypackage.meta" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/4 RTEditor Scripting Demo (Roslyn).unitypackage.meta" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/6 HDRP Support.unitypackage" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/6 HDRP Support.unitypackage" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/6 HDRP Support.unitypackage.meta" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/6 HDRP Support.unitypackage.meta" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/LoadImageAsync/Runtime/ImageUtils.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/LoadImageAsync/Runtime/ImageUtils.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Content/Runtime/RTCommon/Prefabs/Camera.prefab" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Content/Runtime/RTCommon/Prefabs/Camera.prefab" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Content/Runtime/RTEditor/BuitInMenu/GameObjects/Camera.prefab" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Content/Runtime/RTEditor/BuitInMenu/GameObjects/Camera.prefab" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Content/Runtime/RTEditor/BuitInMenu/GameObjects/Light/Directional Light.prefab" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Content/Runtime/RTEditor/BuitInMenu/GameObjects/Light/Directional Light.prefab" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Content/Runtime/RTEditor/Prefabs/Editors/GameObjectEditor.prefab" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Content/Runtime/RTEditor/Prefabs/Editors/GameObjectEditor.prefab" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Content/Runtime/RTEditor/Prefabs/Mobile/Controls/MobileAssetDatabaseTreeViewItem.prefab" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Content/Runtime/RTEditor/Prefabs/Mobile/Controls/MobileAssetDatabaseTreeViewItem.prefab" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Content/Runtime/RTEditor/Prefabs/Mobile/Controls/MobileAssetDatabaseVirtualizingListBoxItem.prefab" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Content/Runtime/RTEditor/Prefabs/Mobile/Controls/MobileAssetDatabaseVirtualizingListBoxItem.prefab" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Content/Runtime/RTEditor/Prefabs/Mobile/Controls/MobileHierarchyTreeViewItem.prefab" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Content/Runtime/RTEditor/Prefabs/Mobile/Controls/MobileHierarchyTreeViewItem.prefab" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Content/Runtime/RTEditor/Prefabs/Models/AssetDatabaseModel.prefab" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Content/Runtime/RTEditor/Prefabs/Models/AssetDatabaseModel.prefab" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Content/Runtime/RTEditor/Prefabs/RuntimeEditor.prefab" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Content/Runtime/RTEditor/Prefabs/RuntimeEditor.prefab" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Content/Runtime/RTEditor/Prefabs/Views/AnimationView.prefab" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Content/Runtime/RTEditor/Prefabs/Views/AnimationView.prefab" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Content/Runtime/RTEditor/Prefabs/Views/AssetDatabaseView.prefab" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Content/Runtime/RTEditor/Prefabs/Views/AssetDatabaseView.prefab" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Content/Runtime/RTEditor/Prefabs/Views/Dialogs/AnimationSelectPropertiesDialog.prefab" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Content/Runtime/RTEditor/Prefabs/Views/Dialogs/AnimationSelectPropertiesDialog.prefab" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Content/Runtime/RTEditor/Prefabs/Views/Dialogs/AssetDatabaseImportDialog.prefab" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Content/Runtime/RTEditor/Prefabs/Views/Dialogs/AssetDatabaseImportDialog.prefab" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Content/Runtime/RTEditor/Prefabs/Views/Dialogs/AssetDatabaseImportSourceDialog.prefab" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Content/Runtime/RTEditor/Prefabs/Views/Dialogs/AssetDatabaseImportSourceDialog.prefab" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Content/Runtime/RTEditor/Prefabs/Views/Dialogs/AssetDatabaseSelectDialog.prefab" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Content/Runtime/RTEditor/Prefabs/Views/Dialogs/AssetDatabaseSelectDialog.prefab" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Content/Runtime/RTEditor/Prefabs/Views/Dialogs/SelectColorDialog.prefab" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Content/Runtime/RTEditor/Prefabs/Views/Dialogs/SelectColorDialog.prefab" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Content/Runtime/RTEditor/Prefabs/Views/HierarchyView.prefab" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Content/Runtime/RTEditor/Prefabs/Views/HierarchyView.prefab" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Content/Runtime/RTEditor/Prefabs/Views/SceneView.prefab" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Content/Runtime/RTEditor/Prefabs/Views/SceneView.prefab" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Content/Runtime/RTEditor/Resources/Themes/Dark.asset" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Content/Runtime/RTEditor/Resources/Themes/Dark.asset" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Content/Runtime/RTEditor/Resources/Themes/Light.asset" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Content/Runtime/RTEditor/Resources/Themes/Light.asset" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Content/Runtime/RTHandles/Prefabs/Models/Materials/SSQuadMat.mat" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Content/Runtime/RTHandles/Prefabs/Models/Materials/SSQuadMat.mat" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Editor/RTCommon/RTEEditor.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Editor/RTCommon/RTEEditor.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Runtime/RTCommon/DragDropTarget.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Runtime/RTCommon/DragDropTarget.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Runtime/RTCommon/Graphics/RenderersCache.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Runtime/RTCommon/Graphics/RenderersCache.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Runtime/RTCommon/RTE.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Runtime/RTCommon/RTE.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Runtime/RTCommon/RTEBase.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Runtime/RTCommon/RTEBase.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Runtime/RTCommon/RTESceneWindow.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Runtime/RTCommon/RTESceneWindow.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Runtime/RTCommon/RTEVersion.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Runtime/RTCommon/RTEVersion.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Runtime/RTCommon/RuntimeCameraWindow.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Runtime/RTCommon/RuntimeCameraWindow.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Runtime/RTCommon/RuntimeWindow.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Runtime/RTCommon/RuntimeWindow.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Runtime/RTEditor/Editors/ComponentDescriptors/UI/CanvasComponentDescriptor.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Runtime/RTEditor/Editors/ComponentDescriptors/UI/CanvasComponentDescriptor.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Runtime/RTEditor/Editors/PropertyEditors/CustomTypeEditor.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Runtime/RTEditor/Editors/PropertyEditors/CustomTypeEditor.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Runtime/RTEditor/Editors/PropertyEditors/DragField.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Runtime/RTEditor/Editors/PropertyEditors/DragField.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Runtime/RTEditor/Editors/PropertyEditors/EnumEditor.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Runtime/RTEditor/Editors/PropertyEditors/EnumEditor.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Runtime/RTEditor/Legacy/Dialogs/SelectObjectDialog.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Runtime/RTEditor/Legacy/Dialogs/SelectObjectDialog.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Runtime/RTEditor/Legacy/Views/GameView.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Runtime/RTEditor/Legacy/Views/GameView.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Runtime/RTEditor/Mobile/MobileContextMenu.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Runtime/RTEditor/Mobile/MobileContextMenu.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Runtime/RTEditor/Mobile/Models/MobileEditorModel.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Runtime/RTEditor/Mobile/Models/MobileEditorModel.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Runtime/RTEditor/Mobile/Views/MobileHeaderView.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Runtime/RTEditor/Mobile/Views/MobileHeaderView.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Runtime/RTEditor/Models/AssetDatabaseImportSources/IAssetDatabaseImportSourceModel.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Runtime/RTEditor/Models/AssetDatabaseImportSources/IAssetDatabaseImportSourceModel.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Runtime/RTEditor/Models/AssetDatabaseModel.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Runtime/RTEditor/Models/AssetDatabaseModel.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Runtime/RTEditor/Models/IAssetDatabaseModel.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Runtime/RTEditor/Models/IAssetDatabaseModel.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Runtime/RTEditor/Models/Legacy/AssetDatabaseOverRTSLModel.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Runtime/RTEditor/Models/Legacy/AssetDatabaseOverRTSLModel.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Runtime/RTEditor/RTEAppearance.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Runtime/RTEditor/RTEAppearance.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Runtime/RTEditor/RuntimeEditor.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Runtime/RTEditor/RuntimeEditor.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Runtime/RTEditor/RuntimeGameWindow.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Runtime/RTEditor/RuntimeGameWindow.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Runtime/RTEditor/SettingsComponent.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Runtime/RTEditor/SettingsComponent.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Runtime/RTEditor/ViewModels/AssetDatabaseSelectViewModel.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Runtime/RTEditor/ViewModels/AssetDatabaseSelectViewModel.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Runtime/RTEditor/ViewModels/AssetDatabaseViewModel.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Runtime/RTEditor/ViewModels/AssetDatabaseViewModel.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Runtime/RTEditor/ViewModels/ConsoleViewModel.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Runtime/RTEditor/ViewModels/ConsoleViewModel.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Runtime/RTEditor/ViewModels/HierarchyViewModel.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Runtime/RTEditor/ViewModels/HierarchyViewModel.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Runtime/RTEditor/ViewModels/Legacy/ProjectViewModel.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Runtime/RTEditor/ViewModels/Legacy/ProjectViewModel.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Runtime/RTEditor/ViewModels/Legacy/SelectObjectViewModel.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Runtime/RTEditor/ViewModels/Legacy/SelectObjectViewModel.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Runtime/RTEditor/ViewModels/SceneViewModel.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Runtime/RTEditor/ViewModels/SceneViewModel.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Runtime/RTEditor/Workspace.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Runtime/RTEditor/Workspace.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Runtime/RTGizmos/GizmoUtility.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Runtime/RTGizmos/GizmoUtility.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Runtime/RTGizmos/GizmoUtility.cs.meta" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Runtime/RTGizmos/GizmoUtility.cs.meta" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Runtime/RTHandles/BoxSelection.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Runtime/RTHandles/BoxSelection.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Runtime/RTHandles/Mobile/MobileSceneControls.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Runtime/RTHandles/Mobile/MobileSceneControls.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Runtime/RTHandles/Mobile/MobileSceneInput.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Runtime/RTHandles/Mobile/MobileSceneInput.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Runtime/RTHandles/SceneGrid.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Runtime/RTHandles/SceneGrid.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Runtime/RTSL/Interface/IStorageAsync.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Runtime/RTSL/Interface/IStorageAsync.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Runtime/RTSL/Interface/RTSLVersion.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Runtime/RTSL/Interface/RTSLVersion.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Runtime/RTSL/RTSLDeps.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Runtime/RTSL/RTSLDeps.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Runtime/RTSL/RTSLDeps.cs.meta" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Runtime/RTSL/RTSLDeps.cs.meta" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Runtime/UIControls/VirtualizingTreeView/UnityWeld/VirtualizingTreeViewBinding.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Runtime/UIControls/VirtualizingTreeView/UnityWeld/VirtualizingTreeViewBinding.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Runtime/UIControls/VirtualizingTreeView/VirtualizingItemContainer.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Runtime/UIControls/VirtualizingTreeView/VirtualizingItemContainer.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Runtime/UIControls/VirtualizingTreeView/VirtualizingItemsControl.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Runtime/UIControls/VirtualizingTreeView/VirtualizingItemsControl.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Runtime/UIControls/VirtualizingTreeView/VirtualizingTreeView.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/Runtime/UIControls/VirtualizingTreeView/VirtualizingTreeView.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/ThirdParty/HSVPicker/Editor/BoxSliderEditor.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/ThirdParty/HSVPicker/Editor/BoxSliderEditor.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/ThirdParty/HSVPicker/Enums/ColorValues.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/ThirdParty/HSVPicker/Enums/ColorValues.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/ThirdParty/HSVPicker/Events/ColorChangedEvent.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/ThirdParty/HSVPicker/Events/ColorChangedEvent.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/ThirdParty/HSVPicker/Events/HSVChangedEvent.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/ThirdParty/HSVPicker/Events/HSVChangedEvent.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/ThirdParty/HSVPicker/Other/TiltWindow.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/ThirdParty/HSVPicker/Other/TiltWindow.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/ThirdParty/HSVPicker/UI/ColorLabel.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/ThirdParty/HSVPicker/UI/ColorLabel.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/ThirdParty/HSVPicker/UtilityScripts/BoxSlider.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/ThirdParty/HSVPicker/UtilityScripts/BoxSlider.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/ThirdParty/HSVPicker/UtilityScripts/HSVUtil.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/ThirdParty/HSVPicker/UtilityScripts/HSVUtil.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/ThirdParty/UIShapesKit/Editor/CustomDrawers/EllipsePropertiesDrawer.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/ThirdParty/UIShapesKit/Editor/CustomDrawers/EllipsePropertiesDrawer.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/ThirdParty/UIShapesKit/Editor/CustomDrawers/LinePropertiesDrawer.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/ThirdParty/UIShapesKit/Editor/CustomDrawers/LinePropertiesDrawer.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/ThirdParty/UIShapesKit/Editor/CustomDrawers/OutlineShapePropertiesDrawer.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/ThirdParty/UIShapesKit/Editor/CustomDrawers/OutlineShapePropertiesDrawer.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/ThirdParty/UIShapesKit/Editor/CustomDrawers/PointListDrawer.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/ThirdParty/UIShapesKit/Editor/CustomDrawers/PointListDrawer.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/ThirdParty/UIShapesKit/Editor/CustomDrawers/PointListPropertiesDrawer.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/ThirdParty/UIShapesKit/Editor/CustomDrawers/PointListPropertiesDrawer.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/ThirdParty/UIShapesKit/Editor/CustomDrawers/PolygonPropertiesDrawer.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/ThirdParty/UIShapesKit/Editor/CustomDrawers/PolygonPropertiesDrawer.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/ThirdParty/UIShapesKit/Editor/CustomDrawers/RoundedPropertiesDrawer.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/ThirdParty/UIShapesKit/Editor/CustomDrawers/RoundedPropertiesDrawer.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/ThirdParty/UIShapesKit/Editor/CustomDrawers/RoundingPropertiesDrawer.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/ThirdParty/UIShapesKit/Editor/CustomDrawers/RoundingPropertiesDrawer.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/ThirdParty/UIShapesKit/Editor/CustomDrawers/ShadowsPropertiesDrawer.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/ThirdParty/UIShapesKit/Editor/CustomDrawers/ShadowsPropertiesDrawer.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/ThirdParty/UIShapesKit/Editor/CustomDrawers/ShapePropertiesDrawer.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/ThirdParty/UIShapesKit/Editor/CustomDrawers/ShapePropertiesDrawer.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/ThirdParty/UIShapesKit/Editor/Editors/ArcEditor.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/ThirdParty/UIShapesKit/Editor/Editors/ArcEditor.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/ThirdParty/UIShapesKit/Editor/Editors/EdgeGradientEditor.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/ThirdParty/UIShapesKit/Editor/Editors/EdgeGradientEditor.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/ThirdParty/UIShapesKit/Editor/Editors/EllipseEditor.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/ThirdParty/UIShapesKit/Editor/Editors/EllipseEditor.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/ThirdParty/UIShapesKit/Editor/Editors/EmptyFillRectEditor.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/ThirdParty/UIShapesKit/Editor/Editors/EmptyFillRectEditor.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/ThirdParty/UIShapesKit/Editor/Editors/LineEditor.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/ThirdParty/UIShapesKit/Editor/Editors/LineEditor.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/ThirdParty/UIShapesKit/Editor/Editors/PixelLineEditor.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/ThirdParty/UIShapesKit/Editor/Editors/PixelLineEditor.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/ThirdParty/UIShapesKit/Editor/Editors/PolygonEditor.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/ThirdParty/UIShapesKit/Editor/Editors/PolygonEditor.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/ThirdParty/UIShapesKit/Editor/Editors/RectangleEditor.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/ThirdParty/UIShapesKit/Editor/Editors/RectangleEditor.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/ThirdParty/UIShapesKit/Editor/Editors/SectorEditor.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/ThirdParty/UIShapesKit/Editor/Editors/SectorEditor.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/ThirdParty/UIShapesKit/Editor/MenuItems.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/ThirdParty/UIShapesKit/Editor/MenuItems.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/ThirdParty/UIShapesKit/Editor/ValueSetterEditor.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/ThirdParty/UIShapesKit/Editor/ValueSetterEditor.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/ThirdParty/UIShapesKit/Editor/ValueSetterEditor2.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/ThirdParty/UIShapesKit/Editor/ValueSetterEditor2.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/package.json" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditor/package.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditorDemo/Editor/RTEditor/RTHandles/RTHandlesMenu.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditorDemo/Editor/RTEditor/RTHandles/RTHandlesMenu.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditorDemo/Examples/Scene5 - Layout SaveLoad.unity" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditorDemo/Examples/Scene5 - Layout SaveLoad.unity" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditorDemo/Examples/Scene6 - Custom Window.unity" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditorDemo/Examples/Scene6 - Custom Window.unity" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditorDemo/Examples/Scene6 - Custom Window/CustomExampleWindow.prefab" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditorDemo/Examples/Scene6 - Custom Window/CustomExampleWindow.prefab" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditorDemo/Examples/Scene7 - Custom  Dialog.unity" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditorDemo/Examples/Scene7 - Custom  Dialog.unity" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditorDemo/Examples/Scene7 - Custom Dialog/CustomExampleDialog.prefab" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditorDemo/Examples/Scene7 - Custom Dialog/CustomExampleDialog.prefab" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditorDemo/Examples/Scene8 - Window Management.unity" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditorDemo/Examples/Scene8 - Window Management.unity" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditorDemo/package.json" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditorDemo/package.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditorURP/Runtime/RTCommon/Legacy/ExecuteCommandBuffers.cs.meta" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditorURP/Runtime/RTCommon/Legacy/ExecuteCommandBuffers.cs.meta" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditorURP/package.json" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditorURP/package.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditor_Data/Prefabs/Editors/Resources/Battlehub_EditorsMapAuto.prefab" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditor_Data/Prefabs/Editors/Resources/Battlehub_EditorsMapAuto.prefab" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTEditor_Data/Scripts/Editors/EditorsMapCreator.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTEditor_Data/Scripts/Editors/EditorsMapCreator.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTExtensions/Content/Runtime/RTBuilder/Prefabs/ProBuilderView.prefab" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTExtensions/Content/Runtime/RTBuilder/Prefabs/ProBuilderView.prefab" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTExtensions/Content/Runtime/RTTerrain/Prefabs/TerrainComponentEditor.prefab" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTExtensions/Content/Runtime/RTTerrain/Prefabs/TerrainComponentEditor.prefab" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTExtensions/Runtime/RTTerrain/Tools/TerrainAreaHandle.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTExtensions/Runtime/RTTerrain/Tools/TerrainAreaHandle.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTExtensions/package.json" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTExtensions/package.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTExtensionsURP/Content/Runtime/RTBuilder/Data/SelectionPickerRenderer.asset" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTExtensionsURP/Content/Runtime/RTBuilder/Data/SelectionPickerRenderer.asset" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTExtensionsURP/package.json" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTExtensionsURP/package.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTImporter/package.json" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTImporter/package.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTScripting.Common/Content/FontFiles/Noto/Noto Mono/NotoMono-Regular SDF.asset" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTScripting.Common/Content/FontFiles/Noto/Noto Mono/NotoMono-Regular SDF.asset" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/RTScripting.Roslyn/package.json" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/RTScripting.Roslyn/package.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/Storage.Addressables/package.json" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/Storage.Addressables/package.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/Storage.Core/Runtime/DynamicSurrogate.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/Storage.Core/Runtime/DynamicSurrogate.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/Storage.Core/Runtime/Interfaces/IIDMap.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/Storage.Core/Runtime/Interfaces/IIDMap.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/Storage.Core/Runtime/RuntimeAssetDatabaseCore.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/Storage.Core/Runtime/RuntimeAssetDatabaseCore.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/Storage.Core/package.json" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/Storage.Core/package.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/Storage.ShaderUtil/package.json" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/Storage.ShaderUtil/package.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/Storage/Editor/TypeModelBuilder.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/Storage/Editor/TypeModelBuilder.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/Storage/Runtime/RuntimeAssetDatabase.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/Storage/Runtime/RuntimeAssetDatabase.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/Storage/package.json" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/Storage/package.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/StorageData/ExternalAssetList.asset" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/StorageData/ExternalAssetList.asset" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/StorageData/Generated/ObjectEnumeratorFactory.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/StorageData/Generated/ObjectEnumeratorFactory.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/StorageData/Generated/Protobuf/Serializer.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/StorageData/Generated/Protobuf/Serializer.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/StorageData/Generated/Resources/ShaderProfiles.asset" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/StorageData/Generated/Resources/ShaderProfiles.asset" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/StorageData/Generated/StorageTypeModel.dll" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/StorageData/Generated/StorageTypeModel.dll" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/StorageData/Surrogates/Editor.meta" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/StorageData/Surrogates/Editor.meta" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/StorageData/Surrogates/Enumerators/UnityEngine.TerrainDataEnumerator.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/StorageData/Surrogates/Enumerators/UnityEngine.TerrainDataEnumerator.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/StorageData/Surrogates/UnityEngine.TerrainDataSurrogate.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/StorageData/Surrogates/UnityEngine.TerrainDataSurrogate.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/StorageData/Surrogates/UnityEngine.Texture2DSurrogate.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/StorageData/Surrogates/UnityEngine.Texture2DSurrogate.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Battlehub/StorageData/Surrogates/UnityEngine.TreePrototypeSurrogate.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Battlehub/StorageData/Surrogates/UnityEngine.TreePrototypeSurrogate.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/BuiltInAssets/HDRIHaven/skies_4k_HDRIs/Materials/kloppenheim_03_4k.mat" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/BuiltInAssets/HDRIHaven/skies_4k_HDRIs/Materials/kloppenheim_03_4k.mat" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/BuiltInAssets/HDRIHaven/skies_4k_HDRIs/Materials/table_mountain_2_4k.mat" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/BuiltInAssets/HDRIHaven/skies_4k_HDRIs/Materials/table_mountain_2_4k.mat" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/BuiltInAssets/Terrain Textures/Ground001.tif.meta" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/BuiltInAssets/Terrain Textures/Ground001.tif.meta" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/BuiltInAssets/Terrain Textures/Ground002.tif.meta" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/BuiltInAssets/Terrain Textures/Ground002.tif.meta" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/BuiltInAssets/Terrain Textures/Ground003.tif.meta" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/BuiltInAssets/Terrain Textures/Ground003.tif.meta" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/BuiltInAssets/Terrain Textures/Ground004.tif.meta" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/BuiltInAssets/Terrain Textures/Ground004.tif.meta" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/BuiltInAssets/Terrain Textures/Ground005.tif.meta" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/BuiltInAssets/Terrain Textures/Ground005.tif.meta" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/BuiltInAssets/Terrain Textures/GroundPatchCracked01.tif.meta" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/BuiltInAssets/Terrain Textures/GroundPatchCracked01.tif.meta" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Enviro 3 - Sky and Weather/Profiles/Configurations/Default Enviro Configuration 3_1_7.asset" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Enviro 3 - Sky and Weather/Profiles/Configurations/Default Enviro Configuration 3_1_7.asset" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Enviro 3 - Sky and Weather/Profiles/Weather Types/Cloudy 2.asset" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Enviro 3 - Sky and Weather/Profiles/Weather Types/Cloudy 2.asset" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Enviro 3 - Sky and Weather/Scripts/Runtime/Modules/Effects/Preset/Default Effects Preset.asset" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Enviro 3 - Sky and Weather/Scripts/Runtime/Modules/Effects/Preset/Default Effects Preset.asset" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Enviro 3 - Sky and Weather/Scripts/Runtime/Modules/FlatClouds/Preset/Default Flat Clouds Preset.asset" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Enviro 3 - Sky and Weather/Scripts/Runtime/Modules/FlatClouds/Preset/Default Flat Clouds Preset.asset" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Enviro 3 - Sky and Weather/Scripts/Runtime/Modules/Fog/Preset/Default Fog Preset.asset" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Enviro 3 - Sky and Weather/Scripts/Runtime/Modules/Fog/Preset/Default Fog Preset.asset" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Enviro 3 - Sky and Weather/Scripts/Runtime/Modules/Lighting/Preset/Default Lighting Preset.asset" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Enviro 3 - Sky and Weather/Scripts/Runtime/Modules/Lighting/Preset/Default Lighting Preset.asset" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Enviro 3 - Sky and Weather/Scripts/Runtime/Modules/Lightning/Preset/Default Lightning Preset.asset" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Enviro 3 - Sky and Weather/Scripts/Runtime/Modules/Lightning/Preset/Default Lightning Preset.asset" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Enviro 3 - Sky and Weather/Scripts/Runtime/Modules/Sky/Preset/Default Sky Preset.asset" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Enviro 3 - Sky and Weather/Scripts/Runtime/Modules/Sky/Preset/Default Sky Preset.asset" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Enviro 3 - Sky and Weather/Scripts/Runtime/Modules/VolumetricClouds/Preset/Default Volumetric Clouds Preset.asset" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Enviro 3 - Sky and Weather/Scripts/Runtime/Modules/VolumetricClouds/Preset/Default Volumetric Clouds Preset.asset" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Fonts/7000常用字.txt" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Fonts/7000常用字.txt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/GeneralEditor/Config/GameObjects.asset" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/GeneralEditor/Config/GameObjects.asset" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/GeneralEditor/Prefabs/Camera Variant.prefab" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/GeneralEditor/Prefabs/Camera Variant.prefab" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/GeneralEditor/Resources/HighQuality_UniversalRenderPipelineAsset.asset" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/GeneralEditor/Resources/HighQuality_UniversalRenderPipelineAsset.asset" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/GeneralEditor/Resources/LowQuality_UniversalRenderPipelineAsset.asset" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/GeneralEditor/Resources/LowQuality_UniversalRenderPipelineAsset.asset" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/GeneralEditor/Resources/MidQuality_UniversalRenderPipelineAsset.asset" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/GeneralEditor/Resources/MidQuality_UniversalRenderPipelineAsset.asset" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/GeneralEditor/Resources/RTEditor.StringResources.cn.xml" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/GeneralEditor/Resources/RTEditor.StringResources.cn.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/GeneralEditor/Resources/RTScripting.StringResources.cn.xml" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/GeneralEditor/Resources/RTScripting.StringResources.cn.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/GeneralEditor/Resources/UniversalRenderPipelineAsset_Renderer.asset" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/GeneralEditor/Resources/UniversalRenderPipelineAsset_Renderer.asset" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/GeneralEditor/Scenes/Test.unity" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/GeneralEditor/Scenes/Test.unity" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/GeneralEditor/Scripts/Components/GeneralSkybox.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/GeneralEditor/Scripts/Components/GeneralSkybox.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/GeneralEditor/Scripts/Components/StreamingCamera.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/GeneralEditor/Scripts/Components/StreamingCamera.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/GeneralEditor/Scripts/DynamicSurrogates.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/GeneralEditor/Scripts/DynamicSurrogates.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/GeneralEditor/Scripts/Importers/FbxImporter.cs" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/GeneralEditor/Scripts/Importers/FbxImporter.cs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/Settings/UniversalRenderPipelineGlobalSettings.asset" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/Settings/UniversalRenderPipelineGlobalSettings.asset" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Assets/TextMesh Pro/Resources/Fonts &amp; Materials/LiberationSans SDF.asset" beforeDir="false" afterPath="$PROJECT_DIR$/Assets/TextMesh Pro/Resources/Fonts &amp; Materials/LiberationSans SDF.asset" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Packages/manifest.json" beforeDir="false" afterPath="$PROJECT_DIR$/Packages/manifest.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Packages/packages-lock.json" beforeDir="false" afterPath="$PROJECT_DIR$/Packages/packages-lock.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ProjectSettings/EditorBuildSettings.asset" beforeDir="false" afterPath="$PROJECT_DIR$/ProjectSettings/EditorBuildSettings.asset" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ProjectSettings/EditorSettings.asset" beforeDir="false" afterPath="$PROJECT_DIR$/ProjectSettings/EditorSettings.asset" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ProjectSettings/GraphicsSettings.asset" beforeDir="false" afterPath="$PROJECT_DIR$/ProjectSettings/GraphicsSettings.asset" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ProjectSettings/Packages/com.unity.probuilder/Settings.json" beforeDir="false" afterPath="$PROJECT_DIR$/ProjectSettings/Packages/com.unity.probuilder/Settings.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ProjectSettings/ProjectSettings.asset" beforeDir="false" afterPath="$PROJECT_DIR$/ProjectSettings/ProjectSettings.asset" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ProjectSettings/ProjectVersion.txt" beforeDir="false" afterPath="$PROJECT_DIR$/ProjectSettings/ProjectVersion.txt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ProjectSettings/QualitySettings.asset" beforeDir="false" afterPath="$PROJECT_DIR$/ProjectSettings/QualitySettings.asset" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ProjectSettings/ScriptableBuildPipeline.json" beforeDir="false" afterPath="$PROJECT_DIR$/ProjectSettings/ScriptableBuildPipeline.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ProjectSettings/TagManager.asset" beforeDir="false" afterPath="$PROJECT_DIR$/ProjectSettings/TagManager.asset" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/ProjectSettings/VFXManager.asset" beforeDir="false" afterPath="$PROJECT_DIR$/ProjectSettings/VFXManager.asset" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/Packages/unity-mcp" />
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/0022d4fb3cd44d45a62e51c39f257e7c1ea600/4a/2ef935ac/ContextMenuItemAttribute.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/0022d4fb3cd44d45a62e51c39f257e7c1ea600/d0/c5a5b348/Component.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/149c271d11ce4ba6862d7470890edcf418c00/90/8a35bba4/AudioSource.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/314938d17f3848e8ac683e11b27f62ee46ae00/36/35ccab3e/ExceptionDispatchInfo.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/321eb2db7c6d43ea8fc39b54eaca345246ae00/b4/786af9a2/ExceptionDispatchInfo.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/353986e22e2b4cb4ab8afdab5b0099d11ddc00/10/d647a4c6/Object.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/353986e22e2b4cb4ab8afdab5b0099d11ddc00/1e/d2289c65/DynamicGI.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/353986e22e2b4cb4ab8afdab5b0099d11ddc00/26/1c1ec973/Graphics.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/353986e22e2b4cb4ab8afdab5b0099d11ddc00/a7/eabc060e/Texture.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/353986e22e2b4cb4ab8afdab5b0099d11ddc00/f0/d3cce3b7/Cubemap.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/4deec029ea6b4ecc9965a7787360d52c35800/18/5c7897c6/IAssetLoaderContext.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/4deec029ea6b4ecc9965a7787360d52c35800/24/9bcb796b/MaterialMapper.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/4deec029ea6b4ecc9965a7787360d52c35800/25/aa3f7a4c/AssetLoaderContext.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/4deec029ea6b4ecc9965a7787360d52c35800/7a/4b8097e1/MaterialMapperContext.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/4deec029ea6b4ecc9965a7787360d52c35800/7a/9cbd645e/IMaterial.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/4deec029ea6b4ecc9965a7787360d52c35800/82/77d9ddb6/IAwaitable.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/4deec029ea6b4ecc9965a7787360d52c35800/bb/44ff51b7/ThreadUtils.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/4deec029ea6b4ecc9965a7787360d52c35800/e8/17d215cc/MaterialRendererContext.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/5a41d6b7189842eca409fd0b1c3e3dcf17bf78/65/afbf4adf/PropertyInfo.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/5a41d6b7189842eca409fd0b1c3e3dcf17bf78/79/ff1e3fbe/DateTime.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/71119615d44348f087b10ce3c1671c8446ae00/14/011fe52b/Task.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/71119615d44348f087b10ce3c1671c8446ae00/1c/51a0c951/Task`1.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/71119615d44348f087b10ce3c1671c8446ae00/48/d7937e62/AsyncMethodBuilderCore.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/71119615d44348f087b10ce3c1671c8446ae00/6b/0b248d53/AsyncTaskMethodBuilder`1.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/71119615d44348f087b10ce3c1671c8446ae00/77/ef736a7c/AwaitTaskContinuation.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/71119615d44348f087b10ce3c1671c8446ae00/78/db6bfc07/ExceptionDispatchInfo.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/71119615d44348f087b10ce3c1671c8446ae00/88/131c0efc/String.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/71119615d44348f087b10ce3c1671c8446ae00/bd/01616a96/TaskAwaiter`1.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/7fa9e22a6c4a40e5abbae8599fd0bce31eac00/33/00ff6fdf/MonoBehaviour.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/7fa9e22a6c4a40e5abbae8599fd0bce31eac00/51/218ff8e6/Object.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/89527d299cb64ba88f87240781b415e51ea400/15/0fbcd798/ResourceRequest.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/89527d299cb64ba88f87240781b415e51ea400/24/940c715d/Resources.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/89527d299cb64ba88f87240781b415e51ea400/fe/7dbbc3e9/Object.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/89e911b2961c44aa97ad0743a56a2d29f400/4f/d573dba8/PropertyWatcher.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/8db6ec373e6d40bd9d38c8037d358c4e46ae00/07/54306fa4/Buffer.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/8db6ec373e6d40bd9d38c8037d358c4e46ae00/5b/43c63e41/ExceptionDispatchInfo.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/c0791a65cbb1483d8f1708b816951dd118c00/44/4f2862f6/AudioClip.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$APPLICATION_CONFIG_DIR$/resharper-host/DecompilerCache/decompiler/e1073c193ef84b1a9ad1995ac137921f9e4000/b2/812368b5/EditorWindow.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://E:/Unity/6000.1.7f1/Editor/Data/Resources/PackageManager/BuiltInPackages/com.unity.shadergraph/Editor/Generation/Targets/BuiltIn/Editor/AssetPostProcessors/MaterialPostprocessor.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Assets/GeneralEditor/Scripts/ComponentDescriptors/GeneralEnviroManagerDescriptor.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Assets/GeneralEditor/Scripts/Components/GeneralEnviroManager.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Assets/GeneralEditor/Scripts/Components/GeneralTransformSync.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Assets/GeneralEditor/Scripts/Editor/PrefabReplacerWindow.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Assets/GeneralEditor/Scripts/GeneralBehaviour.cs" root0="FORCE_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Library/PackageCache/com.unity.cloud.gltfast@c6d36c11e07a/DocExamples/LoadGltfFromMemory.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Library/PackageCache/com.unity.cloud.gltfast@c6d36c11e07a/Runtime/Scripts/GltfImport.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Library/PackageCache/com.unity.cloud.gltfast@c6d36c11e07a/Runtime/Scripts/Material/IMaterialGenerator.cs" root0="SKIP_HIGHLIGHTING" />
    <setting file="file://$PROJECT_DIR$/Library/PackageCache/com.unity.ugui@4f1c21fcc9bc/Runtime/UGUI/UI/Core/SpecializedCollections/IndexedSet.cs" root0="SKIP_HIGHLIGHTING" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 7
}</component>
  <component name="ProjectId" id="2m2pIhbCoqeEvsCg86iy4GsKoaD" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Attach to Unity Editor.Attach to Unity Editor.executor&quot;: &quot;Debug&quot;,
    &quot;Attach to Unity Player.WindowsPlayer(2,DESKTOP-D9EUJSV).executor&quot;: &quot;Debug&quot;,
    &quot;RunOnceActivity.OpenProjectViewOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;WebServerToolWindowFactoryState&quot;: &quot;false&quot;,
    &quot;git-widget-placeholder&quot;: &quot;master&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/桌面&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.stylelint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.stylelint&quot;: &quot;&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;rider.code.cleanup.on.save&quot;: &quot;true&quot;,
    &quot;rider.code.cleanup.on.save.profile&quot;: &quot;Built-in: Reformat &amp; Apply Syntax Style&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.pluginManager&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;,
    &quot;附加到 Unity 编辑器.附加到 Unity 编辑器.executor&quot;: &quot;Debug&quot;
  },
  &quot;keyToStringList&quot;: {
    &quot;rider.external.source.directories&quot;: [
      &quot;C:\\Users\\<USER>\\AppData\\Roaming\\JetBrains\\Rider2024.1\\resharper-host\\DecompilerCache&quot;,
      &quot;C:\\Users\\<USER>\\AppData\\Roaming\\JetBrains\\Rider2024.1\\resharper-host\\SourcesCache&quot;,
      &quot;C:\\Users\\<USER>\\AppData\\Local\\Symbols\\src&quot;
    ]
  }
}</component>
  <component name="RunManager" selected="Attach to Unity Editor.Attach to Unity Editor">
    <configuration name="启动 Unity" type="RunUnityExe" factoryName="Unity Executable">
      <option name="EXE_PATH" value="C:\Program Files\Unity\6000.0.27f1c1\Editor\Unity.exe" />
      <option name="PROGRAM_PARAMETERS" value="-projectPath &quot;F:\LongProjects\General Editor&quot; -debugCodeOptimization" />
      <option name="WORKING_DIRECTORY" value="F:\LongProjects\General Editor" />
      <option name="PASS_PARENT_ENVS" value="1" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <method v="2" />
    </configuration>
    <configuration name="独立播放器" type="RunUnityExe" factoryName="Unity Executable">
      <option name="EXE_PATH" value="$PROJECT_DIR$/Build\General Editor.exe" />
      <option name="PROGRAM_PARAMETERS" value="" />
      <option name="WORKING_DIRECTORY" value="F:\LongProjects\General Editor\Build" />
      <option name="PASS_PARENT_ENVS" value="1" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <method v="2" />
    </configuration>
    <configuration name="Standalone Player" type="RunUnityExe" factoryName="Unity Executable">
      <option name="EXE_PATH" value="$PROJECT_DIR$/Builds/Debug\General Editor.exe" />
      <option name="PROGRAM_PARAMETERS" value="" />
      <option name="WORKING_DIRECTORY" value="F:\LongProjects\General Editor\Build" />
      <option name="PASS_PARENT_ENVS" value="1" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <method v="2" />
    </configuration>
    <configuration name="Start Unity" type="RunUnityExe" factoryName="Unity Executable">
      <option name="EXE_PATH" value="E:\Unity\6000.1.7f1\Editor\Unity.exe" />
      <option name="PROGRAM_PARAMETERS" value="-projectPath &quot;F:\LongProjects\General Editor&quot; -debugCodeOptimization" />
      <option name="WORKING_DIRECTORY" value="F:\LongProjects\General Editor" />
      <option name="PASS_PARENT_ENVS" value="1" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <method v="2" />
    </configuration>
    <configuration name="Unit Tests (batch mode)" type="RunUnityExe" factoryName="Unity Executable">
      <option name="EXE_PATH" value="E:\Unity\6000.1.7f1\Editor\Unity.exe" />
      <option name="PROGRAM_PARAMETERS" value="-runTests -batchmode -projectPath &quot;F:\LongProjects\General Editor&quot; -testResults Logs/results.xml -testPlatform EditMode -debugCodeOptimization" />
      <option name="WORKING_DIRECTORY" value="F:\LongProjects\General Editor" />
      <option name="PASS_PARENT_ENVS" value="1" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <method v="2" />
    </configuration>
    <configuration name="单元测试(批处理模式)" type="RunUnityExe" factoryName="Unity Executable">
      <option name="EXE_PATH" value="C:\Program Files\Unity\6000.0.27f1c1\Editor\Unity.exe" />
      <option name="PROGRAM_PARAMETERS" value="-runTests -batchmode -projectPath &quot;F:\LongProjects\General Editor&quot; -testResults Logs/results.xml -testPlatform EditMode -debugCodeOptimization" />
      <option name="WORKING_DIRECTORY" value="F:\LongProjects\General Editor" />
      <option name="PASS_PARENT_ENVS" value="1" />
      <option name="USE_EXTERNAL_CONSOLE" value="0" />
      <method v="2" />
    </configuration>
    <configuration name="Attach to Unity Editor &amp; Play" type="UNITY_DEBUG_RUN_CONFIGURATION" factoryName="UNITY_ATTACH_AND_PLAY" show_console_on_std_err="false" show_console_on_std_out="false" port="50000" address="localhost">
      <option name="allowRunningInParallel" value="false" />
      <option name="listenPortForConnections" value="false" />
      <option name="pid" />
      <option name="projectPathOnTarget" />
      <option name="selectedOptions">
        <list />
      </option>
      <method v="2" />
    </configuration>
    <configuration name="Attach to Unity Editor" type="UNITY_DEBUG_RUN_CONFIGURATION" factoryName="Unity Debug" show_console_on_std_err="false" show_console_on_std_out="false" port="50000" address="localhost">
      <option name="allowRunningInParallel" value="false" />
      <option name="listenPortForConnections" value="false" />
      <option name="pid" />
      <option name="projectPathOnTarget" />
      <option name="selectedOptions">
        <list />
      </option>
      <method v="2" />
    </configuration>
    <configuration name="WindowsPlayer(2,DESKTOP-D9EUJSV)" type="UnityPlayer" factoryName="UnityAttachToPlayer" temporary="true">
      <option name="host" value="*************" />
      <option name="playerId" value="WindowsPlayer(2,DESKTOP-D9EUJSV)" />
      <option name="playerInstanceId" value="General Editor" />
      <option name="port" value="56533" />
      <option name="projectName" value="General Editor" />
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Attach to Unity Player.WindowsPlayer(2,DESKTOP-D9EUJSV)" />
      </list>
    </recent_temporary>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="SvnConfiguration" cleanupOnStartRun="true">
    <configuration>C:\Users\<USER>\AppData\Roaming\Subversion</configuration>
    <supportedVersion>125</supportedVersion>
  </component>
  <component name="SvnFileUrlMappingImpl">
    <option name="myMappingRoots">
      <list>
        <SvnCopyRootSimple>
          <option name="myVcsRoot" value="F:\LongProjects\General Editor" />
          <option name="myCopyRoot" value="F:\LongProjects\General Editor" />
        </SvnCopyRootSimple>
      </list>
    </option>
    <option name="myMoreRealMappingRoots">
      <list>
        <SvnCopyRootSimple>
          <option name="myVcsRoot" value="F:\LongProjects\General Editor" />
          <option name="myCopyRoot" value="F:\LongProjects\General Editor" />
        </SvnCopyRootSimple>
      </list>
    </option>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="8dfa6a06-767a-4291-b9b4-6d25cce73568" name="Changes" comment="" />
      <created>1726284447193</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1726284447193</updated>
      <workItem from="1726284448829" duration="4505000" />
      <workItem from="1726709609053" duration="3047000" />
      <workItem from="1726796004677" duration="828000" />
      <workItem from="1727081785039" duration="1069000" />
      <workItem from="1727141309853" duration="5654000" />
      <workItem from="1727232368948" duration="1186000" />
      <workItem from="1728366442136" duration="2508000" />
      <workItem from="1728437742598" duration="879000" />
      <workItem from="1728440595339" duration="591000" />
      <workItem from="1728452314811" duration="4077000" />
      <workItem from="1728526590836" duration="869000" />
      <workItem from="1728540561664" duration="3991000" />
      <workItem from="1728609774850" duration="5911000" />
      <workItem from="1728887161555" duration="7092000" />
      <workItem from="1728955936115" duration="5945000" />
      <workItem from="1728982304395" duration="201000" />
      <workItem from="1728982522581" duration="1533000" />
      <workItem from="1729041645192" duration="1184000" />
      <workItem from="1729049810894" duration="592000" />
      <workItem from="1729127211759" duration="341000" />
      <workItem from="1729127564603" duration="596000" />
      <workItem from="1729733054107" duration="2980000" />
      <workItem from="1730686243633" duration="4156000" />
      <workItem from="1730856794081" duration="2627000" />
      <workItem from="1730945762710" duration="85000" />
      <workItem from="1730945859526" duration="1909000" />
      <workItem from="1730964877148" duration="313000" />
      <workItem from="1730966730839" duration="3838000" />
      <workItem from="1731030210921" duration="1049000" />
      <workItem from="1731033476626" duration="154000" />
      <workItem from="1731037952268" duration="966000" />
      <workItem from="1731044023060" duration="854000" />
      <workItem from="1731045403850" duration="5532000" />
      <workItem from="1731307824535" duration="686000" />
      <workItem from="1731308522894" duration="7231000" />
      <workItem from="1731392935884" duration="257000" />
      <workItem from="1731393221035" duration="698000" />
      <workItem from="1731393943015" duration="8471000" />
      <workItem from="1731483247655" duration="3029000" />
      <workItem from="1731551609049" duration="3777000" />
      <workItem from="1731567048907" duration="542000" />
      <workItem from="1731567607979" duration="129000" />
      <workItem from="1731568203359" duration="778000" />
      <workItem from="1731569036145" duration="3235000" />
      <workItem from="1731633700845" duration="8035000" />
      <workItem from="1731658055278" duration="675000" />
      <workItem from="1731892968157" duration="3963000" />
      <workItem from="1731994916666" duration="4256000" />
      <workItem from="1732066736257" duration="9145000" />
      <workItem from="1732096808748" duration="1000" />
      <workItem from="1732153167074" duration="2796000" />
      <workItem from="1733986305019" duration="1249000" />
      <workItem from="1734056972285" duration="747000" />
      <workItem from="1734058617302" duration="5842000" />
      <workItem from="1734316378706" duration="5190000" />
      <workItem from="1734590719995" duration="1864000" />
      <workItem from="1735025213514" duration="879000" />
      <workItem from="1735090047304" duration="4560000" />
      <workItem from="1735179333089" duration="4008000" />
      <workItem from="1735192563379" duration="675000" />
      <workItem from="1735261854922" duration="9773000" />
      <workItem from="1739412735032" duration="6393000" />
      <workItem from="1739432173545" duration="2874000" />
      <workItem from="1739435186881" duration="4366000" />
      <workItem from="1739843321102" duration="7477000" />
      <workItem from="1739929992040" duration="9947000" />
      <workItem from="1740017182891" duration="2633000" />
      <workItem from="1740033192069" duration="3850000" />
      <workItem from="1740106385119" duration="6474000" />
      <workItem from="1740123464000" duration="1322000" />
      <workItem from="1740362722765" duration="5146000" />
      <workItem from="1740448683071" duration="1293000" />
      <workItem from="1740451994036" duration="2998000" />
      <workItem from="1740462200674" duration="434000" />
      <workItem from="1745546961030" duration="1900000" />
      <workItem from="1746000029724" duration="930000" />
      <workItem from="1746004797846" duration="107000" />
      <workItem from="1746005381269" duration="159000" />
      <workItem from="1746006075216" duration="248000" />
      <workItem from="1746426882161" duration="4155000" />
      <workItem from="1746500190684" duration="100000" />
      <workItem from="1746500303482" duration="797000" />
      <workItem from="1746524614035" duration="926000" />
      <workItem from="1746581364726" duration="2118000" />
      <workItem from="1746587689977" duration="3889000" />
      <workItem from="1746666603906" duration="631000" />
      <workItem from="1746691881744" duration="23000" />
      <workItem from="1747116741703" duration="1411000" />
      <workItem from="1747121504705" duration="1768000" />
      <workItem from="1747129486643" duration="992000" />
      <workItem from="1747202611041" duration="4869000" />
      <workItem from="1747209540396" duration="1357000" />
      <workItem from="1747210943478" duration="3400000" />
      <workItem from="1747276679729" duration="13698000" />
      <workItem from="1747360229460" duration="15813000" />
      <workItem from="1749110091973" duration="640000" />
      <workItem from="1749181962995" duration="2988000" />
      <workItem from="1749434829119" duration="15556000" />
      <workItem from="1749463102762" duration="177000" />
      <workItem from="1749519673492" duration="4997000" />
      <workItem from="1749539157860" duration="5556000" />
      <workItem from="1749613146760" duration="2664000" />
      <workItem from="1749709455812" duration="4171000" />
      <workItem from="1750044389376" duration="1756000" />
      <workItem from="1750053019758" duration="7097000" />
      <workItem from="1750141467453" duration="11644000" />
      <workItem from="1750209122661" duration="2224000" />
      <workItem from="1750214604898" duration="18275000" />
      <workItem from="1750239223394" duration="370000" />
      <workItem from="1750297465580" duration="12163000" />
      <workItem from="1750385072444" duration="4625000" />
      <workItem from="1750661553941" duration="15000" />
      <workItem from="1750663466539" duration="8611000" />
      <workItem from="1750745289581" duration="2363000" />
      <workItem from="1750751793615" duration="234000" />
      <workItem from="1750753764977" duration="26000" />
      <workItem from="1750754658420" duration="1831000" />
      <workItem from="1750814779409" duration="2038000" />
      <workItem from="1750820249420" duration="3092000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="UnityProjectConfiguration" hasMinimizedUI="true" />
  <component name="UnityProjectDiscoverer">
    <option name="hasUnityReference" value="true" />
    <option name="unityProject" value="true" />
    <option name="unityProjectFolder" value="true" />
  </component>
  <component name="UnityUnitTestConfiguration" currentTestLauncher="Both" />
  <component name="UnknownFeatures">
    <option featureType="com.intellij.fileTypeFactory" implementationName="*.py" />
  </component>
  <component name="VcsManagerConfiguration">
    <option name="CLEAR_INITIAL_COMMIT_MESSAGE" value="true" />
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
</project>