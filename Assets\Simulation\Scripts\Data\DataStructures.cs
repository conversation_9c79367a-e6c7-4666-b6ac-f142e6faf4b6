using System;
using UnityEngine;

namespace Simulation.Data
{
    /// <summary>
    /// 传感器数据基类
    /// </summary>
    [Serializable]
    public abstract class SensorDataBase
    {
        public string sensorId;
        public string deviceId;
        public string timestamp;
        public string sensorType;
        
        protected SensorDataBase(string sensorId, string deviceId, string sensorType)
        {
            this.sensorId = sensorId;
            this.deviceId = deviceId;
            this.sensorType = sensorType;
            this.timestamp = DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ss.fffZ");
        }
    }
    
    /// <summary>
    /// 位置追踪数据
    /// </summary>
    [Serializable]
    public class PositionData : SensorDataBase
    {
        public PositionInfo position;
        public RotationInfo rotation;
        public string targetType;
        public string targetId;
        public string zone;
        
        public PositionData(string sensorId, string deviceId) : base(sensorId, deviceId, "position")
        {
            position = new PositionInfo();
            rotation = new RotationInfo();
        }
    }
    
    [Serializable]
    public class PositionInfo
    {
        public float x;
        public float y;
        public float z;
        public string unit = "meter";
        
        public void SetPosition(Vector3 pos)
        {
            x = pos.x;
            y = pos.y;
            z = pos.z;
        }
    }
    
    [Serializable]
    public class RotationInfo
    {
        public float x;
        public float y;
        public float z;
        public string unit = "degree";
        
        public void SetRotation(Vector3 rot)
        {
            x = rot.x;
            y = rot.y;
            z = rot.z;
        }
    }
    
    /// <summary>
    /// 身份识别数据
    /// </summary>
    [Serializable]
    public class IdentityData : SensorDataBase
    {
        public string name;
        public string role;
        public string department;
        public string cardId;
        public string targetType;
        public string status;
        
        public IdentityData(string sensorId, string deviceId) : base(sensorId, deviceId, "identity")
        {
        }
    }
    
    /// <summary>
    /// 运动状态数据
    /// </summary>
    [Serializable]
    public class MotionData : SensorDataBase
    {
        public VelocityInfo velocity;
        public string workingStatus;
        public string targetType;
        public string targetId;
        
        public MotionData(string sensorId, string deviceId) : base(sensorId, deviceId, "motion")
        {
            velocity = new VelocityInfo();
        }
    }
    
    [Serializable]
    public class VelocityInfo
    {
        public float speed;
        public float direction;
        public string unit = "m/s";
        
        public void SetVelocity(Vector3 velocity)
        {
            speed = velocity.magnitude;
            direction = Mathf.Atan2(velocity.z, velocity.x) * Mathf.Rad2Deg;
        }
    }
    
    /// <summary>
    /// 噪声数据
    /// </summary>
    [Serializable]
    public class NoiseData : SensorDataBase
    {
        public float noiseLevel;
        public string unit = "dB";
        public string noiseType;
        public PositionInfo location;
        
        public NoiseData(string sensorId, string deviceId) : base(sensorId, deviceId, "noise")
        {
            location = new PositionInfo();
        }
    }
    
    /// <summary>
    /// 扬尘数据
    /// </summary>
    [Serializable]
    public class DustData : SensorDataBase
    {
        public float pm25;
        public float pm10;
        public string unit = "μg/m³";
        public PositionInfo location;
        
        public DustData(string sensorId, string deviceId) : base(sensorId, deviceId, "dust")
        {
            location = new PositionInfo();
        }
    }
    
    /// <summary>
    /// IoT事件数据
    /// </summary>
    [Serializable]
    public class IoTEventData
    {
        public string eventId;
        public string eventType;
        public string sourceId;
        public string timestamp;
        public string title;
        public string description;
        public string severity;
        public object data;
        
        public IoTEventData()
        {
            eventId = Guid.NewGuid().ToString();
            timestamp = DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ss.fffZ");
        }
    }
    
    /// <summary>
    /// 安全事件数据
    /// </summary>
    [Serializable]
    public class SafetyEventData : IoTEventData
    {
        public string violationType;
        public string workerId;
        public string workerName;
        public PositionInfo location;
        
        public SafetyEventData()
        {
            eventType = "safety_violation";
            location = new PositionInfo();
        }
    }
    
    /// <summary>
    /// 状态事件数据
    /// </summary>
    [Serializable]
    public class StatusEventData : IoTEventData
    {
        public string previousStatus;
        public string currentStatus;
        public string equipmentId;
        public string equipmentType;

        public StatusEventData()
        {
            eventType = "status_change";
        }
    }

    /// <summary>
    /// 事件记录
    /// </summary>
    [Serializable]
    public class EventRecord
    {
        public string EventId;
        public string EventType;
        public EventCategory Category;
        public string SourceId;
        public DateTime Timestamp;
        public string Title;
        public string Description;
        public EventSeverity Severity;
        public DateTime ProcessedAt;
    }

    /// <summary>
    /// 事件统计信息
    /// </summary>
    [Serializable]
    public class EventStatistics
    {
        public string EventType;
        public EventCategory Category;
        public DateTime FirstOccurrence;
        public DateTime LastOccurrence;
        public int Count;
        public double TotalProcessingTime;
        public double AverageProcessingTime;
    }

    /// <summary>
    /// 事件分类枚举
    /// </summary>
    public enum EventCategory
    {
        Safety,         // 安全事件
        Operational,    // 操作事件
        System,         // 系统事件
        Alert,          // 警报事件
        Other           // 其他事件
    }

    /// <summary>
    /// 事件严重程度枚举
    /// </summary>
    public enum EventSeverity
    {
        Low,        // 低
        Medium,     // 中
        High,       // 高
        Critical    // 严重
    }

    /// <summary>
    /// 操作事件数据
    /// </summary>
    [Serializable]
    public class OperationEventData : IoTEventData
    {
        public string operationType;
        public string operatorId;
        public string operatorName;
        public string targetId;
        public string targetType;
        public PositionInfo location;
        
        public OperationEventData()
        {
            eventType = "operation";
            location = new PositionInfo();
        }
    }
    
    /// <summary>
    /// 门禁事件数据
    /// </summary>
    [Serializable]
    public class AccessEventData : IoTEventData
    {
        public string accessType; // "entry" or "exit"
        public string personId;
        public string personName;
        public string cardId;
        public string gateId;
        public string gateName;
        
        public AccessEventData()
        {
            eventType = "access_control";
        }
    }
    
    /// <summary>
    /// 物料事件数据
    /// </summary>
    [Serializable]
    public class MaterialEventData : IoTEventData
    {
        public string materialType;
        public string materialId;
        public float quantity;
        public string unit;
        public string operationType; // "in", "out", "transfer"
        public string warehouseId;
        public string operatorId;
        
        public MaterialEventData()
        {
            eventType = "material_operation";
        }
    }
    
    /// <summary>
    /// 数据格式工具类
    /// </summary>
    public static class DataFormatUtils
    {
        /// <summary>
        /// 将对象转换为JSON字符串
        /// </summary>
        public static string ToJson(object data)
        {
            try
            {
                return JsonUtility.ToJson(data, true);
            }
            catch (Exception ex)
            {
                Debug.LogError($"JSON序列化失败: {ex.Message}");
                return null;
            }
        }
        
        /// <summary>
        /// 从JSON字符串转换为对象
        /// </summary>
        public static T FromJson<T>(string json)
        {
            try
            {
                return JsonUtility.FromJson<T>(json);
            }
            catch (Exception ex)
            {
                Debug.LogError($"JSON反序列化失败: {ex.Message}");
                return default(T);
            }
        }
        
        /// <summary>
        /// 验证传感器数据
        /// </summary>
        public static bool ValidateSensorData(SensorDataBase data)
        {
            if (data == null) return false;
            if (string.IsNullOrEmpty(data.sensorId)) return false;
            if (string.IsNullOrEmpty(data.deviceId)) return false;
            if (string.IsNullOrEmpty(data.sensorType)) return false;
            if (string.IsNullOrEmpty(data.timestamp)) return false;
            
            return true;
        }
        
        /// <summary>
        /// 验证IoT事件数据
        /// </summary>
        public static bool ValidateEventData(IoTEventData eventData)
        {
            if (eventData == null) return false;
            if (string.IsNullOrEmpty(eventData.eventId)) return false;
            if (string.IsNullOrEmpty(eventData.eventType)) return false;
            if (string.IsNullOrEmpty(eventData.sourceId)) return false;
            if (string.IsNullOrEmpty(eventData.timestamp)) return false;
            
            return true;
        }
        
        /// <summary>
        /// 生成MQTT主题
        /// </summary>
        public static string GenerateMQTTTopic(string prefix, string sensorType, string sensorId, string dataType = "data")
        {
            return $"{prefix}/{sensorType}/{sensorId}/{dataType}";
        }
        
        /// <summary>
        /// 生成事件MQTT主题
        /// </summary>
        public static string GenerateEventMQTTTopic(string prefix, string eventType, string sourceId = null)
        {
            if (string.IsNullOrEmpty(sourceId))
            {
                return $"{prefix}/events/{eventType}";
            }
            else
            {
                return $"{prefix}/events/{eventType}/{sourceId}";
            }
        }
    }
}
