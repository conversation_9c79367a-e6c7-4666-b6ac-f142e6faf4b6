using System.Threading;
using Cysharp.Threading.Tasks;
using UnityEngine;
using System;

namespace Simulation
{
    /// <summary>
    /// 模拟器基类，定义了模拟器的基本行为
    /// </summary>
    public abstract class SimulatorBase : MonoBehaviour
    {
        [Header("模拟器基础配置")]
        [SerializeField] protected string simulatorId;
        [SerializeField] protected bool autoStart = true;
        [SerializeField] protected bool enableDebugLog = false;

        // 模拟器状态
        protected bool isRunning = false;
        private CancellationTokenSource cancellationTokenSource;

        // 公共属性
        public string SimulatorId => simulatorId;
        public bool IsRunning => isRunning;
        public abstract string SimulatorType { get; }

        // 事件
        public event Action OnSimulationStarted;
        public event Action OnSimulationStopped;
        public event Action<string> OnSimulationError;

        protected virtual void Awake()
        {
            // 如果没有设置ID，自动生成
            if (string.IsNullOrEmpty(simulatorId))
            {
                simulatorId = $"{SimulatorType}_{GetInstanceID()}";
            }
        }

        protected virtual void Start()
        {
            if (autoStart)
            {
                StartSimulationAsync().Forget();
            }
        }

        protected virtual void OnDestroy()
        {
            StopSimulation();
        }

        /// <summary>
        /// 异步启动模拟
        /// </summary>
        public async UniTaskVoid StartSimulationAsync()
        {
            if (isRunning)
            {
                LogDebug("模拟器已在运行中");
                return;
            }

            try
            {
                cancellationTokenSource = new CancellationTokenSource();
                isRunning = true;

                LogDebug("模拟器启动");
                await OnStartSimulationAsync(cancellationTokenSource.Token);

                OnSimulationStarted?.Invoke();

                // 开始模拟循环
                SimulationLoop(cancellationTokenSource.Token).Forget();
            }
            catch (Exception ex)
            {
                LogError($"模拟器启动异常: {ex.Message}");
                OnSimulationError?.Invoke(ex.Message);
                isRunning = false;
            }
        }

        /// <summary>
        /// 停止模拟
        /// </summary>
        public void StopSimulation()
        {
            if (!isRunning)
            {
                return;
            }

            isRunning = false;
            cancellationTokenSource?.Cancel();
            cancellationTokenSource?.Dispose();
            cancellationTokenSource = null;

            LogDebug("模拟器停止");
            OnStopSimulationAsync().Forget();
            OnSimulationStopped?.Invoke();
        }

        /// <summary>
        /// 模拟循环
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        protected async UniTaskVoid SimulationLoop(CancellationToken cancellationToken)
        {
            try
            {
                while (!cancellationToken.IsCancellationRequested && isRunning)
                {
                    OnSimulationUpdate(Time.deltaTime);
                    await UniTask.Yield(PlayerLoopTiming.Update, cancellationToken);
                }
            }
            catch (OperationCanceledException)
            {
                LogDebug("模拟循环被取消");
            }
            catch (Exception ex)
            {
                LogError($"模拟循环异常: {ex.Message}");
                OnSimulationError?.Invoke(ex.Message);
            }
        }

        /// <summary>
        /// 在模拟启动时调用的异步方法
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        protected virtual async UniTask OnStartSimulationAsync(CancellationToken cancellationToken)
        {
            // 子类可以重写此方法
            await UniTask.Yield(cancellationToken);
        }

        /// <summary>
        /// 在模拟更新时调用
        /// </summary>
        /// <param name="deltaTime">时间增量</param>
        protected virtual void OnSimulationUpdate(float deltaTime)
        {
            // 子类可以重写此方法
        }

        /// <summary>
        /// 在模拟停止时调用的异步方法
        /// </summary>
        protected virtual async UniTaskVoid OnStopSimulationAsync()
        {
            // 子类可以重写此方法
            await UniTask.Yield();
        }

        /// <summary>
        /// 验证模拟器配置
        /// </summary>
        public virtual bool ValidateConfiguration()
        {
            if (string.IsNullOrEmpty(simulatorId))
            {
                LogError("模拟器ID不能为空");
                return false;
            }

            return true;
        }

        /// <summary>
        /// 调试日志
        /// </summary>
        protected void LogDebug(string message)
        {
            if (enableDebugLog)
            {
                Debug.Log($"[{SimulatorType}:{simulatorId}] {message}");
            }
        }

        /// <summary>
        /// 错误日志
        /// </summary>
        protected void LogError(string message)
        {
            Debug.LogError($"[{SimulatorType}:{simulatorId}] {message}");
        }

        /// <summary>
        /// 警告日志
        /// </summary>
        protected void LogWarning(string message)
        {
            Debug.LogWarning($"[{SimulatorType}:{simulatorId}] {message}");
        }
    }
}