using System.Threading;
using Cysharp.Threading.Tasks;
using UnityEngine;

/// <summary>
/// 模拟器基类，定义了模拟器的基本行为
/// </summary>
public abstract class SimulatorBase : MonoBehaviour {
    private CancellationTokenSource _cancellationTokenSource; // 用于取消模拟的CancellationTokenSource


    /// <summary>
    /// 异步启动模拟
    /// </summary>
    public async UniTaskVoid StartSimulationAsync()
    {
        _cancellationTokenSource = new();
        await OnStartSimulationAsync(_cancellationTokenSource.Token);
        SimulationLoop(_cancellationTokenSource.Token).Forget();
    }

    /// <summary>
    /// 停止模拟
    /// </summary>
    public void StopSimulation() {
        _cancellationTokenSource?.Cancel();
        _cancellationTokenSource = null;
        OnStopSimulationAsync().Forget();
    }

    /// <summary>
    /// 模拟循环
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    async UniTaskVoid SimulationLoop(CancellationToken cancellationToken) {
        while (!cancellationToken.IsCancellationRequested) {
            OnSimulationUpdate(Time.deltaTime);

            await UniTask.Yield(PlayerLoopTiming.Update, cancellationToken);
        }
    }

    /// <summary>
    /// 在模拟启动时调用的异步方法
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    protected virtual async UniTask OnStartSimulationAsync(CancellationToken cancellationToken) {
        throw new System.NotImplementedException();
    }
    
    /// <summary>
    /// 在模拟更新时调用
    /// </summary>
    /// <param name="deltaTime">时间增量</param>
    protected virtual void OnSimulationUpdate(float deltaTime) {
        throw new System.NotImplementedException();
    }

    /// <summary>
    /// 在模拟停止时调用的异步方法
    /// </summary>
    protected virtual async UniTask OnStopSimulationAsync() {
        throw new System.NotImplementedException();
    }
}