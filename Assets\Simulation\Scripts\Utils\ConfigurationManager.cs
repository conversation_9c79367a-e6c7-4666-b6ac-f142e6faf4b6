using UnityEngine;
using Cysharp.Threading.Tasks;
using System.Collections.Generic;
using System;
using Simulation.IoTSystem.Core;

namespace Simulation.Utils
{
    /// <summary>
    /// 配置管理器
    /// 负责系统配置的存储、加载和管理
    /// </summary>
    public class ConfigurationManager : MonoBehaviour, IConfigurationManager
    {
        [Header("配置管理")]
        [SerializeField] private bool autoLoad = true;
        [SerializeField] private bool autoSave = true;
        [SerializeField] private string configFileName = "simulation_config.json";
        
        // 配置存储
        private readonly Dictionary<string, object> configurations = new();
        private string configFilePath;
        
        // 事件
        public event Action<string, object> OnConfigChanged;
        
        private void Awake()
        {
            configFilePath = System.IO.Path.Combine(Application.persistentDataPath, configFileName);
        }
        
        private void Start()
        {
            if (autoLoad)
            {
                LoadConfigAsync().Forget();
            }
        }
        
        private void OnApplicationPause(bool pauseStatus)
        {
            if (autoSave && pauseStatus)
            {
                SaveConfigAsync().Forget();
            }
        }
        
        private void OnApplicationFocus(bool hasFocus)
        {
            if (autoSave && !hasFocus)
            {
                SaveConfigAsync().Forget();
            }
        }
        
        private void OnDestroy()
        {
            if (autoSave)
            {
                SaveConfigAsync().Forget();
            }
        }
        
        /// <summary>
        /// 获取配置值
        /// </summary>
        public T GetConfig<T>(string key, T defaultValue = default)
        {
            if (string.IsNullOrEmpty(key))
            {
                Debug.LogWarning("[ConfigurationManager] 配置键不能为空");
                return defaultValue;
            }
            
            if (configurations.TryGetValue(key, out var value))
            {
                try
                {
                    if (value is T directValue)
                    {
                        return directValue;
                    }
                    
                    // 尝试类型转换
                    return (T)Convert.ChangeType(value, typeof(T));
                }
                catch (Exception ex)
                {
                    Debug.LogError($"[ConfigurationManager] 配置值类型转换失败 - 键: {key}, 错误: {ex.Message}");
                    return defaultValue;
                }
            }
            
            // 如果没有找到配置，设置默认值
            SetConfig(key, defaultValue);
            return defaultValue;
        }
        
        /// <summary>
        /// 设置配置值
        /// </summary>
        public void SetConfig<T>(string key, T value)
        {
            if (string.IsNullOrEmpty(key))
            {
                Debug.LogWarning("[ConfigurationManager] 配置键不能为空");
                return;
            }
            
            var oldValue = configurations.ContainsKey(key) ? configurations[key] : null;
            configurations[key] = value;
            
            Debug.Log($"[ConfigurationManager] 配置已更新 - 键: {key}, 值: {value}");
            
            // 触发配置变化事件
            OnConfigChanged?.Invoke(key, value);
        }
        
        /// <summary>
        /// 保存配置
        /// </summary>
        public async UniTask SaveConfigAsync()
        {
            try
            {
                var configData = new ConfigurationData
                {
                    configurations = new List<ConfigurationItem>()
                };
                
                foreach (var kvp in configurations)
                {
                    configData.configurations.Add(new ConfigurationItem
                    {
                        key = kvp.Key,
                        value = kvp.Value?.ToString() ?? "",
                        type = kvp.Value?.GetType().Name ?? "String"
                    });
                }
                
                var json = JsonUtility.ToJson(configData, true);
                await System.IO.File.WriteAllTextAsync(configFilePath, json);
                
                Debug.Log($"[ConfigurationManager] 配置已保存到: {configFilePath}");
            }
            catch (Exception ex)
            {
                Debug.LogError($"[ConfigurationManager] 保存配置失败: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 加载配置
        /// </summary>
        public async UniTask LoadConfigAsync()
        {
            try
            {
                if (!System.IO.File.Exists(configFilePath))
                {
                    Debug.Log("[ConfigurationManager] 配置文件不存在，将使用默认配置");
                    await LoadDefaultConfigAsync();
                    return;
                }
                
                var json = await System.IO.File.ReadAllTextAsync(configFilePath);
                var configData = JsonUtility.FromJson<ConfigurationData>(json);
                
                configurations.Clear();
                
                foreach (var item in configData.configurations)
                {
                    object value = ConvertStringToType(item.value, item.type);
                    configurations[item.key] = value;
                }
                
                Debug.Log($"[ConfigurationManager] 配置已从文件加载: {configFilePath}");
            }
            catch (Exception ex)
            {
                Debug.LogError($"[ConfigurationManager] 加载配置失败: {ex.Message}");
                await LoadDefaultConfigAsync();
            }
        }
        
        /// <summary>
        /// 加载默认配置
        /// </summary>
        private async UniTask LoadDefaultConfigAsync()
        {
            configurations.Clear();
            
            // 设置默认配置
            SetConfig("mqtt.host", "localhost");
            SetConfig("mqtt.port", 1883);
            SetConfig("mqtt.clientId", "UnitySimulation");
            SetConfig("mqtt.keepAlive", 60);
            SetConfig("mqtt.autoReconnect", true);
            
            SetConfig("sensor.defaultSampleRate", 1.0f);
            SetConfig("sensor.enableDebugLog", false);
            
            SetConfig("simulator.autoStart", true);
            SetConfig("simulator.enableDebugLog", false);
            
            SetConfig("system.enableDataValidation", true);
            SetConfig("system.enableDataCompression", false);
            
            Debug.Log("[ConfigurationManager] 默认配置已加载");
            
            // 保存默认配置
            if (autoSave)
            {
                await SaveConfigAsync();
            }
        }
        
        /// <summary>
        /// 字符串转换为指定类型
        /// </summary>
        private object ConvertStringToType(string value, string typeName)
        {
            try
            {
                return typeName switch
                {
                    "String" => value,
                    "Int32" => int.Parse(value),
                    "Single" => float.Parse(value),
                    "Double" => double.Parse(value),
                    "Boolean" => bool.Parse(value),
                    _ => value
                };
            }
            catch
            {
                return value;
            }
        }
        
        /// <summary>
        /// 重置配置
        /// </summary>
        [ContextMenu("Reset Configuration")]
        public void ResetConfiguration()
        {
            LoadDefaultConfigAsync().Forget();
        }
        
        /// <summary>
        /// 删除配置
        /// </summary>
        public bool RemoveConfig(string key)
        {
            if (configurations.Remove(key))
            {
                Debug.Log($"[ConfigurationManager] 配置已删除 - 键: {key}");
                OnConfigChanged?.Invoke(key, null);
                return true;
            }
            
            return false;
        }
        
        /// <summary>
        /// 检查配置是否存在
        /// </summary>
        public bool HasConfig(string key)
        {
            return configurations.ContainsKey(key);
        }
        
        /// <summary>
        /// 获取所有配置键
        /// </summary>
        public string[] GetAllKeys()
        {
            var keys = new string[configurations.Count];
            configurations.Keys.CopyTo(keys, 0);
            return keys;
        }
    }
    
    /// <summary>
    /// 配置数据结构
    /// </summary>
    [Serializable]
    public class ConfigurationData
    {
        public List<ConfigurationItem> configurations;
    }
    
    /// <summary>
    /// 配置项结构
    /// </summary>
    [Serializable]
    public class ConfigurationItem
    {
        public string key;
        public string value;
        public string type;
    }
}
