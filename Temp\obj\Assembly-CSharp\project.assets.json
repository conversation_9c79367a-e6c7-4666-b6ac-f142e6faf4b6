{"version": 3, "targets": {".NETStandard,Version=v2.1": {"Battlehub.LoadImageAsync/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/Battlehub.LoadImageAsync.dll": {}}, "runtime": {"bin/placeholder/Battlehub.LoadImageAsync.dll": {}}}, "Battlehub.RTEditor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Battlehub.LoadImageAsync": "1.0.0", "Battlehub.Storage.Addressables": "1.0.0", "Battlehub.Storage.Core.Runtime": "1.0.0", "Battlehub.Storage.Runtime": "1.0.0", "Battlehub.Storage.ShaderUtil.Runtime": "1.0.0", "HSVPicker": "1.0.0"}, "compile": {"bin/placeholder/Battlehub.RTEditor.dll": {}}, "runtime": {"bin/placeholder/Battlehub.RTEditor.dll": {}}}, "Battlehub.RTEditor.Demo/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Battlehub.RTEditor": "1.0.0", "Battlehub.RTExtensions": "1.0.0", "Battlehub.RTScripting.Jint": "1.0.0", "Battlehub.RTScripting.Roslyn": "1.0.0"}, "compile": {"bin/placeholder/Battlehub.RTEditor.Demo.dll": {}}, "runtime": {"bin/placeholder/Battlehub.RTEditor.Demo.dll": {}}}, "Battlehub.RTEditor.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Battlehub.RTEditor": "1.0.0", "Battlehub.Storage.Editor": "1.0.0", "Battlehub.Storage.Runtime": "1.0.0"}, "compile": {"bin/placeholder/Battlehub.RTEditor.Editor.dll": {}}, "runtime": {"bin/placeholder/Battlehub.RTEditor.Editor.dll": {}}}, "Battlehub.RTEditor.URP/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Battlehub.RTEditor": "1.0.0"}, "compile": {"bin/placeholder/Battlehub.RTEditor.URP.dll": {}}, "runtime": {"bin/placeholder/Battlehub.RTEditor.URP.dll": {}}}, "Battlehub.RTEditor.URP.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Battlehub.RTEditor": "1.0.0", "Battlehub.RTEditor.URP": "1.0.0"}, "compile": {"bin/placeholder/Battlehub.RTEditor.URP.Editor.dll": {}}, "runtime": {"bin/placeholder/Battlehub.RTEditor.URP.Editor.dll": {}}}, "Battlehub.RTExtensions/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Battlehub.RTEditor": "1.0.0", "Battlehub.Storage.Core.Runtime": "1.0.0", "Battlehub.Storage.Runtime": "1.0.0"}, "compile": {"bin/placeholder/Battlehub.RTExtensions.dll": {}}, "runtime": {"bin/placeholder/Battlehub.RTExtensions.dll": {}}}, "Battlehub.RTExtensions.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Battlehub.RTEditor": "1.0.0", "Battlehub.RTExtensions": "1.0.0", "Battlehub.Storage.Editor": "1.0.0"}, "compile": {"bin/placeholder/Battlehub.RTExtensions.Editor.dll": {}}, "runtime": {"bin/placeholder/Battlehub.RTExtensions.Editor.dll": {}}}, "Battlehub.RTExtensions.URP/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Battlehub.RTEditor": "1.0.0", "Battlehub.RTExtensions": "1.0.0"}, "compile": {"bin/placeholder/Battlehub.RTExtensions.URP.dll": {}}, "runtime": {"bin/placeholder/Battlehub.RTExtensions.URP.dll": {}}}, "Battlehub.RTExtensions.URP.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Battlehub.RTEditor": "1.0.0", "Battlehub.RTExtensions.URP": "1.0.0"}, "compile": {"bin/placeholder/Battlehub.RTExtensions.URP.Editor.dll": {}}, "runtime": {"bin/placeholder/Battlehub.RTExtensions.URP.Editor.dll": {}}}, "Battlehub.RTImporter/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Battlehub.RTEditor": "1.0.0"}, "compile": {"bin/placeholder/Battlehub.RTImporter.dll": {}}, "runtime": {"bin/placeholder/Battlehub.RTImporter.dll": {}}}, "Battlehub.RTScripting.Common/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Battlehub.RTEditor": "1.0.0"}, "compile": {"bin/placeholder/Battlehub.RTScripting.Common.dll": {}}, "runtime": {"bin/placeholder/Battlehub.RTScripting.Common.dll": {}}}, "Battlehub.RTScripting.Jint/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Battlehub.RTEditor": "1.0.0"}, "compile": {"bin/placeholder/Battlehub.RTScripting.Jint.dll": {}}, "runtime": {"bin/placeholder/Battlehub.RTScripting.Jint.dll": {}}}, "Battlehub.RTScripting.Jint.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Battlehub.RTEditor": "1.0.0"}, "compile": {"bin/placeholder/Battlehub.RTScripting.Jint.Editor.dll": {}}, "runtime": {"bin/placeholder/Battlehub.RTScripting.Jint.Editor.dll": {}}}, "Battlehub.RTScripting.Roslyn/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Battlehub.RTEditor": "1.0.0", "CodeAnalysis": "1.0.0"}, "compile": {"bin/placeholder/Battlehub.RTScripting.Roslyn.dll": {}}, "runtime": {"bin/placeholder/Battlehub.RTScripting.Roslyn.dll": {}}}, "Battlehub.RTScripting.Roslyn.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Battlehub.RTEditor": "1.0.0"}, "compile": {"bin/placeholder/Battlehub.RTScripting.Roslyn.Editor.dll": {}}, "runtime": {"bin/placeholder/Battlehub.RTScripting.Roslyn.Editor.dll": {}}}, "Battlehub.Storage.Addressables/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Battlehub.Storage.Core.Runtime": "1.0.0"}, "compile": {"bin/placeholder/Battlehub.Storage.Addressables.dll": {}}, "runtime": {"bin/placeholder/Battlehub.Storage.Addressables.dll": {}}}, "Battlehub.Storage.Core.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Battlehub.Storage.Core.Runtime": "1.0.0"}, "compile": {"bin/placeholder/Battlehub.Storage.Core.Editor.dll": {}}, "runtime": {"bin/placeholder/Battlehub.Storage.Core.Editor.dll": {}}}, "Battlehub.Storage.Core.Runtime/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Battlehub.Storage.ShaderUtil.Runtime": "1.0.0"}, "compile": {"bin/placeholder/Battlehub.Storage.Core.Runtime.dll": {}}, "runtime": {"bin/placeholder/Battlehub.Storage.Core.Runtime.dll": {}}}, "Battlehub.Storage.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Battlehub.Storage.Core.Editor": "1.0.0", "Battlehub.Storage.Core.Runtime": "1.0.0", "Battlehub.Storage.Runtime": "1.0.0", "Battlehub.Storage.ShaderUtil.Editor": "1.0.0", "Battlehub.Storage.ShaderUtil.Runtime": "1.0.0"}, "compile": {"bin/placeholder/Battlehub.Storage.Editor.dll": {}}, "runtime": {"bin/placeholder/Battlehub.Storage.Editor.dll": {}}}, "Battlehub.Storage.Runtime/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Battlehub.Storage.Core.Runtime": "1.0.0", "Battlehub.Storage.ShaderUtil.Runtime": "1.0.0"}, "compile": {"bin/placeholder/Battlehub.Storage.Runtime.dll": {}}, "runtime": {"bin/placeholder/Battlehub.Storage.Runtime.dll": {}}}, "Battlehub.Storage.ShaderUtil.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Battlehub.Storage.ShaderUtil.Runtime": "1.0.0"}, "compile": {"bin/placeholder/Battlehub.Storage.ShaderUtil.Editor.dll": {}}, "runtime": {"bin/placeholder/Battlehub.Storage.ShaderUtil.Editor.dll": {}}}, "Battlehub.Storage.ShaderUtil.Runtime/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/Battlehub.Storage.ShaderUtil.Runtime.dll": {}}, "runtime": {"bin/placeholder/Battlehub.Storage.ShaderUtil.Runtime.dll": {}}}, "CodeAnalysis/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/CodeAnalysis.dll": {}}, "runtime": {"bin/placeholder/CodeAnalysis.dll": {}}}, "com.Tivadar.Best.HTTP/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/com.Tivadar.Best.HTTP.dll": {}}, "runtime": {"bin/placeholder/com.Tivadar.Best.HTTP.dll": {}}}, "com.Tivadar.Best.MQTT/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"com.Tivadar.Best.HTTP": "1.0.0", "com.Tivadar.Best.WebSockets": "1.0.0"}, "compile": {"bin/placeholder/com.Tivadar.Best.MQTT.dll": {}}, "runtime": {"bin/placeholder/com.Tivadar.Best.MQTT.dll": {}}}, "com.Tivadar.Best.TLSSecurity/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"com.Tivadar.Best.HTTP": "1.0.0"}, "compile": {"bin/placeholder/com.Tivadar.Best.TLSSecurity.dll": {}}, "runtime": {"bin/placeholder/com.Tivadar.Best.TLSSecurity.dll": {}}}, "com.Tivadar.Best.WebSockets/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"com.Tivadar.Best.HTTP": "1.0.0"}, "compile": {"bin/placeholder/com.Tivadar.Best.WebSockets.dll": {}}, "runtime": {"bin/placeholder/com.Tivadar.Best.WebSockets.dll": {}}}, "DOTween.Modules/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/DOTween.Modules.dll": {}}, "runtime": {"bin/placeholder/DOTween.Modules.dll": {}}}, "Enviro3.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Enviro3.Runtime": "1.0.0"}, "compile": {"bin/placeholder/Enviro3.Editor.dll": {}}, "runtime": {"bin/placeholder/Enviro3.Editor.dll": {}}}, "Enviro3.Runtime/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/Enviro3.Runtime.dll": {}}, "runtime": {"bin/placeholder/Enviro3.Runtime.dll": {}}}, "HSVPicker/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/HSVPicker.dll": {}}, "runtime": {"bin/placeholder/HSVPicker.dll": {}}}, "IngameDebugConsole.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"IngameDebugConsole.Runtime": "1.0.0"}, "compile": {"bin/placeholder/IngameDebugConsole.Editor.dll": {}}, "runtime": {"bin/placeholder/IngameDebugConsole.Editor.dll": {}}}, "IngameDebugConsole.Runtime/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/IngameDebugConsole.Runtime.dll": {}}, "runtime": {"bin/placeholder/IngameDebugConsole.Runtime.dll": {}}}, "Simulation/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"com.Tivadar.Best.HTTP": "1.0.0", "com.Tivadar.Best.MQTT": "1.0.0"}, "compile": {"bin/placeholder/Simulation.dll": {}}, "runtime": {"bin/placeholder/Simulation.dll": {}}}, "TriLib/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/TriLib.dll": {}}, "runtime": {"bin/placeholder/TriLib.dll": {}}}, "TriLib.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"TriLib": "1.0.0"}, "compile": {"bin/placeholder/TriLib.Editor.dll": {}}, "runtime": {"bin/placeholder/TriLib.Editor.dll": {}}}, "UIShapesKit/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/UIShapesKit.dll": {}}, "runtime": {"bin/placeholder/UIShapesKit.dll": {}}}, "UIShapesKit.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UIShapesKit": "1.0.0"}, "compile": {"bin/placeholder/UIShapesKit.Editor.dll": {}}, "runtime": {"bin/placeholder/UIShapesKit.Editor.dll": {}}}, "UMP/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/UMP.dll": {}}, "runtime": {"bin/placeholder/UMP.dll": {}}}}}, "libraries": {"Battlehub.LoadImageAsync/1.0.0": {"type": "project", "path": "Battlehub.LoadImageAsync.csproj", "msbuildProject": "Battlehub.LoadImageAsync.csproj"}, "Battlehub.RTEditor/1.0.0": {"type": "project", "path": "Battlehub.RTEditor.csproj", "msbuildProject": "Battlehub.RTEditor.csproj"}, "Battlehub.RTEditor.Demo/1.0.0": {"type": "project", "path": "Battlehub.RTEditor.Demo.csproj", "msbuildProject": "Battlehub.RTEditor.Demo.csproj"}, "Battlehub.RTEditor.Editor/1.0.0": {"type": "project", "path": "Battlehub.RTEditor.Editor.csproj", "msbuildProject": "Battlehub.RTEditor.Editor.csproj"}, "Battlehub.RTEditor.URP/1.0.0": {"type": "project", "path": "Battlehub.RTEditor.URP.csproj", "msbuildProject": "Battlehub.RTEditor.URP.csproj"}, "Battlehub.RTEditor.URP.Editor/1.0.0": {"type": "project", "path": "Battlehub.RTEditor.URP.Editor.csproj", "msbuildProject": "Battlehub.RTEditor.URP.Editor.csproj"}, "Battlehub.RTExtensions/1.0.0": {"type": "project", "path": "Battlehub.RTExtensions.csproj", "msbuildProject": "Battlehub.RTExtensions.csproj"}, "Battlehub.RTExtensions.Editor/1.0.0": {"type": "project", "path": "Battlehub.RTExtensions.Editor.csproj", "msbuildProject": "Battlehub.RTExtensions.Editor.csproj"}, "Battlehub.RTExtensions.URP/1.0.0": {"type": "project", "path": "Battlehub.RTExtensions.URP.csproj", "msbuildProject": "Battlehub.RTExtensions.URP.csproj"}, "Battlehub.RTExtensions.URP.Editor/1.0.0": {"type": "project", "path": "Battlehub.RTExtensions.URP.Editor.csproj", "msbuildProject": "Battlehub.RTExtensions.URP.Editor.csproj"}, "Battlehub.RTImporter/1.0.0": {"type": "project", "path": "Battlehub.RTImporter.csproj", "msbuildProject": "Battlehub.RTImporter.csproj"}, "Battlehub.RTScripting.Common/1.0.0": {"type": "project", "path": "Battlehub.RTScripting.Common.csproj", "msbuildProject": "Battlehub.RTScripting.Common.csproj"}, "Battlehub.RTScripting.Jint/1.0.0": {"type": "project", "path": "Battlehub.RTScripting.Jint.csproj", "msbuildProject": "Battlehub.RTScripting.Jint.csproj"}, "Battlehub.RTScripting.Jint.Editor/1.0.0": {"type": "project", "path": "Battlehub.RTScripting.Jint.Editor.csproj", "msbuildProject": "Battlehub.RTScripting.Jint.Editor.csproj"}, "Battlehub.RTScripting.Roslyn/1.0.0": {"type": "project", "path": "Battlehub.RTScripting.Roslyn.csproj", "msbuildProject": "Battlehub.RTScripting.Roslyn.csproj"}, "Battlehub.RTScripting.Roslyn.Editor/1.0.0": {"type": "project", "path": "Battlehub.RTScripting.Roslyn.Editor.csproj", "msbuildProject": "Battlehub.RTScripting.Roslyn.Editor.csproj"}, "Battlehub.Storage.Addressables/1.0.0": {"type": "project", "path": "Battlehub.Storage.Addressables.csproj", "msbuildProject": "Battlehub.Storage.Addressables.csproj"}, "Battlehub.Storage.Core.Editor/1.0.0": {"type": "project", "path": "Battlehub.Storage.Core.Editor.csproj", "msbuildProject": "Battlehub.Storage.Core.Editor.csproj"}, "Battlehub.Storage.Core.Runtime/1.0.0": {"type": "project", "path": "Battlehub.Storage.Core.Runtime.csproj", "msbuildProject": "Battlehub.Storage.Core.Runtime.csproj"}, "Battlehub.Storage.Editor/1.0.0": {"type": "project", "path": "Battlehub.Storage.Editor.csproj", "msbuildProject": "Battlehub.Storage.Editor.csproj"}, "Battlehub.Storage.Runtime/1.0.0": {"type": "project", "path": "Battlehub.Storage.Runtime.csproj", "msbuildProject": "Battlehub.Storage.Runtime.csproj"}, "Battlehub.Storage.ShaderUtil.Editor/1.0.0": {"type": "project", "path": "Battlehub.Storage.ShaderUtil.Editor.csproj", "msbuildProject": "Battlehub.Storage.ShaderUtil.Editor.csproj"}, "Battlehub.Storage.ShaderUtil.Runtime/1.0.0": {"type": "project", "path": "Battlehub.Storage.ShaderUtil.Runtime.csproj", "msbuildProject": "Battlehub.Storage.ShaderUtil.Runtime.csproj"}, "CodeAnalysis/1.0.0": {"type": "project", "path": "CodeAnalysis.csproj", "msbuildProject": "CodeAnalysis.csproj"}, "com.Tivadar.Best.HTTP/1.0.0": {"type": "project", "path": "com.Tivadar.Best.HTTP.csproj", "msbuildProject": "com.Tivadar.Best.HTTP.csproj"}, "com.Tivadar.Best.MQTT/1.0.0": {"type": "project", "path": "com.Tivadar.Best.MQTT.csproj", "msbuildProject": "com.Tivadar.Best.MQTT.csproj"}, "com.Tivadar.Best.TLSSecurity/1.0.0": {"type": "project", "path": "com.Tivadar.Best.TLSSecurity.csproj", "msbuildProject": "com.Tivadar.Best.TLSSecurity.csproj"}, "com.Tivadar.Best.WebSockets/1.0.0": {"type": "project", "path": "com.Tivadar.Best.WebSockets.csproj", "msbuildProject": "com.Tivadar.Best.WebSockets.csproj"}, "DOTween.Modules/1.0.0": {"type": "project", "path": "DOTween.Modules.csproj", "msbuildProject": "DOTween.Modules.csproj"}, "Enviro3.Editor/1.0.0": {"type": "project", "path": "Enviro3.Editor.csproj", "msbuildProject": "Enviro3.Editor.csproj"}, "Enviro3.Runtime/1.0.0": {"type": "project", "path": "Enviro3.Runtime.csproj", "msbuildProject": "Enviro3.Runtime.csproj"}, "HSVPicker/1.0.0": {"type": "project", "path": "HSVPicker.csproj", "msbuildProject": "HSVPicker.csproj"}, "IngameDebugConsole.Editor/1.0.0": {"type": "project", "path": "IngameDebugConsole.Editor.csproj", "msbuildProject": "IngameDebugConsole.Editor.csproj"}, "IngameDebugConsole.Runtime/1.0.0": {"type": "project", "path": "IngameDebugConsole.Runtime.csproj", "msbuildProject": "IngameDebugConsole.Runtime.csproj"}, "Simulation/1.0.0": {"type": "project", "path": "Simulation.csproj", "msbuildProject": "Simulation.csproj"}, "TriLib/1.0.0": {"type": "project", "path": "TriLib.csproj", "msbuildProject": "TriLib.csproj"}, "TriLib.Editor/1.0.0": {"type": "project", "path": "TriLib.Editor.csproj", "msbuildProject": "TriLib.Editor.csproj"}, "UIShapesKit/1.0.0": {"type": "project", "path": "UIShapesKit.csproj", "msbuildProject": "UIShapesKit.csproj"}, "UIShapesKit.Editor/1.0.0": {"type": "project", "path": "UIShapesKit.Editor.csproj", "msbuildProject": "UIShapesKit.Editor.csproj"}, "UMP/1.0.0": {"type": "project", "path": "UMP.csproj", "msbuildProject": "UMP.csproj"}}, "projectFileDependencyGroups": {".NETStandard,Version=v2.1": ["Battlehub.LoadImageAsync >= 1.0.0", "Battlehub.RTEditor >= 1.0.0", "Battlehub.RTEditor.Demo >= 1.0.0", "Battlehub.RTEditor.Editor >= 1.0.0", "Battlehub.RTEditor.URP >= 1.0.0", "Battlehub.RTEditor.URP.Editor >= 1.0.0", "Battlehub.RTExtensions >= 1.0.0", "Battlehub.RTExtensions.Editor >= 1.0.0", "Battlehub.RTExtensions.URP >= 1.0.0", "Battlehub.RTExtensions.URP.Editor >= 1.0.0", "Battlehub.RTImporter >= 1.0.0", "Battlehub.RTScripting.Common >= 1.0.0", "Battlehub.RTScripting.Jint >= 1.0.0", "Battlehub.RTScripting.Jint.Editor >= 1.0.0", "Battlehub.RTScripting.Roslyn >= 1.0.0", "Battlehub.RTScripting.Roslyn.Editor >= 1.0.0", "Battlehub.Storage.Addressables >= 1.0.0", "Battlehub.Storage.Core.Editor >= 1.0.0", "Battlehub.Storage.Core.Runtime >= 1.0.0", "Battlehub.Storage.Editor >= 1.0.0", "Battlehub.Storage.Runtime >= 1.0.0", "Battlehub.Storage.ShaderUtil.Editor >= 1.0.0", "Battlehub.Storage.ShaderUtil.Runtime >= 1.0.0", "CodeAnalysis >= 1.0.0", "DOTween.Modules >= 1.0.0", "Enviro3.Editor >= 1.0.0", "Enviro3.Runtime >= 1.0.0", "HSVPicker >= 1.0.0", "IngameDebugConsole.Editor >= 1.0.0", "IngameDebugConsole.Runtime >= 1.0.0", "Simulation >= 1.0.0", "TriLib >= 1.0.0", "TriLib.Editor >= 1.0.0", "UIShapesKit >= 1.0.0", "UIShapesKit.Editor >= 1.0.0", "UMP >= 1.0.0", "com.Tivadar.Best.HTTP >= 1.0.0", "com.Tivadar.Best.MQTT >= 1.0.0", "com.Tivadar.Best.TLSSecurity >= 1.0.0", "com.Tivadar.Best.WebSockets >= 1.0.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "E:\\VS2022Tools\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "f:\\LongProjects\\General Editor\\Assembly-CSharp.csproj", "projectName": "Assembly-CSharp", "projectPath": "f:\\LongProjects\\General Editor\\Assembly-CSharp.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "f:\\LongProjects\\General Editor\\Temp\\obj\\Assembly-CSharp\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\VS2022Tools\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"f:\\LongProjects\\General Editor\\Battlehub.LoadImageAsync.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.LoadImageAsync.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.RTEditor.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.RTEditor.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.RTEditor.Demo.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.RTEditor.Demo.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.RTEditor.Editor.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.RTEditor.Editor.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.RTEditor.URP.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.RTEditor.URP.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.RTEditor.URP.Editor.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.RTEditor.URP.Editor.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.RTExtensions.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.RTExtensions.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.RTExtensions.Editor.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.RTExtensions.Editor.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.RTExtensions.URP.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.RTExtensions.URP.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.RTExtensions.URP.Editor.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.RTExtensions.URP.Editor.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.RTImporter.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.RTImporter.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.RTScripting.Common.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.RTScripting.Common.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.RTScripting.Jint.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.RTScripting.Jint.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.RTScripting.Jint.Editor.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.RTScripting.Jint.Editor.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.RTScripting.Roslyn.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.RTScripting.Roslyn.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.RTScripting.Roslyn.Editor.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.RTScripting.Roslyn.Editor.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.Addressables.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.Addressables.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.Core.Editor.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.Core.Editor.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.Core.Runtime.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.Core.Runtime.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.Editor.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.Editor.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.Runtime.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.Runtime.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.ShaderUtil.Editor.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.ShaderUtil.Editor.csproj"}, "f:\\LongProjects\\General Editor\\Battlehub.Storage.ShaderUtil.Runtime.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Battlehub.Storage.ShaderUtil.Runtime.csproj"}, "f:\\LongProjects\\General Editor\\CodeAnalysis.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\CodeAnalysis.csproj"}, "f:\\LongProjects\\General Editor\\com.Tivadar.Best.HTTP.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\com.Tivadar.Best.HTTP.csproj"}, "f:\\LongProjects\\General Editor\\com.Tivadar.Best.MQTT.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\com.Tivadar.Best.MQTT.csproj"}, "f:\\LongProjects\\General Editor\\com.Tivadar.Best.TLSSecurity.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\com.Tivadar.Best.TLSSecurity.csproj"}, "f:\\LongProjects\\General Editor\\com.Tivadar.Best.WebSockets.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\com.Tivadar.Best.WebSockets.csproj"}, "f:\\LongProjects\\General Editor\\DOTween.Modules.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\DOTween.Modules.csproj"}, "f:\\LongProjects\\General Editor\\Enviro3.Editor.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Enviro3.Editor.csproj"}, "f:\\LongProjects\\General Editor\\Enviro3.Runtime.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Enviro3.Runtime.csproj"}, "f:\\LongProjects\\General Editor\\HSVPicker.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\HSVPicker.csproj"}, "f:\\LongProjects\\General Editor\\IngameDebugConsole.Editor.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\IngameDebugConsole.Editor.csproj"}, "f:\\LongProjects\\General Editor\\IngameDebugConsole.Runtime.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\IngameDebugConsole.Runtime.csproj"}, "f:\\LongProjects\\General Editor\\Simulation.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\Simulation.csproj"}, "f:\\LongProjects\\General Editor\\TriLib.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\TriLib.csproj"}, "f:\\LongProjects\\General Editor\\TriLib.Editor.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\TriLib.Editor.csproj"}, "f:\\LongProjects\\General Editor\\UIShapesKit.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\UIShapesKit.csproj"}, "f:\\LongProjects\\General Editor\\UIShapesKit.Editor.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\UIShapesKit.Editor.csproj"}, "f:\\LongProjects\\General Editor\\UMP.csproj": {"projectPath": "f:\\LongProjects\\General Editor\\UMP.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}}