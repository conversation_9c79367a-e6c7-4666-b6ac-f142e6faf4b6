using System;
using System.Collections.Generic;
using UnityEngine;
using Best.MQTT.Packets;

namespace Simulation.IoTSystem
{
    /// <summary>
    /// MQTT配置管理器
    /// 提供运行时MQTT配置管理功能
    /// </summary>
    public class MQTTConfigurationManager : MonoBehaviour
    {
        [Header("预设配置")]
        [SerializeField] private MQTTConnectionPreset[] availablePresets;
        [SerializeField] private int defaultPresetIndex = 0;
        
        [Header("运行时配置")]
        [SerializeField] private bool allowRuntimeConfigChange = true;
        [SerializeField] private bool saveConfigToPlayerPrefs = true;
        
        private MQTTManager mqttManager;
        private MQTTConnectionPreset currentPreset;
        private const string PRESET_KEY = "MQTT_CurrentPreset";
        
        // 配置变更事件
        public event Action<MQTTConnectionPreset> OnConfigurationChanged;
        
        // 公共属性
        public MQTTConnectionPreset CurrentPreset => currentPreset;
        public MQTTConnectionPreset[] AvailablePresets => availablePresets;
        public bool AllowRuntimeConfigChange => allowRuntimeConfigChange;
        
        private void Awake()
        {
            // 获取MQTT管理器
            mqttManager = GetComponent<MQTTManager>();
            if (mqttManager == null)
            {
                mqttManager = gameObject.AddComponent<MQTTManager>();
            }
            
            // 加载配置
            LoadConfiguration();
        }
        
        private void Start()
        {
            // 应用当前配置
            if (currentPreset != null)
            {
                ApplyConfiguration(currentPreset);
            }
        }
        
        /// <summary>
        /// 加载配置
        /// </summary>
        private void LoadConfiguration()
        {
            if (saveConfigToPlayerPrefs && PlayerPrefs.HasKey(PRESET_KEY))
            {
                // 从PlayerPrefs加载保存的配置
                string savedPresetName = PlayerPrefs.GetString(PRESET_KEY);
                var savedPreset = FindPresetByName(savedPresetName);
                
                if (savedPreset != null)
                {
                    currentPreset = savedPreset;
                    Debug.Log($"[MQTTConfig] 加载保存的配置: {savedPresetName}");
                    return;
                }
            }
            
            // 使用默认配置
            if (availablePresets != null && availablePresets.Length > 0)
            {
                int index = Mathf.Clamp(defaultPresetIndex, 0, availablePresets.Length - 1);
                currentPreset = availablePresets[index];
                Debug.Log($"[MQTTConfig] 使用默认配置: {currentPreset.PresetName}");
            }
            else
            {
                Debug.LogWarning("[MQTTConfig] 没有可用的预设配置");
            }
        }
        
        /// <summary>
        /// 保存配置
        /// </summary>
        private void SaveConfiguration()
        {
            if (saveConfigToPlayerPrefs && currentPreset != null)
            {
                PlayerPrefs.SetString(PRESET_KEY, currentPreset.PresetName);
                PlayerPrefs.Save();
                Debug.Log($"[MQTTConfig] 保存配置: {currentPreset.PresetName}");
            }
        }
        
        /// <summary>
        /// 应用配置
        /// </summary>
        public bool ApplyConfiguration(MQTTConnectionPreset preset)
        {
            if (preset == null)
            {
                Debug.LogError("[MQTTConfig] 预设配置为空");
                return false;
            }
            
            if (!allowRuntimeConfigChange && Application.isPlaying && mqttManager.IsConnected)
            {
                Debug.LogWarning("[MQTTConfig] 运行时配置更改被禁用");
                return false;
            }
            
            try
            {
                // 如果已连接，先断开
                if (mqttManager.IsConnected)
                {
                    mqttManager.DisconnectAsync().Forget();
                }
                
                // 应用新配置
                preset.ApplyToMQTTManager(mqttManager);
                currentPreset = preset;
                
                // 保存配置
                SaveConfiguration();
                
                // 触发事件
                OnConfigurationChanged?.Invoke(preset);
                
                Debug.Log($"[MQTTConfig] 应用配置: {preset.PresetName}");
                return true;
            }
            catch (Exception ex)
            {
                Debug.LogError($"[MQTTConfig] 应用配置失败: {ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// 切换到指定预设
        /// </summary>
        public bool SwitchToPreset(string presetName)
        {
            var preset = FindPresetByName(presetName);
            if (preset != null)
            {
                return ApplyConfiguration(preset);
            }
            
            Debug.LogError($"[MQTTConfig] 未找到预设: {presetName}");
            return false;
        }
        
        /// <summary>
        /// 切换到指定索引的预设
        /// </summary>
        public bool SwitchToPreset(int index)
        {
            if (availablePresets == null || index < 0 || index >= availablePresets.Length)
            {
                Debug.LogError($"[MQTTConfig] 预设索引无效: {index}");
                return false;
            }
            
            return ApplyConfiguration(availablePresets[index]);
        }
        
        /// <summary>
        /// 根据名称查找预设
        /// </summary>
        private MQTTConnectionPreset FindPresetByName(string name)
        {
            if (availablePresets == null) return null;
            
            foreach (var preset in availablePresets)
            {
                if (preset != null && preset.PresetName == name)
                {
                    return preset;
                }
            }
            
            return null;
        }
        
        /// <summary>
        /// 获取当前配置摘要
        /// </summary>
        public string GetCurrentConfigurationSummary()
        {
            if (currentPreset == null)
            {
                return "无当前配置";
            }
            
            return currentPreset.GetConfigurationSummary();
        }
        
        /// <summary>
        /// 获取所有可用预设的名称
        /// </summary>
        public string[] GetAvailablePresetNames()
        {
            if (availablePresets == null) return new string[0];
            
            var names = new List<string>();
            foreach (var preset in availablePresets)
            {
                if (preset != null)
                {
                    names.Add(preset.PresetName);
                }
            }
            
            return names.ToArray();
        }
        
        /// <summary>
        /// 验证当前配置
        /// </summary>
        public bool ValidateCurrentConfiguration()
        {
            if (currentPreset == null)
            {
                Debug.LogError("[MQTTConfig] 没有当前配置");
                return false;
            }
            
            return currentPreset.ValidateConfiguration();
        }
        
        /// <summary>
        /// 重置为默认配置
        /// </summary>
        [ContextMenu("重置为默认配置")]
        public void ResetToDefault()
        {
            if (availablePresets != null && availablePresets.Length > 0)
            {
                int index = Mathf.Clamp(defaultPresetIndex, 0, availablePresets.Length - 1);
                ApplyConfiguration(availablePresets[index]);
            }
        }
        
        /// <summary>
        /// 清除保存的配置
        /// </summary>
        [ContextMenu("清除保存的配置")]
        public void ClearSavedConfiguration()
        {
            if (PlayerPrefs.HasKey(PRESET_KEY))
            {
                PlayerPrefs.DeleteKey(PRESET_KEY);
                PlayerPrefs.Save();
                Debug.Log("[MQTTConfig] 已清除保存的配置");
            }
        }
        
        /// <summary>
        /// 连接使用当前配置
        /// </summary>
        [ContextMenu("连接MQTT")]
        public void ConnectWithCurrentConfiguration()
        {
            if (currentPreset == null)
            {
                Debug.LogError("[MQTTConfig] 没有当前配置");
                return;
            }
            
            if (!ValidateCurrentConfiguration())
            {
                Debug.LogError("[MQTTConfig] 当前配置无效");
                return;
            }
            
            mqttManager.ConnectAsync().Forget();
        }
        
        /// <summary>
        /// 断开MQTT连接
        /// </summary>
        [ContextMenu("断开MQTT")]
        public void DisconnectMQTT()
        {
            mqttManager.DisconnectAsync().Forget();
        }
    }
}
