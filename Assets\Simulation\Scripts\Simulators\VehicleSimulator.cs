using System;
using System.Threading;
using Cysharp.Threading.Tasks;
using UnityEngine;
using Simulation.IoTSystem;
using Simulation.Data;

namespace Simulation.Simulators
{
    /// <summary>
    /// 车辆模拟器
    /// 模拟车辆的运输任务流程、门禁识别和装卸作业
    /// </summary>
    public class VehicleSimulator : SimulatorBase
    {
        [Header("车辆基础信息")]
        [SerializeField] private string vehicleId = "vehicle_001";
        [SerializeField] private string vehicleType = "truck"; // truck, crane, excavator, mixer
        [SerializeField] private string licensePlate = "京A12345";
        [SerializeField] private string driverName = "李四";
        [SerializeField] private string driverId = "driver_001";
        [SerializeField] private float vehicleWeight = 15000f; // 车辆重量（kg）
        [SerializeField] private string vehicleModel = "重型卡车";
        
        [Header("运输配置")]
        [SerializeField] private float maxSpeed = 10f; // 最大速度（米/秒）
        [SerializeField] private float acceleration = 2f; // 加速度
        [SerializeField] private float loadingTime = 300f; // 装载时间（秒）
        [SerializeField] private float unloadingTime = 180f; // 卸载时间（秒）
        [SerializeField] private int maxCapacity = 20; // 最大载重（吨）
        
        [Header("路径配置")]
        [SerializeField] private Transform[] routePoints; // 运输路径点
        [SerializeField] private Transform gatePosition; // 门禁位置
        [SerializeField] private Transform loadingArea; // 装载区域
        [SerializeField] private Transform unloadingArea; // 卸载区域
        [SerializeField] private Transform parkingArea; // 停车区域
        
        [Header("任务配置")]
        [SerializeField] private int dailyTrips = 5; // 每日运输次数
        [SerializeField] private float tripInterval = 3600f; // 运输间隔（秒）
        [SerializeField] private bool enableRandomDelay = true; // 启用随机延迟
        [SerializeField] private float maxRandomDelay = 300f; // 最大随机延迟（秒）
        
        [Header("门禁配置")]
        [SerializeField] private bool requireGateAccess = true; // 需要门禁识别
        [SerializeField] private float gateAccessTime = 10f; // 门禁识别时间（秒）
        [SerializeField] private string accessCardId = "CARD_V001";
        
        [Header("调试配置")]
        [SerializeField] private bool enableDebugLog = true;
        [SerializeField] private bool showRouteGizmos = true;
        [SerializeField] private Color routeColor = Color.blue;
        
        // 车辆状态
        private VehicleState currentState = VehicleState.Parked;
        private int currentRouteIndex = 0;
        private int completedTrips = 0;
        private float currentSpeed = 0f;
        private float stateStartTime;
        private bool isLoaded = false;
        private float currentLoad = 0f; // 当前载重（吨）
        
        // 任务统计
        private float totalDistance = 0f;
        private float totalWorkTime = 0f;
        private int totalAccessEvents = 0;
        
        // 公共属性
        public string VehicleId => vehicleId;
        public string LicensePlate => licensePlate;
        public VehicleState CurrentState => currentState;
        public float CurrentSpeed => currentSpeed;
        public bool IsLoaded => isLoaded;
        public int CompletedTrips => completedTrips;
        
        protected override async UniTask OnStartSimulationAsync(CancellationToken cancellationToken)
        {
            LogDebug("车辆模拟器启动");
            
            // 初始化路径点
            if (routePoints == null || routePoints.Length == 0)
            {
                // 如果没有设置路径点，创建默认路径
                CreateDefaultRoute();
            }
            
            // 初始化位置
            if (parkingArea != null)
            {
                transform.position = parkingArea.position;
                transform.rotation = parkingArea.rotation;
            }
            
            // 设置初始状态
            ChangeState(VehicleState.Parked);
            
            // 发送车辆进入工地事件
            SendVehicleEvent("vehicle_entry", "车辆进入工地", 
                $"{vehicleType} {licensePlate} 由 {driverName} 驾驶进入工地");
            
            // 启动运输任务循环
            TransportTaskLoop(cancellationToken).Forget();
        }
        
        protected override void OnSimulationUpdate(float deltaTime)
        {
            totalWorkTime += deltaTime;
            
            switch (currentState)
            {
                case VehicleState.MovingToGate:
                    UpdateMovingToGate(deltaTime);
                    break;
                case VehicleState.AccessingGate:
                    UpdateAccessingGate(deltaTime);
                    break;
                case VehicleState.MovingToLoading:
                    UpdateMovingToLoading(deltaTime);
                    break;
                case VehicleState.Loading:
                    UpdateLoading(deltaTime);
                    break;
                case VehicleState.MovingToUnloading:
                    UpdateMovingToUnloading(deltaTime);
                    break;
                case VehicleState.Unloading:
                    UpdateUnloading(deltaTime);
                    break;
                case VehicleState.MovingToParking:
                    UpdateMovingToParking(deltaTime);
                    break;
                case VehicleState.FollowingRoute:
                    UpdateFollowingRoute(deltaTime);
                    break;
            }
        }

        protected override UniTask OnStopSimulationAsync()
        {
            LogDebug("车辆模拟器停止");

            // 发送车辆离开工地事件
            SendVehicleEvent("vehicle_exit", "车辆离开工地",
                $"{vehicleType} {licensePlate} 完成工作，共完成{completedTrips}次运输");

            return UniTask.CompletedTask;
        }
        
        /// <summary>
        /// 运输任务循环
        /// </summary>
        private async UniTaskVoid TransportTaskLoop(CancellationToken cancellationToken)
        {
            try
            {
                while (!cancellationToken.IsCancellationRequested && completedTrips < dailyTrips)
                {
                    // 等待运输间隔
                    await UniTask.Delay(TimeSpan.FromSeconds(tripInterval), cancellationToken: cancellationToken);
                    
                    // 添加随机延迟
                    if (enableRandomDelay)
                    {
                        float randomDelay = UnityEngine.Random.Range(0f, maxRandomDelay);
                        await UniTask.Delay(TimeSpan.FromSeconds(randomDelay), cancellationToken: cancellationToken);
                    }
                    
                    // 开始新的运输任务
                    await StartTransportTask(cancellationToken);
                }
                
                LogDebug($"完成所有运输任务，共{completedTrips}次");
            }
            catch (OperationCanceledException)
            {
                LogDebug("运输任务循环被取消");
            }
        }
        
        /// <summary>
        /// 开始运输任务
        /// </summary>
        private async UniTask StartTransportTask(CancellationToken cancellationToken)
        {
            LogDebug($"开始第{completedTrips + 1}次运输任务");
            
            // 发送任务开始事件
            SendOperationEvent("transport_start", "运输任务开始", 
                $"开始第{completedTrips + 1}次运输任务");
            
            // 移动到门禁
            if (requireGateAccess && gatePosition != null)
            {
                ChangeState(VehicleState.MovingToGate);
            }
            else
            {
                ChangeState(VehicleState.MovingToLoading);
            }
        }
        
        /// <summary>
        /// 更新移动到门禁状态
        /// </summary>
        private void UpdateMovingToGate(float deltaTime)
        {
            if (MoveTowards(gatePosition.position, deltaTime))
            {
                LogDebug("到达门禁位置");
                ChangeState(VehicleState.AccessingGate);
            }
        }
        
        /// <summary>
        /// 更新门禁识别状态
        /// </summary>
        private void UpdateAccessingGate(float deltaTime)
        {
            float accessTime = Time.time - stateStartTime;
            
            if (accessTime >= gateAccessTime)
            {
                LogDebug("门禁识别完成");
                totalAccessEvents++;
                
                // 发送门禁事件
                SendAccessEvent("entry");
                
                ChangeState(VehicleState.MovingToLoading);
            }
        }
        
        /// <summary>
        /// 更新移动到装载区状态
        /// </summary>
        private void UpdateMovingToLoading(float deltaTime)
        {
            if (MoveTowards(loadingArea.position, deltaTime))
            {
                LogDebug("到达装载区域");
                ChangeState(VehicleState.Loading);
            }
        }
        
        /// <summary>
        /// 更新装载状态
        /// </summary>
        private void UpdateLoading(float deltaTime)
        {
            float loadTime = Time.time - stateStartTime;
            
            if (loadTime >= loadingTime)
            {
                LogDebug("装载完成");
                isLoaded = true;
                currentLoad = UnityEngine.Random.Range(maxCapacity * 0.7f, maxCapacity);
                
                // 发送装载完成事件
                SendMaterialEvent("material_loaded", currentLoad);
                
                ChangeState(VehicleState.MovingToUnloading);
            }
        }
        
        /// <summary>
        /// 更新移动到卸载区状态
        /// </summary>
        private void UpdateMovingToUnloading(float deltaTime)
        {
            if (MoveTowards(unloadingArea.position, deltaTime))
            {
                LogDebug("到达卸载区域");
                ChangeState(VehicleState.Unloading);
            }
        }
        
        /// <summary>
        /// 更新卸载状态
        /// </summary>
        private void UpdateUnloading(float deltaTime)
        {
            float unloadTime = Time.time - stateStartTime;
            
            if (unloadTime >= unloadingTime)
            {
                LogDebug("卸载完成");
                
                // 发送卸载完成事件
                SendMaterialEvent("material_unloaded", currentLoad);
                
                isLoaded = false;
                currentLoad = 0f;
                completedTrips++;
                
                // 发送任务完成事件
                SendOperationEvent("transport_complete", "运输任务完成", 
                    $"完成第{completedTrips}次运输任务");
                
                ChangeState(VehicleState.MovingToParking);
            }
        }
        
        /// <summary>
        /// 更新移动到停车区状态
        /// </summary>
        private void UpdateMovingToParking(float deltaTime)
        {
            if (MoveTowards(parkingArea.position, deltaTime))
            {
                LogDebug("返回停车区域");
                ChangeState(VehicleState.Parked);
            }
        }
        
        /// <summary>
        /// 更新路径跟随状态
        /// </summary>
        private void UpdateFollowingRoute(float deltaTime)
        {
            if (routePoints != null && routePoints.Length > 0)
            {
                Transform targetPoint = routePoints[currentRouteIndex];
                
                if (MoveTowards(targetPoint.position, deltaTime))
                {
                    currentRouteIndex = (currentRouteIndex + 1) % routePoints.Length;
                    LogDebug($"到达路径点 {currentRouteIndex}");
                }
            }
        }
        
        /// <summary>
        /// 移动到目标位置
        /// </summary>
        private bool MoveTowards(Vector3 target, float deltaTime)
        {
            Vector3 direction = (target - transform.position).normalized;
            float distance = Vector3.Distance(transform.position, target);
            
            if (distance <= 0.5f)
            {
                transform.position = target;
                currentSpeed = 0f;
                return true;
            }
            
            // 计算速度（考虑加速和减速）
            float targetSpeed = Mathf.Min(maxSpeed, distance * 2f); // 接近目标时减速
            currentSpeed = Mathf.MoveTowards(currentSpeed, targetSpeed, acceleration * deltaTime);
            
            // 移动
            Vector3 movement = direction * currentSpeed * deltaTime;
            transform.position += movement;
            totalDistance += movement.magnitude;
            
            // 更新朝向
            if (direction != Vector3.zero)
            {
                transform.rotation = Quaternion.LookRotation(direction);
            }
            
            return false;
        }
        
        /// <summary>
        /// 改变车辆状态
        /// </summary>
        private void ChangeState(VehicleState newState)
        {
            if (currentState != newState)
            {
                LogDebug($"车辆状态变化: {currentState} -> {newState}");
                currentState = newState;
                stateStartTime = Time.time;
                
                // 发送状态变化事件
                SendStatusEvent(currentState.ToString(), newState.ToString());
            }
        }
        
        /// <summary>
        /// 创建默认路径
        /// </summary>
        private void CreateDefaultRoute()
        {
            Vector3 basePos = transform.position;
            routePoints = new Transform[4];
            
            for (int i = 0; i < 4; i++)
            {
                GameObject routePoint = new GameObject($"RoutePoint_{i}");
                routePoint.transform.position = basePos + new Vector3(
                    Mathf.Cos(i * Mathf.PI * 0.5f) * 20f,
                    0f,
                    Mathf.Sin(i * Mathf.PI * 0.5f) * 20f
                );
                routePoints[i] = routePoint.transform;
            }
            
            LogDebug("创建默认运输路径");
        }
        
        /// <summary>
        /// 发送车辆事件
        /// </summary>
        private void SendVehicleEvent(string eventType, string title, string description)
        {
            var eventData = new OperationEventData
            {
                operationType = eventType,
                operatorId = driverId,
                operatorName = driverName,
                targetId = vehicleId,
                targetType = "vehicle",
                title = title,
                description = description,
                sourceId = vehicleId
            };
            
            eventData.location.SetPosition(transform.position);
            
            IoTSystem.IoTSystem.Instance?.SendIOTEvent(vehicleId, eventType, title, description, eventData);
        }
        
        /// <summary>
        /// 发送状态变化事件
        /// </summary>
        private void SendStatusEvent(string previousStatus, string currentStatus)
        {
            var statusEvent = new StatusEventData
            {
                previousStatus = previousStatus,
                currentStatus = currentStatus,
                equipmentId = vehicleId,
                equipmentType = "vehicle",
                sourceId = vehicleId,
                title = "车辆状态变化",
                description = $"{vehicleType} {licensePlate} 状态从{previousStatus}变为{currentStatus}"
            };
            
            IoTSystem.IoTSystem.Instance?.SendIOTEvent(vehicleId, "status_change", "车辆状态变化", statusEvent.description, statusEvent);
        }
        
        /// <summary>
        /// 发送门禁事件
        /// </summary>
        private void SendAccessEvent(string accessType)
        {
            var accessEvent = new AccessEventData
            {
                accessType = accessType,
                personId = driverId,
                personName = driverName,
                cardId = accessCardId,
                gateId = "main_gate",
                gateName = "主门禁",
                sourceId = vehicleId,
                title = "车辆门禁",
                description = $"{vehicleType} {licensePlate} {(accessType == "entry" ? "进入" : "离开")}工地"
            };
            
            IoTSystem.IoTSystem.Instance?.SendIOTEvent(vehicleId, "access_control", "车辆门禁", accessEvent.description, accessEvent);
        }
        
        /// <summary>
        /// 发送物料事件
        /// </summary>
        private void SendMaterialEvent(string operationType, float quantity)
        {
            var materialEvent = new MaterialEventData
            {
                materialType = "construction_material",
                materialId = $"material_{DateTime.Now:yyyyMMddHHmmss}",
                quantity = quantity,
                unit = "ton",
                operationType = operationType == "material_loaded" ? "out" : "in",
                warehouseId = operationType == "material_loaded" ? "loading_area" : "unloading_area",
                operatorId = driverId,
                sourceId = vehicleId,
                title = operationType == "material_loaded" ? "物料装载" : "物料卸载",
                description = $"{vehicleType} {licensePlate} {(operationType == "material_loaded" ? "装载" : "卸载")} {quantity:F1}吨物料"
            };
            
            IoTSystem.IoTSystem.Instance?.SendIOTEvent(vehicleId, "material_operation", materialEvent.title, materialEvent.description, materialEvent);
        }
        
        /// <summary>
        /// 发送操作事件
        /// </summary>
        private void SendOperationEvent(string operationType, string title, string description)
        {
            var operationEvent = new OperationEventData
            {
                operationType = operationType,
                operatorId = driverId,
                operatorName = driverName,
                targetId = vehicleId,
                targetType = "vehicle",
                title = title,
                description = description,
                sourceId = vehicleId
            };
            
            operationEvent.location.SetPosition(transform.position);
            
            IoTSystem.IoTSystem.Instance?.SendIOTEvent(vehicleId, operationType, title, description, operationEvent);
        }
        
        /// <summary>
        /// 调试日志
        /// </summary>
        private void LogDebug(string message)
        {
            if (enableDebugLog)
            {
                Debug.Log($"[VehicleSimulator:{vehicleId}] {message}");
            }
        }
        
        /// <summary>
        /// 绘制调试信息
        /// </summary>
        private void OnDrawGizmos()
        {
            if (!showRouteGizmos) return;
            
            // 绘制路径点
            if (routePoints != null)
            {
                Gizmos.color = routeColor;
                for (int i = 0; i < routePoints.Length; i++)
                {
                    if (routePoints[i] != null)
                    {
                        Gizmos.DrawWireSphere(routePoints[i].position, 1f);
                        
                        // 绘制路径连线
                        if (i < routePoints.Length - 1 && routePoints[i + 1] != null)
                        {
                            Gizmos.DrawLine(routePoints[i].position, routePoints[i + 1].position);
                        }
                    }
                }
            }
            
            // 绘制关键位置
            if (gatePosition != null)
            {
                Gizmos.color = Color.red;
                Gizmos.DrawWireCube(gatePosition.position, Vector3.one);
            }
            
            if (loadingArea != null)
            {
                Gizmos.color = Color.green;
                Gizmos.DrawWireCube(loadingArea.position, Vector3.one * 2f);
            }
            
            if (unloadingArea != null)
            {
                Gizmos.color = Color.blue;
                Gizmos.DrawWireCube(unloadingArea.position, Vector3.one * 2f);
            }
            
            if (parkingArea != null)
            {
                Gizmos.color = Color.yellow;
                Gizmos.DrawWireCube(parkingArea.position, Vector3.one * 1.5f);
            }
            
            // 绘制车辆状态
            Gizmos.color = isLoaded ? Color.red : Color.white;
            Gizmos.DrawWireCube(transform.position + Vector3.up * 2f, Vector3.one * 0.5f);
        }
    }
    
    /// <summary>
    /// 车辆状态枚举
    /// </summary>
    public enum VehicleState
    {
        Parked,             // 停车
        MovingToGate,       // 移动到门禁
        AccessingGate,      // 门禁识别
        MovingToLoading,    // 移动到装载区
        Loading,            // 装载中
        MovingToUnloading,  // 移动到卸载区
        Unloading,          // 卸载中
        MovingToParking,    // 移动到停车区
        FollowingRoute      // 跟随路径
    }
}
