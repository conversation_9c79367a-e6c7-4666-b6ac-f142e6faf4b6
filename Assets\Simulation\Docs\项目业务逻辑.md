# 建筑工地数字孪生模拟系统业务逻辑文档

## 1. 系统整体业务架构

### 1.1 核心业务流程概览

```mermaid
graph TB
    A[工地模拟启动] --> B[初始化各模拟器]
    B --> C[启动异步业务流程]
    C --> D[工人日常作业流程]
    C --> E[车辆运输流程]
    C --> F[设备作业流程]
    C --> G[门禁管理流程]
    C --> H[物料管理流程]

    D --> I[环境模拟器监听]
    E --> I
    F --> I

    I --> J[环境数据生成]

    D --> K[传感器数据采集]
    E --> K
    F --> K
    G --> K
    H --> K
    J --> K

    K --> L[IoT系统数据处理]
    L --> M[MQTT数据发布]
    M --> N[数字孪生系统接收]
```

### 1.2 数据流向架构

```mermaid
graph LR
    subgraph "模拟层"
        A1[工人模拟器]
        A2[车辆模拟器]
        A3[设备模拟器]
        A4[门禁模拟器]
        A5[物料模拟器]
    end
    
    subgraph "传感器层"
        B1[位置传感器]
        B2[身份传感器]
        B3[运动传感器]
        B4[噪声传感器]
        B5[扬尘传感器]
    end
    
    subgraph "环境层"
        C1[环境模拟器]
    end
    
    subgraph "数据层"
        D1[IoT系统]
        D2[MQTT发布]
    end
    
    A1 --> B1
    A1 --> B2
    A2 --> B1
    A2 --> B3
    A3 --> B3
    
    A1 --> C1
    A2 --> C1
    A3 --> C1
    
    C1 --> B4
    C1 --> B5
    
    B1 --> D1
    B2 --> D1
    B3 --> D1
    B4 --> D1
    B5 --> D1
    
    D1 --> D2
```

## 2. 核心业务模块流程

### 2.1 工人管理业务流程

#### 2.1.1 工人日常作业流程

```mermaid
sequenceDiagram
    participant W as 工人模拟器
    participant G as 门禁系统
    participant P as 位置传感器
    participant I as 身份传感器
    participant IoT as IoT系统
    participant S as 安全检查
    
    W->>G: 刷卡进入工地
    G->>I: 识别并记录身份信息
    I->>IoT: 上报身份数据
    
    loop 工作日循环
        W->>W: 移动到工作位置
        W->>P: 更新位置信息
        P->>IoT: 上报位置数据
        
        W->>W: 执行工作任务
        W->>S: 随机安全检查
        
        alt 发现安全违规
            S->>IoT: 发送安全事件
        end
        
        W->>W: 等待下一个任务
    end
    
    W->>G: 刷卡离开工地
    G->>I: 识别并记录离开信息
    I->>IoT: 上报离开数据
```

#### 2.1.2 安全违规检测流程

```mermaid
flowchart TD
    A[工人执行任务] --> B{随机安全事件触发}
    B -->|触发概率| C[随机选择违规类型]
    B -->|继续工作| A

    C --> D[生成安全违规事件]
    D --> E[记录违规详情]
    E --> F[发送IoT事件]
    F --> A

    note1[违规类型包括:<br/>- 未佩戴安全帽<br/>- 未穿着防护服<br/>- 未佩戴安全带<br/>- 工作时使用手机<br/>- 违规操作设备]
    C -.-> note1
```

### 2.2 车辆管理业务流程

#### 2.2.1 车辆运输任务流程

```mermaid
sequenceDiagram
    participant V as 车辆模拟器
    participant G as 门禁系统
    participant P as 位置传感器
    participant M as 运动传感器
    participant Mat as 物料系统
    participant IoT as IoT系统
    
    V->>G: 车辆到达工地门口
    G->>G: 车牌识别记录
    G->>IoT: 记录进入信息
    
    V->>V: 移动到装载区
    V->>P: 更新位置信息
    P->>IoT: 上报位置数据
    
    V->>Mat: 开始装载物料
    Mat->>Mat: 更新库存状态
    Mat->>IoT: 上报物料变化
    
    V->>V: 运输到目标位置
    V->>M: 更新速度状态
    M->>IoT: 上报运动数据
    
    V->>Mat: 卸载物料
    Mat->>Mat: 更新目标库存
    Mat->>IoT: 上报物料变化
    
    V->>G: 车辆离开工地
    G->>IoT: 记录离开信息
```

#### 2.2.2 车辆状态监控流程

```mermaid
stateDiagram-v2
    [*] --> 等待任务
    等待任务 --> 前往工地 : 接收运输任务
    前往工地 --> 门禁识别 : 到达工地
    门禁识别 --> 进入工地 : 识别记录
    
    进入工地 --> 移动到装载区
    移动到装载区 --> 装载物料
    装载物料 --> 运输中 : 装载完成
    运输中 --> 卸载物料 : 到达目标
    卸载物料 --> 离开工地 : 卸载完成
    离开工地 --> 等待任务 : 任务完成
    
    note right of 运输中 : 持续监控位置和速度
    note right of 装载物料 : 更新物料库存
    note right of 卸载物料 : 更新目标库存
```

### 2.3 设备管理业务流程

#### 2.3.1 塔吊作业流程

```mermaid
flowchart TD
    A[塔吊启动] --> B[进入待机状态]

    B --> C{接收作业任务}
    C -->|有任务| D[移动到起吊位置]
    C -->|无任务| B

    D --> E[起吊物料]
    E --> F[旋转到目标位置]
    F --> G[下降放置物料]
    G --> H[返回待机位置]
    H --> B

    B --> I{随机安全事件触发}
    I -->|安全事件| J[生成安全事件]
    I -->|正常运行| B

    J --> K[发送安全事件]
    K --> B

    subgraph "数据采集"
        M[运动传感器]
        N[位置传感器]
        O[工作状态监控]
    end

    D --> M
    E --> M
    F --> M
    G --> M

    D --> N
    F --> N
    H --> N

    B --> O
    D --> O
    J --> O
```

#### 2.3.2 升降机作业流程

```mermaid
flowchart TD
    A[升降机启动] --> B[进入待机状态]

    B --> C{接收运输任务}
    C -->|有任务| D[移动到起始楼层]
    C -->|无任务| B

    D --> E[装载人员/物料]
    E --> F[运行到目标楼层]
    F --> G[卸载人员/物料]
    G --> H[返回待机位置]
    H --> B

    B --> I{随机安全事件触发}
    I -->|安全事件| J[生成安全事件]
    I -->|正常运行| B

    J --> K[发送安全事件]
    K --> B

    subgraph "数据采集"
        P[运动传感器]
        Q[位置传感器]
        R[工作状态监控]
    end

    D --> P
    E --> P
    F --> P
    G --> P

    D --> Q
    F --> Q
    H --> Q

    B --> R
    D --> R
    J --> R
```

### 2.4 环境监测业务流程

#### 2.4.1 环境影响传播模型

```mermaid
graph TD
    subgraph "噪声源"
        A1[工人作业]
        A2[车辆行驶]
        A3[设备运行]
    end
    
    subgraph "扬尘源"
        B1[车辆行驶]
        B2[物料装卸]
        B3[施工作业]
    end
    
    subgraph "环境计算"
        C1[噪声传播算法]
        C2[扬尘扩散算法]
    end
    
    subgraph "传感器采集"
        D1[噪声传感器]
        D2[扬尘传感器]
    end
    
    A1 --> C1
    A2 --> C1
    A3 --> C1
    
    B1 --> C2
    B2 --> C2
    B3 --> C2
    
    C1 --> D1
    C2 --> D2
    
    D1 --> E[IoT系统]
    D2 --> E
```

### 2.5 门禁管理业务流程

#### 2.5.1 人员出入管理流程

```mermaid
sequenceDiagram
    participant P as 人员
    participant G as 门禁设备
    participant I as 身份识别
    participant IoT as IoT系统
    participant S as 统计系统

    P->>G: 刷卡/扫码
    G->>I: 读取身份信息
    I->>G: 识别身份
    G->>P: 允许通过
    G->>IoT: 记录通过信息
    IoT->>S: 更新统计数据
```

### 2.6 物料管理业务流程

#### 2.6.1 物料出入库流程

```mermaid
flowchart TD
    A[物料到达] --> B{入库还是出库}

    B -->|入库| C[更新库存数据]
    B -->|出库| M[接收出库申请]

    C --> D[生成入库单]
    D --> E[发送IoT事件]

    M --> F[确认库存数量]
    F --> G{库存充足}
    G -->|充足| H[执行出库]
    G -->|不足| I[发送库存不足IoT事件]

    H --> J[更新库存数据]
    J --> K[生成出库单]
    K --> L[发送IoT事件]

    E --> End[流程结束]
    L --> End
    I --> End
```

## 3. 技术实现指导

### 3.1 异步编程实现要点

#### 3.1.1 核心异步模式
```csharp
// 工人模拟器异步实现示例
public class WorkerSimulator : MonoBehaviour
{
    private CancellationTokenSource cancellationTokenSource;

    private async void Start()
    {
        cancellationTokenSource = new CancellationTokenSource();
        await StartWorkDayAsync(cancellationTokenSource.Token);
    }

    public async UniTask StartWorkDayAsync(CancellationToken cancellationToken)
    {
        await EnterWorkSiteAsync(cancellationToken);
        await PerformDailyWorkAsync(cancellationToken);
        await ExitWorkSiteAsync(cancellationToken);
    }

    private async UniTask PerformDailyWorkAsync(CancellationToken cancellationToken)
    {
        while (!cancellationToken.IsCancellationRequested)
        {
            await MoveToWorkLocationAsync(cancellationToken);
            await PerformWorkTaskAsync(cancellationToken);
            await RandomSafetyViolationCheckAsync(cancellationToken);
            await UniTask.Delay(TimeSpan.FromSeconds(workInterval), cancellationToken);
        }
    }
}
```

#### 3.1.2 传感器组件基础实现
```csharp
public abstract class SensorComponentBase : MonoBehaviour
{
    [SerializeField] protected string sensorId;
    [SerializeField] protected float publishInterval = 1.0f;
    [SerializeField] protected bool isActive = true;

    private float lastPublishTime;

    protected virtual void Start()
    {
        IoTSystem.Instance?.RegisterSensor(this);
    }

    protected virtual void Update()
    {
        if (isActive && Time.time - lastPublishTime >= publishInterval)
        {
            UpdateSensorData();
            lastPublishTime = Time.time;
        }
    }

    public abstract void UpdateSensorData();

    protected virtual void ReportData(object data)
    {
        IoTSystem.Instance?.CollectSensorData(sensorId, data);
    }
}
```

#### 3.1.3 IoT系统核心接口
```csharp
public class IoTSystem : MonoBehaviour
{
    public static IoTSystem Instance { get; private set; }

    // 传感器管理
    public void RegisterSensor(ISensorComponent sensor);
    public void UnregisterSensor(string sensorId);

    // 数据收集
    public void CollectSensorData(string sensorId, object data);

    // 事件管理
    public void SendIOTEvent(string sourceId, string eventType, string title, string description, object data);

    // 连接管理
    public void StartDataTransmission();
    public void StopDataTransmission();
    public bool IsConnected { get; private set; }
}
```

### 3.2 关键配置参数

#### 3.2.1 传感器配置参数
```csharp
[Header("传感器基础配置")]
[SerializeField] private string sensorId = "sensor_001";
[SerializeField] private float publishInterval = 1.0f;
[SerializeField] private bool isActive = true;

[Header("位置追踪传感器配置")]
[SerializeField] private bool enablePositionX = true;
[SerializeField] private bool enablePositionY = true;
[SerializeField] private bool enablePositionZ = true;
[SerializeField] private bool enableRotationY = true;
[SerializeField] private string targetType = "person";
[SerializeField] private string targetId = "worker_001";
[SerializeField] private string zone = "construction_area_A";
```

#### 3.2.2 模拟器配置参数
```csharp
[Header("工人模拟器配置")]
[SerializeField] private float workInterval = 5.0f;
[SerializeField] private float safetyCheckInterval = 300.0f; // 5分钟
[SerializeField] private float safetyViolationProbability = 0.1f; // 10%概率

[Header("车辆模拟器配置")]
[SerializeField] private float moveSpeed = 10.0f;
[SerializeField] private float loadingTime = 30.0f;
[SerializeField] private string vehicleType = "transport_truck";
[SerializeField] private string licensePlate = "京A12345";

[Header("设备模拟器配置")]
[SerializeField] private float operationCycleTime = 120.0f;
[SerializeField] private float safetyEventProbability = 0.05f; // 5%概率
[SerializeField] private string equipmentType = "tower_crane";
[SerializeField] private string equipmentId = "crane_001";
```

#### 3.2.3 MQTT连接配置
```csharp
[Header("MQTT配置")]
[SerializeField] private string brokerAddress = "localhost";
[SerializeField] private int brokerPort = 1883;
[SerializeField] private string clientId = "simulation_system";
[SerializeField] private bool useSSL = false;
[SerializeField] private int keepAliveInterval = 60;
[SerializeField] private int reconnectDelay = 5;
```

### 3.3 数据格式规范

#### 3.3.1 位置数据格式
```json
{
  "sensorId": "pos_001",
  "timestamp": "2024-01-01T10:00:00Z",
  "targetType": "person|vehicle|equipment",
  "targetId": "target_001",
  "position": {
    "x": 100.5,
    "y": 50.2,
    "z": 10.0
  },
  "rotation": {
    "y": 45.0
  },
  "zone": "construction_area_A"
}
```

#### 3.3.2 身份数据格式
```json
{
  "sensorId": "id_001",
  "timestamp": "2024-01-01T10:00:00Z",
  "targetType": "person|vehicle",
  "targetId": "target_001",
  "identity": {
    "name": "张三",
    "role": "工人",
    "department": "施工队A"
  },
  "attributes": {
    "vehicleType": "挖掘机",
    "licensePlate": "京A12345"
  }
}
```

#### 3.3.3 运动数据格式
```json
{
  "sensorId": "motion_001",
  "timestamp": "2024-01-01T10:00:00Z",
  "targetType": "vehicle|crane|elevator",
  "targetId": "target_001",
  "motion": {
    "speed": 15.5,
    "direction": 45.0
  },
  "workStatus": {
    "status": "working|idle|maintenance",
    "load": 80.5,
    "operation": "lifting|moving|loading"
  }
}
```

#### 3.3.4 环境数据格式
```json
// 噪声数据
{
  "sensorId": "noise_001",
  "timestamp": "2024-01-01T10:00:00Z",
  "position": {
    "x": 100.5,
    "y": 50.2,
    "z": 10.0
  },
  "noise": {
    "level": 75.5,
    "unit": "dB"
  }
}

// 扬尘数据
{
  "sensorId": "dust_001",
  "timestamp": "2024-01-01T10:00:00Z",
  "position": {
    "x": 100.5,
    "y": 50.2,
    "z": 10.0
  },
  "dust": {
    "pm25": 45.2,
    "pm10": 78.1,
    "unit": "μg/m³"
  }
}
```

#### 3.3.5 事件数据格式
```json
{
  "eventId": "evt_001",
  "eventType": "safety|status|operation",
  "sourceId": "worker_001",
  "sourceType": "worker|vehicle|equipment",
  "timestamp": "2024-01-01T12:00:00Z",
  "severity": "low|medium|high|critical",
  "title": "工人安全违规",
  "description": "检测到工人未佩戴安全帽",
  "data": {
    "violationType": "未佩戴安全帽",
    "location": {
      "x": 100.5,
      "y": 50.2,
      "z": 10.0
    },
    "riskLevel": "high"
  }
}
```

### 3.4 MQTT主题规范

#### 3.4.1 传感器数据主题
- **位置数据**：`sensors/position/{targetType}/{targetId}`
- **身份数据**：`sensors/identity/{targetType}/{targetId}`
- **运动数据**：`sensors/motion/{targetType}/{targetId}`
- **噪声数据**：`sensors/noise/{sensorId}`
- **扬尘数据**：`sensors/dust/{sensorId}`

#### 3.4.2 事件通知主题
- **安全事件**：`events/safety/{sourceId}`
- **状态事件**：`events/status/{sourceId}`
- **操作事件**：`events/operation/{sourceId}`

#### 3.4.3 系统状态主题
- **系统状态**：`system/status/{componentId}`
- **连接状态**：`system/connection/{systemId}`

### 3.5 实现优先级

#### 第一阶段：基础架构
1. IoTSystem单例实现
2. 传感器基类实现
3. MQTT基础连接

#### 第二阶段：核心模拟器
1. 工人模拟器 + 位置/身份传感器
2. 车辆模拟器 + 运动传感器
3. 基础门禁系统

#### 第三阶段：扩展功能
1. 设备模拟器（塔吊、升降机）
2. 环境模拟器（噪声、扬尘）
3. 物料管理系统




