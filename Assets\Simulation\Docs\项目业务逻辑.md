# 建筑工地数字孪生模拟沙盘 - 项目业务逻辑规划

## 1. 项目概述

### 1.1 业务目标
本项目旨在构建一个高度仿真的建筑工地数字孪生模拟系统，通过模拟真实工地的各种业务场景，生成高质量的测试数据，为后续的数字孪生系统提供数据基础和功能验证。

### 1.2 核心价值
- **数据驱动**: 生成结构化的工地运营数据
- **场景仿真**: 模拟真实工地的复杂业务场景
- **风险预警**: 模拟安全违规和异常情况
- **效率优化**: 为工地管理提供数据支撑
- **系统解耦**: 模拟系统与数据采集系统完全分离
- **统一管理**: 通过物联网系统统一管理所有传感器和事件发布
- **标准接口**: 基于MQTT协议的标准化数据传输

## 2. 业务架构设计

### 2.1 整体业务架构

```mermaid
graph TB
    subgraph "模拟系统层"
        SM[SimulationManager<br/>模拟管理器]
    end

    subgraph "业务模拟层"
        WS[WorkerSimulator<br/>人员模拟器]
        VS[VehicleSimulator<br/>车辆模拟器]
        ES[EquipmentSimulator<br/>设备模拟器]
        ENS[EnvironmentSimulator<br/>环境模拟器]
        AS[AccessSimulator<br/>门禁模拟器]
        MS[MaterialSimulator<br/>物料模拟器]
    end

    subgraph "传感器组件层"
        PT[PositionTracker<br/>位置追踪传感器]
        IS[IdentitySensor<br/>身份识别传感器]
        MOS[MotionSensor<br/>运动状态传感器]
        NS[NoiseSensor<br/>噪声传感器]
        DS[DustSensor<br/>扬尘传感器]
    end

    subgraph "物联网系统层"
        IOT[IoTSystem.Instance<br/>物联网系统单例]
        EM[EventManager<br/>事件管理]
    end

    subgraph "数据传输层"
        MC[BestMQTTClient<br/>BestMQTT客户端]
        MB[MQTTBroker<br/>MQTT代理]
    end

    subgraph "数字孪生系统"
        DTS[DigitalTwinSystem<br/>数字孪生系统]
    end

    SM --> WS
    SM --> VS
    SM --> ES
    SM --> ENS
    SM --> AS
    SM --> MS

    WS -.-> PT
    VS -.-> PT
    ES -.-> MOS
    ENS -.-> NS
    ENS -.-> DS
    AS -.-> IS
    MS -.-> PT

    PT --> IOT
    IS --> IOT
    MOS --> IOT
    NS --> IOT
    DS --> IOT

    IOT --> MC
    MC --> MB
    MB --> DTS
```

### 2.2 模块间关系

```mermaid
graph LR
    subgraph "模拟系统"
        WORKER[人员模拟器]
        VEHICLE[车辆模拟器]
        EQUIPMENT[设备模拟器]
        ENV[环境模拟器]
        ACCESS[门禁模拟器]
        MATERIAL[物料模拟器]
    end

    subgraph "传感器组件"
        PT[位置追踪传感器]
        IS[身份识别传感器]
        MOS[运动状态传感器]
        NS[噪声传感器]
        DS[扬尘传感器]
    end

    subgraph "物联网系统"
        IOT[IoTSystem.Instance<br/>物联网系统单例]
    end

    subgraph "数据传输"
        MQTT[BestMQTT客户端]
        BROKER[MQTT代理]
    end

    subgraph "外部系统"
        DTS[数字孪生系统]
    end

    WORKER --> PT
    WORKER --> IS
    VEHICLE --> PT
    VEHICLE --> IS
    VEHICLE --> MOS
    EQUIPMENT --> MOS
    ENV --> ES
    ACCESS --> IS
    MATERIAL --> PT

    PT --> IOT
    IS --> IOT
    MOS --> IOT
    NS --> IOT
    DS --> IOT

    IOT --> MQTT
    MQTT --> BROKER
    BROKER --> DTS
```

### 2.3 传感器数据映射关系

```mermaid
graph TB
    subgraph "模拟对象数据"
        PO[Person Objects<br/>人员对象]
        VO[Vehicle Objects<br/>车辆对象]
        EO[Equipment Objects<br/>设备对象]
        NVO[Noise Values<br/>噪声数值]
        DVO[Dust Values<br/>扬尘数值]
    end

    subgraph "传感器获取方式"
        TP[Transform.position<br/>位置获取]
        SF[Serialized Fields<br/>序列化字段]
        CS[Calculate Speed<br/>速度计算]
        SF2[Simulator Functions<br/>模拟器数据获取函数调用]
    end

    subgraph "传感器数据"
        PD[Position Data<br/>位置数据]
        ID[Identity Data<br/>身份数据]
        MD[Motion Data<br/>运动数据]
        ND[Noise Data<br/>噪声数据]
        DD[Dust Data<br/>扬尘数据]
    end

    subgraph "MQTT主题"
        PT[sensors/position/*]
        IT[sensors/identity/*]
        MT[sensors/motion/*]
        NT[sensors/noise/*]
        DT[sensors/dust/*]
    end

    PO --> TP
    VO --> TP
    EO --> TP
    PO --> SF
    VO --> SF
    VO --> CS
    EO --> CS
    NVO --> SF2
    DVO --> SF2

    TP --> PD
    SF --> ID
    CS --> MD
    SF2 --> ND
    SF2 --> DD

    PD --> PT
    ID --> IT
    MD --> MT
    ND --> NT
    DD --> DT
```

## 3. 核心业务模块

### 3.0 传感器组件模块

#### 业务职责
- 在Update函数中直接获取目标对象数据，生成对应的传感器数据
- 向物联网系统单例上报标准化的传感器数据
- 通过Unity Inspector面板进行传感器配置
- 支持运行时动态添加和移除传感器组件

#### 传感器类型配置

**通用数据发送开关配置**：
所有传感器类型都支持以下通用数据发送开关，通过Unity Inspector面板进行配置：
- **enableDataTransmission**: 数据传输总开关
- **enableTimestampData**: 时间戳数据发送开关
- **enableTargetInfo**: 目标信息发送开关（targetType、targetId）
- **enableSensorMetadata**: 传感器元数据发送开关

- **位置追踪传感器**:
  - 通过transform.position和transform.rotation获取人员、车辆、设备的位置和旋转信息
  - 支持通过Unity Inspector面板独立配置xyz轴位置和旋转数据的采集开关
  - **专用配置选项**：
    - enablePositionData: 位置数据总开关
    - enablePositionX/Y/Z: 各轴位置数据开关
    - enableRotationData: 旋转数据总开关
    - enableRotationX/Y/Z: 各轴旋转数据开关
    - enableZoneData: 区域信息发送开关
  - 生成标准化的位置和旋转数据
  - 通过序列化字段配置

- **身份识别传感器**:
  - 通过序列化字段直接读取人员和车辆的身份信息
  - **专用配置选项**：
    - enableIdentityData: 身份信息总开关
    - enableNameData: 姓名信息发送开关
    - enableRoleData: 角色信息发送开关
    - enableDepartmentData: 部门信息发送开关
    - enableCertificationData: 认证信息发送开关
    - enableAttributesData: 属性信息总开关
    - enableVehicleTypeData: 车辆类型发送开关
    - enableLicensePlateData: 车牌号发送开关
    - enableCapacityData: 载重信息发送开关
  - 生成身份信息和属性数据
  - 通过序列化字段配置

- **运动状态传感器**:
  - 通过transform.position计算车辆速度和设备工作状态
  - **专用配置选项**：
    - enableMotionData: 运动数据总开关
    - enableSpeedData: 速度数据发送开关
    - enableDirectionData: 方向数据发送开关
    - enableAccelerationData: 加速度数据发送开关
    - enableWorkStatusData: 工作状态总开关
    - enableStatusData: 状态信息发送开关
    - enableLoadData: 负载信息发送开关
    - enableOperationData: 操作信息发送开关
  - 生成运动参数和工作状态数据
  - 通过序列化字段配置

- **噪声传感器**:
  - 调用模拟器数据获取函数（如GetNoiseValue()）获取噪声数据
  - **专用配置选项**：
    - enablePositionData: 传感器位置信息发送开关
    - enableNoiseData: 噪声数据总开关
    - enableNoiseLevelData: 噪声等级发送开关
    - enableNoiseUnitData: 噪声单位发送开关
  - 生成噪声监测数据
  - 通过序列化字段配置

- **扬尘传感器**:
  - 调用模拟器数据获取函数（如GetDustValue()）获取扬尘数据
  - **专用配置选项**：
    - enablePositionData: 传感器位置信息发送开关
    - enableDustData: 扬尘数据总开关
    - enablePM25Data: PM2.5数据发送开关
    - enablePM10Data: PM10数据发送开关
    - enableDustUnitData: 扬尘单位发送开关
  - 生成扬尘监测数据
  - 通过序列化字段配置

#### 关键数据输出
- 标准化的传感器数据格式
- 实时传感器状态信息

### 3.1 人员管理模块（模拟系统）

#### 业务职责
- 模拟工人的日常行为和工作状态
- 内置随机安全违规事件生成逻辑
- 记录人员活动轨迹和工作效率数据

#### 随机违规事件配置
- **触发频率**: 每个工人每5-15分钟检查一次
- **违规类型**:
  - 未佩戴安全帽
  - 未穿防护服
  - 工作时使用手机
  - 进入危险区域
  - 未佩戴安全带
  - 违规操作设备
  - 其他违规行为
- **分布概率**: 待实现时根据真实工地数据确定

#### 关键数据输出
- 人员出勤统计
- 随机安全违规事件记录
- 工作状态数据
- 人员轨迹数据
- 违规事件通知消息

### 3.2 车辆管理模块（模拟系统）

#### 业务职责
- 管理车辆进出工地的完整流程
- 模拟运输作业和装卸过程
- 内置随机交通违规事件生成逻辑

#### 随机违规事件配置
- **触发频率**: 每辆车每10-30分钟检查一次
- **违规类型**:
  - 超速行驶
  - 超载运输
  - 违规路线
  - 违规停车
  - 其他违规行为
- **分布概率**: 待实现时根据真实交通数据确定

#### 关键数据输出
- 车辆通行记录
- 货物运输统计
- 装卸效率数据
- 交通违规事件记录
- 违规事件通知消息

### 3.3 设备管理模块（模拟系统）

#### 业务职责
- 模拟塔吊、升降机等大型设备运行
- 模拟摄像头监控系统运行
- 内置随机设备故障事件生成逻辑
- 监控设备安全和效率指标

#### 随机故障事件配置
- **触发频率**: 每台设备每30-120分钟检查一次
- **故障类型**:
  - 机械故障
  - 电气故障
  - 负载超限
  - 紧急停机
  - 摄像头故障
- **分布概率**: 待实现时根据真实设备故障统计数据确定

#### 摄像头监控功能
- **监控范围**: 工地关键区域和设备作业区
- **数据采集**: 模拟视频流数据生成
- **异常检测**: 基于规则的异常行为识别
- **数据输出**: 监控画面状态、异常事件记录

#### 关键数据输出
- 设备运行时长
- 作业效率统计
- 随机故障和维护记录
- 设备状态数据
- 摄像头监控数据
- 视频异常事件记录
- 故障事件通知消息

### 3.4 物联网系统模块

#### 业务职责
- 作为单例模式提供全局访问的物联网系统服务
- 统一管理所有传感器组件的注册和配置
- 收集来自各个传感器组件的数据
- 统一管理和发布来自各个模拟器的事件通知
- 对传感器数据和事件进行预处理、验证和格式化
- 通过MQTT协议统一发布数据和事件到外部系统

#### 核心功能
- **单例管理**：
  - 提供IoTSystem.Instance全局访问点
  - 确保系统中只有一个物联网系统实例

- **传感器管理**：
  - 传感器组件注册和注销
  - 传感器运行状态监控

- **数据收集**：
  - 接收传感器组件上报的数据
  - 同步处理数据收集请求

- **事件管理**：
  - 接收和处理来自各模拟器的事件通知
  - 同步处理事件发布请求

- **数据处理**：
  - 数据格式标准化
  - 事件格式标准化
  - 数据批量处理
  - 数据压缩和优化

- **数据传输**：
  - MQTT连接管理
  - 数据发布调度
  - 事件发布调度

#### 关键数据输出
- 统一格式的MQTT消息
- 统一格式的事件通知消息
- 传感器系统状态报告
- 事件发布统计报告

### 3.5 环境模拟模块（模拟系统）

#### 业务职责
- 监听其他模拟器的活动事件
- 基于事件计算噪声和扬尘数据
- 模拟噪声扬尘在工地环境下的传播和影响

#### 环境计算配置
- **噪声计算模型**:
  - 设备运行基础噪声值
  - 车辆运输噪声贡献
  - 距离衰减算法 (反平方定律)
  - 环境噪声叠加计算

- **扬尘计算模型**:
  - 施工活动扬尘系数
  - 车辆行驶扬尘贡献
  - 风速风向影响算法
  - 扬尘沉降和扩散模拟

- **环境阈值**:
  - 噪声限值: 待设定 (如70dB白天/55dB夜间)
  - 扬尘限值: 待设定 (如PM10/PM2.5标准)

#### 关键数据输出
- 实时噪声分布数据
- 实时扬尘浓度数据
- 环境影响评估报告
- 超标预警通知
- 环境质量趋势分析

## 4. 数据流设计

### 4.1 数据生成流程

```mermaid
flowchart LR
    subgraph "模拟系统"
        SIM[模拟器运行]
        OBJECTS[模拟对象]
        ENV[环境模拟器]
    end

    subgraph "传感器组件"
        UPDATE[Update函数]
        GETDATA[获取数据]
        GENERATE[数据生成]
        VALIDATE[数据验证]
        REPORT[上报数据]
    end

    subgraph "物联网系统"
        IOT[物联网系统]
        COLLECT[数据收集]
        PROCESS_IOT[数据处理]
        BATCH[批量处理]
    end

    subgraph "数据传输"
        MQTT[BestMQTT客户端]
        BUFFER[数据缓冲]
        PUBLISH[消息发布]
    end

    subgraph "消息代理"
        BROKER[MQTT代理]
        TOPIC[主题路由]
    end

    subgraph "数字孪生系统"
        SUBSCRIBE[消息订阅]
        PROCESS[数据处理]
        STORAGE[数据存储]
    end

    SIM --> OBJECTS
    SIM --> ENV
    
    UPDATE --> GETDATA
    GETDATA -.-> OBJECTS
    GETDATA -.-> ENV
    GETDATA --> GENERATE
    GENERATE --> VALIDATE
    VALIDATE --> REPORT
    REPORT --> IOT

    IOT --> COLLECT
    COLLECT --> PROCESS_IOT
    PROCESS_IOT --> BATCH
    BATCH --> MQTT

    MQTT --> BUFFER
    BUFFER --> PUBLISH
    PUBLISH --> BROKER

    BROKER --> TOPIC
    TOPIC --> SUBSCRIBE
    SUBSCRIBE --> PROCESS
    PROCESS --> STORAGE
```

### 4.1.1 数据和事件处理流程

```mermaid
flowchart TD
    A[传感器数据生成] --> B[数据验证]
    B --> C{验证通过?}
    C -->|是| D[上报给物联网系统]
    C -->|否| E[丢弃数据]
    
    M[模拟器事件生成] --> N[事件验证]
    N --> O{验证通过?}
    O -->|是| P[发布给物联网系统]
    O -->|否| Q[丢弃事件]
    
    D --> F[物联网系统]
    P --> F
    F --> G[数据收集]
    F --> R[事件管理]
    G --> H[数据处理]
    R --> H
    H --> I[批量处理]
    I --> J[BestMQTT客户端]
    J --> K[发送到MQTT服务器]
    K --> L[数字孪生系统接收]
```

### 4.2 事件订阅功能

#### 4.2.1 事件类型分类
- **安全事件**：工人安全违规、设备安全警报、危险区域进入等
- **状态事件**：设备状态变化、系统状态更新、连接状态变化等
- **操作事件**：用户操作记录、系统操作日志、配置变更等
- **异常事件**：设备故障报警、系统异常、通信异常等

#### 4.2.2 事件发布流程
1. **事件触发**：模拟器检测到特定条件或状态变化
2. **事件生成**：创建标准化的事件数据结构
3. **事件验证**：验证事件数据的完整性和有效性
4. **事件发布**：模拟器通过IoTSystem.Instance.SendIOTEvent方法发布事件
5. **事件处理**：物联网系统单例接收并处理事件
6. **MQTT发布**：将事件通过MQTT协议发布到指定主题
7. **数字孪生接收**：数字孪生系统订阅并接收事件通知

#### 4.2.3 事件数据结构
- **事件ID**：唯一标识符
- **事件类型**：安全、状态、操作、异常
- **源ID**：事件来源的组件或模拟器ID
- **源类型**：工人、车辆、设备、环境
- **时间戳**：事件发生的精确时间
- **严重程度**：低、中、高、严重
- **事件标题**：简短的事件描述
- **事件描述**：详细的事件说明
- **事件数据**：与事件相关的具体数据

#### 4.2.4 MQTT主题规范
- **安全事件**：`events/safety/{sourceId}`
- **状态事件**：`events/status/{sourceId}`
- **操作事件**：`events/operation/{sourceId}`
- **异常事件**：`events/exception/{sourceId}`

### 4.3 数据分类体系

#### 模拟系统数据
- **模拟对象**: 人员对象、车辆对象、设备对象、环境数值
- **业务状态**: 工作状态、违规事件、故障事件、门禁事件
- **内部数据**: 模拟器配置、运行状态

#### 传感器数据
- **位置数据**: 人员位置、车辆位置、设备位置
- **身份数据**: 人员身份、车辆信息、设备信息
- **运动数据**: 车辆速度、设备工作状态、运动参数
- **噪声数据**: 噪声等级、噪声单位
- **扬尘数据**: PM2.5、PM10、扬尘浓度

#### MQTT主题分类
- **位置主题**: `sensors/position/{targetType}/{targetId}`
- **身份主题**: `sensors/identity/{targetType}/{targetId}`
- **运动主题**: `sensors/motion/{targetType}/{targetId}`
- **噪声主题**: `sensors/noise/{sensorId}`
- **扬尘主题**: `sensors/dust/{sensorId}`
- **系统主题**: `system/status/{componentId}`

#### 数据质量等级
- **QoS 0**: 实时位置数据、噪声监测数据、扬尘监测数据
- **QoS 1**: 身份识别数据、运动状态数据
- **QoS 2**: 关键事件数据、系统状态数据

### 4.3 数据传输规范

#### MQTT底层实现
- **MQTT客户端**: 使用BestMQTT插件作为底层MQTT通信实现
- **技术优势**:
  - 高性能的MQTT 3.1.1和5.0协议支持
  - 完整的Unity集成和跨平台兼容性
  - 内置连接管理和自动重连机制
  - 支持TLS/SSL加密连接
  - 优化的内存管理和性能表现
  - 支持消息持久化和离线缓存

#### 消息格式标准
- **时间戳**: ISO 8601格式的UTC时间
- **数据编码**: UTF-8编码的JSON格式，支持BestMQTT的高效序列化
- **消息大小**: 单条消息不超过1MB
- **批量传输**: 支持批量数据打包传输
- **协议版本**: 支持MQTT 3.1.1和5.0协议

*本文档将随着项目开发进展持续更新和完善*
