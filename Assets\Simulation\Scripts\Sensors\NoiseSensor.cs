using System;
using System.Threading;
using Cysharp.Threading.Tasks;
using UnityEngine;
using Simulation.Data;
using Simulation.Simulators;
using Simulation.IoT;

namespace Simulation.Sensors
{
    /// <summary>
    /// 噪声传感器
    /// 监测环境噪声等级，从环境监测模拟器获取数据
    /// </summary>
    public class NoiseSensor : SensorBase
    {
        [Header("噪声传感器配置")]
        [SerializeField] private string noiseUnit = "dB"; // 噪声单位
        [SerializeField] private float minNoiseLevel = 30f; // 最小噪声等级
        [SerializeField] private float maxNoiseLevel = 120f; // 最大噪声等级
        [SerializeField] private float noiseThreshold = 70f; // 噪声阈值
        [SerializeField] private bool enableThresholdAlert = true; // 启用阈值报警
        
        [Header("数据源配置")]
        [SerializeField] private EnvironmentalMonitorSimulator environmentalMonitor; // 环境监测器引用
        [SerializeField] private bool autoFindMonitor = true; // 自动查找监测器
        [SerializeField] private float detectionRadius = 50f; // 检测半径
        
        [Header("数据处理配置")]
        [SerializeField] private bool enableSmoothing = true; // 启用数据平滑
        [SerializeField] private float smoothingFactor = 0.3f; // 平滑因子
        [SerializeField] private bool enableNoiseVariation = true; // 启用噪声变化
        [SerializeField] private float variationRange = 2f; // 变化范围
        
        [Header("校准配置")]
        [SerializeField] private bool enableCalibration = true; // 启用校准
        [SerializeField] private float calibrationOffset = 0f; // 校准偏移
        [SerializeField] private float calibrationMultiplier = 1f; // 校准倍数
        
        [Header("调试配置")]
        [SerializeField] private bool showDebugInfo = true; // 显示调试信息
        [SerializeField] private bool logThresholdExceeds = true; // 记录超阈值事件
        
        // 传感器状态
        private float currentNoiseLevel = 0f;
        private float smoothedNoiseLevel = 0f;
        private float lastRawValue = 0f;
        private DateTime lastThresholdExceedTime = DateTime.MinValue;
        private bool isThresholdExceeded = false;
        
        // 统计数据
        private float maxRecordedNoise = 0f;
        private float minRecordedNoise = float.MaxValue;
        private float averageNoiseLevel = 0f;
        private int totalMeasurements = 0;
        private int thresholdExceedCount = 0;
        
        // 公共属性
        public override string SensorType => "NoiseSensor";
        public float CurrentNoiseLevel => currentNoiseLevel;
        public float SmoothedNoiseLevel => smoothedNoiseLevel;
        public string NoiseUnit => noiseUnit;
        public bool IsThresholdExceeded => isThresholdExceeded;
        public float MaxRecordedNoise => maxRecordedNoise;
        public float MinRecordedNoise => minRecordedNoise;
        public float AverageNoiseLevel => averageNoiseLevel;
        public int ThresholdExceedCount => thresholdExceedCount;
        
        protected override void Awake()
        {
            base.Awake();
            
            // 自动查找环境监测器
            if (autoFindMonitor && environmentalMonitor == null)
            {
                FindEnvironmentalMonitor();
            }
        }
        
        protected override void OnSensorStarted()
        {
            LogDebug("噪声传感器启动");
            
            // 验证配置
            if (!ValidateNoiseConfiguration())
            {
                LogError("噪声传感器配置验证失败");
                return;
            }
            
            // 初始化数据
            ResetStatistics();
        }
        
        protected override void OnSensorStopped()
        {
            LogDebug($"噪声传感器停止 - 总测量次数: {totalMeasurements}, 超阈值次数: {thresholdExceedCount}");
        }
        
        protected override async UniTask<object> GenerateSensorDataAsync(CancellationToken cancellationToken)
        {
            try
            {
                // 获取原始噪声数据
                float rawNoiseLevel = GetRawNoiseLevel();
                
                // 应用校准
                if (enableCalibration)
                {
                    rawNoiseLevel = ApplyCalibration(rawNoiseLevel);
                }
                
                // 添加随机变化
                if (enableNoiseVariation)
                {
                    rawNoiseLevel += UnityEngine.Random.Range(-variationRange, variationRange);
                }
                
                // 限制范围
                rawNoiseLevel = Mathf.Clamp(rawNoiseLevel, minNoiseLevel, maxNoiseLevel);
                
                // 应用平滑
                if (enableSmoothing)
                {
                    smoothedNoiseLevel = ApplySmoothing(rawNoiseLevel);
                    currentNoiseLevel = smoothedNoiseLevel;
                }
                else
                {
                    currentNoiseLevel = rawNoiseLevel;
                }
                
                // 更新统计
                UpdateStatistics(currentNoiseLevel);
                
                // 检查阈值
                CheckThreshold(currentNoiseLevel);
                
                // 创建传感器数据
                var noiseData = new NoiseSensorData
                {
                    timestamp = DateTime.UtcNow,
                    sensorId = sensorId,
                    deviceId = deviceId,
                    noiseLevel = currentNoiseLevel,
                    unit = noiseUnit,
                    isThresholdExceeded = isThresholdExceeded,
                    threshold = noiseThreshold,
                    location = new Vector3
                    {
                        x = transform.position.x,
                        y = transform.position.y,
                        z = transform.position.z
                    }
                };
                
                LogDebug($"噪声数据: {currentNoiseLevel:F1}{noiseUnit}");
                
                return noiseData;
            }
            catch (Exception ex)
            {
                LogError($"生成噪声数据异常: {ex.Message}");
                return null;
            }
        }
        
        /// <summary>
        /// 获取原始噪声等级
        /// </summary>
        private float GetRawNoiseLevel()
        {
            // 优先从环境监测器获取数据
            if (environmentalMonitor != null && environmentalMonitor.isActiveAndEnabled)
            {
                return environmentalMonitor.CurrentNoiseLevel;
            }
            
            // 如果没有环境监测器，生成模拟数据
            return GenerateSimulatedNoiseLevel();
        }
        
        /// <summary>
        /// 生成模拟噪声等级
        /// </summary>
        private float GenerateSimulatedNoiseLevel()
        {
            // 基础噪声等级
            float baseNoise = 45f;
            
            // 时间变化（模拟一天中的噪声变化）
            float timeOfDay = (Time.time % 86400f) / 86400f; // 0-1之间
            float timeVariation = Mathf.Sin(timeOfDay * Mathf.PI * 2f) * 5f;
            
            // 随机变化
            float randomVariation = UnityEngine.Random.Range(-3f, 8f);
            
            // 检测附近的噪声源
            float sourceInfluence = DetectNearbyNoiseSources();
            
            return baseNoise + timeVariation + randomVariation + sourceInfluence;
        }
        
        /// <summary>
        /// 检测附近的噪声源
        /// </summary>
        private float DetectNearbyNoiseSources()
        {
            float totalInfluence = 0f;
            
            Collider[] nearbyObjects = Physics.OverlapSphere(transform.position, detectionRadius);
            
            foreach (var collider in nearbyObjects)
            {
                float influence = 0f;
                float distance = Vector3.Distance(transform.position, collider.transform.position);
                float distanceFactor = Mathf.Max(0f, 1f - distance / detectionRadius);
                
                // 检查塔吊
                var craneSimulator = collider.GetComponent<CraneSimulator>();
                if (craneSimulator != null && craneSimulator.CurrentState != CraneState.Idle)
                {
                    influence = 15f * distanceFactor;
                }
                
                // 检查车辆
                var vehicleSimulator = collider.GetComponent<VehicleSimulator>();
                if (vehicleSimulator != null && vehicleSimulator.CurrentSpeed > 0.1f)
                {
                    influence = 10f * distanceFactor;
                }
                
                // 检查升降机
                var elevatorSimulator = collider.GetComponent<ElevatorSimulator>();
                if (elevatorSimulator != null && elevatorSimulator.CurrentState != ElevatorState.Idle)
                {
                    influence = 8f * distanceFactor;
                }
                
                totalInfluence += influence;
            }
            
            return totalInfluence;
        }
        
        /// <summary>
        /// 应用校准
        /// </summary>
        private float ApplyCalibration(float rawValue)
        {
            return (rawValue * calibrationMultiplier) + calibrationOffset;
        }
        
        /// <summary>
        /// 应用平滑
        /// </summary>
        private float ApplySmoothing(float newValue)
        {
            if (totalMeasurements == 0)
            {
                return newValue;
            }
            
            return Mathf.Lerp(smoothedNoiseLevel, newValue, smoothingFactor);
        }
        
        /// <summary>
        /// 更新统计数据
        /// </summary>
        private void UpdateStatistics(float noiseLevel)
        {
            totalMeasurements++;
            
            maxRecordedNoise = Mathf.Max(maxRecordedNoise, noiseLevel);
            minRecordedNoise = Mathf.Min(minRecordedNoise, noiseLevel);
            
            // 计算平均值
            averageNoiseLevel = ((averageNoiseLevel * (totalMeasurements - 1)) + noiseLevel) / totalMeasurements;
        }
        
        /// <summary>
        /// 检查阈值
        /// </summary>
        private void CheckThreshold(float noiseLevel)
        {
            bool wasExceeded = isThresholdExceeded;
            isThresholdExceeded = noiseLevel > noiseThreshold;
            
            if (isThresholdExceeded && !wasExceeded)
            {
                // 刚刚超过阈值
                thresholdExceedCount++;
                lastThresholdExceedTime = DateTime.UtcNow;
                
                if (logThresholdExceeds)
                {
                    LogDebug($"噪声超过阈值: {noiseLevel:F1}{noiseUnit} > {noiseThreshold}{noiseUnit}");
                }
                
                if (enableThresholdAlert)
                {
                    TriggerThresholdAlert(noiseLevel);
                }
            }
        }
        
        /// <summary>
        /// 触发阈值报警
        /// </summary>
        private void TriggerThresholdAlert(float noiseLevel)
        {
            // 发送阈值超标事件到IoT系统
            var alertData = new
            {
                sensorId = sensorId,
                sensorType = SensorType,
                alertType = "threshold_exceeded",
                currentValue = noiseLevel,
                threshold = noiseThreshold,
                unit = noiseUnit,
                timestamp = DateTime.UtcNow,
                location = new { x = transform.position.x, y = transform.position.y, z = transform.position.z }
            };
            
            IoTSystem.Instance?.SendIOTEvent(
                sensorId, 
                "noise_threshold_alert", 
                "噪声阈值报警", 
                $"噪声传感器 {sensorId} 检测到噪声 {noiseLevel:F1}{noiseUnit} 超过阈值 {noiseThreshold}{noiseUnit}",
                alertData
            );
        }
        
        /// <summary>
        /// 查找环境监测器
        /// </summary>
        private void FindEnvironmentalMonitor()
        {
            // 在检测半径内查找环境监测器
            Collider[] colliders = Physics.OverlapSphere(transform.position, detectionRadius);
            
            foreach (var collider in colliders)
            {
                var monitor = collider.GetComponent<EnvironmentalMonitorSimulator>();
                if (monitor != null)
                {
                    environmentalMonitor = monitor;
                    LogDebug($"找到环境监测器: {monitor.StationId}");
                    break;
                }
            }
            
            // 如果没找到，尝试在场景中查找
            if (environmentalMonitor == null)
            {
                environmentalMonitor = FindObjectOfType<EnvironmentalMonitorSimulator>();
                if (environmentalMonitor != null)
                {
                    LogDebug($"在场景中找到环境监测器: {environmentalMonitor.StationId}");
                }
            }
        }
        
        /// <summary>
        /// 验证噪声配置
        /// </summary>
        private bool ValidateNoiseConfiguration()
        {
            if (minNoiseLevel >= maxNoiseLevel)
            {
                LogError("最小噪声等级必须小于最大噪声等级");
                return false;
            }
            
            if (noiseThreshold < minNoiseLevel || noiseThreshold > maxNoiseLevel)
            {
                LogError("噪声阈值必须在最小和最大噪声等级之间");
                return false;
            }
            
            if (smoothingFactor < 0f || smoothingFactor > 1f)
            {
                LogError("平滑因子必须在0-1之间");
                return false;
            }
            
            return true;
        }
        
        /// <summary>
        /// 重置统计数据
        /// </summary>
        private void ResetStatistics()
        {
            maxRecordedNoise = 0f;
            minRecordedNoise = float.MaxValue;
            averageNoiseLevel = 0f;
            totalMeasurements = 0;
            thresholdExceedCount = 0;
            smoothedNoiseLevel = 0f;
        }
        
        /// <summary>
        /// 调试日志
        /// </summary>
        private void LogDebug(string message)
        {
            if (enableDebugLog && showDebugInfo)
            {
                Debug.Log($"[NoiseSensor:{sensorId}] {message}");
            }
        }
        
        /// <summary>
        /// 错误日志
        /// </summary>
        private void LogError(string message)
        {
            Debug.LogError($"[NoiseSensor:{sensorId}] {message}");
        }
        
        /// <summary>
        /// 绘制调试信息
        /// </summary>
        private void OnDrawGizmos()
        {
            if (!showDebugInfo) return;
            
            // 绘制检测范围
            Gizmos.color = Color.yellow;
            Gizmos.DrawWireSphere(transform.position, detectionRadius);
            
            // 绘制传感器
            Color sensorColor = isThresholdExceeded ? Color.red : Color.green;
            Gizmos.color = sensorColor;
            Gizmos.DrawWireCube(transform.position, Vector3.one);
            
            // 绘制到环境监测器的连线
            if (environmentalMonitor != null)
            {
                Gizmos.color = Color.blue;
                Gizmos.DrawLine(transform.position, environmentalMonitor.transform.position);
            }
        }
    }
    
    /// <summary>
    /// 噪声传感器数据结构
    /// </summary>
    [Serializable]
    public class NoiseSensorData
    {
        public DateTime timestamp;
        public string sensorId;
        public string deviceId;
        public float noiseLevel;
        public string unit;
        public bool isThresholdExceeded;
        public float threshold;
        public Vector3 location;
    }
}
