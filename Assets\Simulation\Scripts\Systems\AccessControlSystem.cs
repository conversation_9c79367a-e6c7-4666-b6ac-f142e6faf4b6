using System;
using System.Collections.Generic;
using System.Threading;
using Cysharp.Threading.Tasks;
using UnityEngine;
using Simulation.IoTSystem;
using Simulation.Data;

namespace Simulation.Systems
{
    /// <summary>
    /// 门禁管理系统
    /// 实现简化的门禁管理，只进行身份识别和记录，不做权限验证
    /// </summary>
    public class AccessControlSystem : SimulatorBase
    {
        [Header("门禁基础配置")]
        [SerializeField] private string gateId = "main_gate";
        [SerializeField] private string gateName = "主门禁";
        [SerializeField] private string gateLocation = "工地入口";
        [SerializeField] private AccessType supportedAccessTypes = AccessType.Card | AccessType.QRCode;
        
        [Header("检测配置")]
        [SerializeField] private float detectionRange = 3f; // 检测范围（米）
        [SerializeField] private LayerMask detectionLayers = -1; // 检测层
        [SerializeField] private float accessProcessTime = 2f; // 门禁处理时间（秒）
        [SerializeField] private bool requireStopForAccess = true; // 是否需要停车识别
        
        [Header("识别配置")]
        [SerializeField] private bool enableCardReader = true; // 启用刷卡识别
        [SerializeField] private bool enableQRScanner = true; // 启用二维码扫描
        [SerializeField] private bool enableFaceRecognition = false; // 启用人脸识别
        [SerializeField] private float identificationTimeout = 10f; // 识别超时时间（秒）
        
        [Header("记录配置")]
        [SerializeField] private bool recordAllAttempts = true; // 记录所有尝试
        [SerializeField] private bool enableDuplicateCheck = true; // 启用重复检查
        [SerializeField] private float duplicateTimeWindow = 30f; // 重复检查时间窗口（秒）
        
        [Header("统计配置")]
        [SerializeField] private bool enableDailyStatistics = true; // 启用日统计
        [SerializeField] private int maxDailyRecords = 1000; // 最大日记录数
        
        [Header("调试配置")]
        [SerializeField] private bool enableDebugLog = true;
        [SerializeField] private bool showDetectionGizmos = true;
        [SerializeField] private Color detectionColor = Color.green;
        
        // 门禁状态
        private AccessGateState currentState = AccessGateState.Idle;
        private Dictionary<string, AccessRecord> recentAccess = new Dictionary<string, AccessRecord>();
        private Queue<AccessAttempt> pendingAttempts = new Queue<AccessAttempt>();
        
        // 统计数据
        private int dailyEntryCount = 0;
        private int dailyExitCount = 0;
        private int totalAccessAttempts = 0;
        private int successfulAccess = 0;
        private DateTime lastResetTime = DateTime.Today;
        
        // 公共属性
        public string GateId => gateId;
        public string GateName => gateName;
        public AccessGateState CurrentState => currentState;
        public int DailyEntryCount => dailyEntryCount;
        public int DailyExitCount => dailyExitCount;
        
        protected override async UniTask OnStartSimulationAsync(CancellationToken cancellationToken)
        {
            LogDebug("门禁系统启动");
            
            // 初始化状态
            currentState = AccessGateState.Idle;
            recentAccess.Clear();
            
            // 重置日统计
            ResetDailyStatistics();
            
            // 发送系统启动事件
            SendSystemEvent("gate_system_start", "门禁系统启动", $"门禁 {gateName} 系统启动");
            
            // 启动检测循环
            DetectionLoop(cancellationToken).Forget();
            
            // 启动处理循环
            ProcessingLoop(cancellationToken).Forget();
        }
        
        protected override void OnSimulationUpdate(float deltaTime)
        {
            // 检查是否需要重置日统计
            if (enableDailyStatistics && DateTime.Today > lastResetTime)
            {
                ResetDailyStatistics();
            }
            
            // 清理过期的重复检查记录
            CleanupExpiredRecords();
        }
        
        protected override async UniTask OnStopSimulationAsync()
        {
            LogDebug("门禁系统停止");
            
            // 发送系统停止事件
            SendSystemEvent("gate_system_stop", "门禁系统停止", 
                $"门禁 {gateName} 系统停止，今日进入{dailyEntryCount}人次，离开{dailyExitCount}人次");
        }
        
        /// <summary>
        /// 检测循环
        /// </summary>
        private async UniTaskVoid DetectionLoop(CancellationToken cancellationToken)
        {
            try
            {
                while (!cancellationToken.IsCancellationRequested)
                {
                    DetectNearbyTargets();
                    await UniTask.Delay(TimeSpan.FromSeconds(0.5f), cancellationToken: cancellationToken);
                }
            }
            catch (OperationCanceledException)
            {
                LogDebug("检测循环被取消");
            }
        }
        
        /// <summary>
        /// 处理循环
        /// </summary>
        private async UniTaskVoid ProcessingLoop(CancellationToken cancellationToken)
        {
            try
            {
                while (!cancellationToken.IsCancellationRequested)
                {
                    if (pendingAttempts.Count > 0 && currentState == AccessGateState.Idle)
                    {
                        var attempt = pendingAttempts.Dequeue();
                        await ProcessAccessAttempt(attempt, cancellationToken);
                    }
                    
                    await UniTask.Delay(TimeSpan.FromSeconds(0.1f), cancellationToken: cancellationToken);
                }
            }
            catch (OperationCanceledException)
            {
                LogDebug("处理循环被取消");
            }
        }
        
        /// <summary>
        /// 检测附近目标
        /// </summary>
        private void DetectNearbyTargets()
        {
            Collider[] colliders = Physics.OverlapSphere(transform.position, detectionRange, detectionLayers);
            
            foreach (var collider in colliders)
            {
                // 检查是否是有效的访问目标
                if (IsValidAccessTarget(collider))
                {
                    CreateAccessAttempt(collider);
                }
            }
        }
        
        /// <summary>
        /// 检查是否是有效的访问目标
        /// </summary>
        private bool IsValidAccessTarget(Collider collider)
        {
            // 检查工人
            var workerSimulator = collider.GetComponent<WorkerSimulator>();
            if (workerSimulator != null)
            {
                return true;
            }
            
            // 检查车辆
            var vehicleSimulator = collider.GetComponent<VehicleSimulator>();
            if (vehicleSimulator != null)
            {
                return true;
            }
            
            // 检查标签
            if (collider.CompareTag("Worker") || collider.CompareTag("Vehicle"))
            {
                return true;
            }
            
            return false;
        }
        
        /// <summary>
        /// 创建访问尝试
        /// </summary>
        private void CreateAccessAttempt(Collider target)
        {
            string targetId = GetTargetId(target);
            
            // 检查重复
            if (enableDuplicateCheck && IsRecentAccess(targetId))
            {
                return;
            }
            
            var attempt = new AccessAttempt
            {
                targetId = targetId,
                targetType = GetTargetType(target),
                targetName = GetTargetName(target),
                cardId = GetCardId(target),
                attemptTime = DateTime.UtcNow,
                position = target.transform.position,
                accessType = DetermineAccessType(target)
            };
            
            pendingAttempts.Enqueue(attempt);
            LogDebug($"创建访问尝试: {attempt.targetType} {attempt.targetName}");
        }
        
        /// <summary>
        /// 处理访问尝试
        /// </summary>
        private async UniTask ProcessAccessAttempt(AccessAttempt attempt, CancellationToken cancellationToken)
        {
            currentState = AccessGateState.Processing;
            totalAccessAttempts++;
            
            LogDebug($"处理访问尝试: {attempt.targetType} {attempt.targetName}");
            
            try
            {
                // 模拟识别过程
                await UniTask.Delay(TimeSpan.FromSeconds(accessProcessTime), cancellationToken: cancellationToken);
                
                // 简化实现：所有识别都成功（因为不做权限验证）
                bool identificationSuccess = true;
                
                if (identificationSuccess)
                {
                    await ProcessSuccessfulAccess(attempt, cancellationToken);
                }
                else
                {
                    await ProcessFailedAccess(attempt, cancellationToken);
                }
            }
            catch (OperationCanceledException)
            {
                LogDebug("访问处理被取消");
            }
            finally
            {
                currentState = AccessGateState.Idle;
            }
        }
        
        /// <summary>
        /// 处理成功访问
        /// </summary>
        private async UniTask ProcessSuccessfulAccess(AccessAttempt attempt, CancellationToken cancellationToken)
        {
            successfulAccess++;
            
            // 确定访问方向（简化实现：根据位置判断）
            string accessDirection = DetermineAccessDirection(attempt.position);
            
            if (accessDirection == "entry")
            {
                dailyEntryCount++;
            }
            else
            {
                dailyExitCount++;
            }
            
            // 记录访问
            var accessRecord = new AccessRecord
            {
                targetId = attempt.targetId,
                lastAccessTime = attempt.attemptTime,
                accessDirection = accessDirection
            };
            
            recentAccess[attempt.targetId] = accessRecord;
            
            // 发送访问事件
            SendAccessEvent(attempt, accessDirection, true);
            
            LogDebug($"访问成功: {attempt.targetName} {accessDirection}");
        }
        
        /// <summary>
        /// 处理失败访问
        /// </summary>
        private async UniTask ProcessFailedAccess(AccessAttempt attempt, CancellationToken cancellationToken)
        {
            // 发送访问失败事件
            SendAccessEvent(attempt, "failed", false);
            
            LogDebug($"访问失败: {attempt.targetName}");
        }
        
        /// <summary>
        /// 确定访问方向
        /// </summary>
        private string DetermineAccessDirection(Vector3 position)
        {
            // 简化实现：根据相对位置判断进入或离开
            Vector3 relativePosition = transform.InverseTransformPoint(position);
            return relativePosition.z > 0 ? "entry" : "exit";
        }
        
        /// <summary>
        /// 确定访问类型
        /// </summary>
        private string DetermineAccessType(Collider target)
        {
            // 简化实现：默认使用卡片识别
            if (enableCardReader && (supportedAccessTypes & AccessType.Card) != 0)
            {
                return "card";
            }
            else if (enableQRScanner && (supportedAccessTypes & AccessType.QRCode) != 0)
            {
                return "qr_code";
            }
            else if (enableFaceRecognition && (supportedAccessTypes & AccessType.Face) != 0)
            {
                return "face";
            }
            
            return "unknown";
        }
        
        /// <summary>
        /// 获取目标ID
        /// </summary>
        private string GetTargetId(Collider target)
        {
            var workerSimulator = target.GetComponent<WorkerSimulator>();
            if (workerSimulator != null)
            {
                return workerSimulator.WorkerId;
            }
            
            var vehicleSimulator = target.GetComponent<VehicleSimulator>();
            if (vehicleSimulator != null)
            {
                return vehicleSimulator.VehicleId;
            }
            
            return target.name;
        }
        
        /// <summary>
        /// 获取目标类型
        /// </summary>
        private string GetTargetType(Collider target)
        {
            if (target.GetComponent<WorkerSimulator>() != null || target.CompareTag("Worker"))
            {
                return "person";
            }
            else if (target.GetComponent<VehicleSimulator>() != null || target.CompareTag("Vehicle"))
            {
                return "vehicle";
            }
            
            return "unknown";
        }
        
        /// <summary>
        /// 获取目标名称
        /// </summary>
        private string GetTargetName(Collider target)
        {
            var workerSimulator = target.GetComponent<WorkerSimulator>();
            if (workerSimulator != null)
            {
                return workerSimulator.WorkerName;
            }
            
            var vehicleSimulator = target.GetComponent<VehicleSimulator>();
            if (vehicleSimulator != null)
            {
                return vehicleSimulator.LicensePlate;
            }
            
            return target.name;
        }
        
        /// <summary>
        /// 获取卡片ID
        /// </summary>
        private string GetCardId(Collider target)
        {
            // 简化实现：生成基于目标ID的卡片ID
            string targetId = GetTargetId(target);
            return $"CARD_{targetId}";
        }
        
        /// <summary>
        /// 检查是否是最近访问
        /// </summary>
        private bool IsRecentAccess(string targetId)
        {
            if (recentAccess.TryGetValue(targetId, out AccessRecord record))
            {
                TimeSpan timeSinceLastAccess = DateTime.UtcNow - record.lastAccessTime;
                return timeSinceLastAccess.TotalSeconds < duplicateTimeWindow;
            }
            
            return false;
        }
        
        /// <summary>
        /// 清理过期记录
        /// </summary>
        private void CleanupExpiredRecords()
        {
            var expiredKeys = new List<string>();
            DateTime cutoffTime = DateTime.UtcNow.AddSeconds(-duplicateTimeWindow * 2);
            
            foreach (var kvp in recentAccess)
            {
                if (kvp.Value.lastAccessTime < cutoffTime)
                {
                    expiredKeys.Add(kvp.Key);
                }
            }
            
            foreach (string key in expiredKeys)
            {
                recentAccess.Remove(key);
            }
        }
        
        /// <summary>
        /// 重置日统计
        /// </summary>
        private void ResetDailyStatistics()
        {
            dailyEntryCount = 0;
            dailyExitCount = 0;
            lastResetTime = DateTime.Today;
            
            LogDebug("重置日统计数据");
        }
        
        /// <summary>
        /// 发送访问事件
        /// </summary>
        private void SendAccessEvent(AccessAttempt attempt, string accessDirection, bool success)
        {
            var accessEvent = new AccessEventData
            {
                accessType = accessDirection,
                personId = attempt.targetId,
                personName = attempt.targetName,
                cardId = attempt.cardId,
                gateId = gateId,
                gateName = gateName,
                sourceId = gateId,
                title = success ? "门禁通过" : "门禁失败",
                description = $"{attempt.targetType} {attempt.targetName} {(success ? "成功" : "失败")}{accessDirection}门禁"
            };
            
            IoTSystem.IoTSystem.Instance?.SendIOTEvent(gateId, "access_control", accessEvent.title, accessEvent.description, accessEvent);
        }
        
        /// <summary>
        /// 发送系统事件
        /// </summary>
        private void SendSystemEvent(string eventType, string title, string description)
        {
            var systemEvent = new StatusEventData
            {
                equipmentId = gateId,
                equipmentType = "access_gate",
                currentStatus = currentState.ToString(),
                sourceId = gateId,
                title = title,
                description = description
            };
            
            IoTSystem.IoTSystem.Instance?.SendIOTEvent(gateId, eventType, title, description, systemEvent);
        }
        
        /// <summary>
        /// 调试日志
        /// </summary>
        private void LogDebug(string message)
        {
            if (enableDebugLog)
            {
                Debug.Log($"[AccessControlSystem:{gateId}] {message}");
            }
        }
        
        /// <summary>
        /// 绘制调试信息
        /// </summary>
        private void OnDrawGizmos()
        {
            if (!showDetectionGizmos) return;
            
            // 绘制检测范围
            Gizmos.color = detectionColor;
            Gizmos.DrawWireSphere(transform.position, detectionRange);
            
            // 绘制门禁方向
            Gizmos.color = Color.blue;
            Gizmos.DrawRay(transform.position, transform.forward * detectionRange * 0.5f);
            
            // 绘制状态指示
            Color stateColor = currentState == AccessGateState.Idle ? Color.green : Color.red;
            Gizmos.color = stateColor;
            Gizmos.DrawWireCube(transform.position + Vector3.up * 2f, Vector3.one * 0.5f);
        }
    }
    
    /// <summary>
    /// 门禁状态枚举
    /// </summary>
    public enum AccessGateState
    {
        Idle,           // 空闲
        Processing,     // 处理中
        Maintenance     // 维护中
    }
    
    /// <summary>
    /// 访问类型标志
    /// </summary>
    [Flags]
    public enum AccessType
    {
        Card = 1,       // 刷卡
        QRCode = 2,     // 二维码
        Face = 4        // 人脸识别
    }
    
    /// <summary>
    /// 访问尝试数据
    /// </summary>
    [Serializable]
    public class AccessAttempt
    {
        public string targetId;
        public string targetType;
        public string targetName;
        public string cardId;
        public DateTime attemptTime;
        public Vector3 position;
        public string accessType;
    }
    
    /// <summary>
    /// 访问记录数据
    /// </summary>
    [Serializable]
    public class AccessRecord
    {
        public string targetId;
        public DateTime lastAccessTime;
        public string accessDirection;
    }
}
